﻿
    return fileId;
}

function ensureFileComment(content, language, fileName, filePath) {
    // ØªÙ… ØªØ¹Ø¯ÙŠÙ„ Ø§Ù„Ø¯Ø§Ù„Ø© Ù„Ø¹Ø¯Ù… Ø¥Ø¶Ø§ÙØ© ØªØ¹Ù„ÙŠÙ‚Ø§Øª Ø§Ù„Ù…Ø³Ø§Ø±
    // Ù„Ø§ Ù†Ø¶ÙŠÙ Ø£ÙŠ ØªØ¹Ù„ÙŠÙ‚Ø§Øª Ù„Ù„Ù…Ù„Ù
    return content;
}

function createFolder(path) {
    const parts = path.split('/').filter(p => p);
    let currentPath = '/';

    for (const part of parts) {
        const newPath = currentPath + part + '/';
        if (!workspace.folders[newPath]) {
            const folderId = 'folder_' + Date.now() + Math.random().toString(36).substr(2, 5);
            workspace.folders[newPath] = {
                id: folderId,
