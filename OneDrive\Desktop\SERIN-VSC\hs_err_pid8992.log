#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 851536 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=8992, tid=1284
#
# JRE version: Java(TM) SE Runtime Environment (21.0.1+12) (build 21.0.1+12-LTS-29)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (21.0.1+12-LTS-29, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: c:\Users\<USER>\.vscode\extensions\redhat.vscode-apache-camel-1.11.0\jars\language-server.jar

Host: Intel(R) Core(TM) i5-10500H CPU @ 2.50GHz, 12 cores, 7G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5124)
Time: Tue May 27 20:25:44 2025 Egypt Daylight Time elapsed time: 14.205421 seconds (0d 0h 0m 14s)

---------------  T H R E A D  ---------------

Current thread (0x0000029efc188fb0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=1284, stack(0x000000ac59300000,0x000000ac59400000) (1024K)]


Current CompileTask:
C2:  14205 3663       4       org.springframework.boot.loader.net.protocol.jar.LazyDelegatingInputStream::read (11 bytes)

Stack: [0x000000ac59300000,0x000000ac59400000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6c9029]
V  [jvm.dll+0x854b81]
V  [jvm.dll+0x856e5e]
V  [jvm.dll+0x857543]
V  [jvm.dll+0x280c06]
V  [jvm.dll+0xc3ead]
V  [jvm.dll+0xc43c3]
V  [jvm.dll+0x3b5732]
V  [jvm.dll+0x382475]
V  [jvm.dll+0x3818ea]
V  [jvm.dll+0x249ad0]
V  [jvm.dll+0x2490b1]
V  [jvm.dll+0x1c9ca1]
V  [jvm.dll+0x2586b9]
V  [jvm.dll+0x256c9a]
V  [jvm.dll+0x3ef1b6]
V  [jvm.dll+0x7fd728]
V  [jvm.dll+0x6c777d]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af38]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000029efd4f00c0, length=14, elements={
0x0000029efc176b70, 0x0000029efc1776a0, 0x0000029efc17ca20, 0x0000029efc17e4b0,
0x0000029efc181960, 0x0000029efc17f200, 0x0000029efc188fb0, 0x0000029efc1ae0c0,
0x0000029efc17ff20, 0x0000029efc17eb70, 0x0000029efc17f890, 0x0000029efc181ff0,
0x0000029efc180c40, 0x0000029efc1812d0
}

Java Threads: ( => current thread )
  0x0000029efc176b70 JavaThread "Reference Handler"          daemon [_thread_blocked, id=7220, stack(0x000000ac58d00000,0x000000ac58e00000) (1024K)]
  0x0000029efc1776a0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=10756, stack(0x000000ac58e00000,0x000000ac58f00000) (1024K)]
  0x0000029efc17ca20 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=16592, stack(0x000000ac58f00000,0x000000ac59000000) (1024K)]
  0x0000029efc17e4b0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=2440, stack(0x000000ac59000000,0x000000ac59100000) (1024K)]
  0x0000029efc181960 JavaThread "Service Thread"             daemon [_thread_blocked, id=18928, stack(0x000000ac59100000,0x000000ac59200000) (1024K)]
  0x0000029efc17f200 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=7432, stack(0x000000ac59200000,0x000000ac59300000) (1024K)]
=>0x0000029efc188fb0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=1284, stack(0x000000ac59300000,0x000000ac59400000) (1024K)]
  0x0000029efc1ae0c0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=4848, stack(0x000000ac59400000,0x000000ac59500000) (1024K)]
  0x0000029efc17ff20 JavaThread "Notification Thread"        daemon [_thread_blocked, id=19396, stack(0x000000ac59500000,0x000000ac59600000) (1024K)]
  0x0000029efc17eb70 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=4980, stack(0x000000ac59600000,0x000000ac59700000) (1024K)]
  0x0000029efc17f890 JavaThread "Cleaner-0"                  daemon [_thread_blocked, id=10320, stack(0x000000ac59700000,0x000000ac59800000) (1024K)]
  0x0000029efc181ff0 JavaThread "ForkJoinPool.commonPool-worker-1" daemon [_thread_blocked, id=3756, stack(0x000000ac5a100000,0x000000ac5a200000) (1024K)]
  0x0000029efc180c40 JavaThread "pool-2-thread-1"                   [_thread_in_native, id=19088, stack(0x000000ac5a200000,0x000000ac5a300000) (1024K)]
  0x0000029efc1812d0 JavaThread "DestroyJavaVM"                     [_thread_blocked, id=10472, stack(0x000000ac58500000,0x000000ac58600000) (1024K)]
Total: 14

Other Threads:
  0x0000029efc159b80 VMThread "VM Thread"                           [id=6804, stack(0x000000ac58c00000,0x000000ac58d00000) (1024K)]
  0x0000029efc143ac0 WatcherThread "VM Periodic Task Thread"        [id=18604, stack(0x000000ac58b00000,0x000000ac58c00000) (1024K)]
  0x0000029eee4e19f0 WorkerThread "GC Thread#0"                     [id=16640, stack(0x000000ac58600000,0x000000ac58700000) (1024K)]
  0x0000029efcd75fb0 WorkerThread "GC Thread#1"                     [id=4488, stack(0x000000ac59a00000,0x000000ac59b00000) (1024K)]
  0x0000029efcd76350 WorkerThread "GC Thread#2"                     [id=7116, stack(0x000000ac59b00000,0x000000ac59c00000) (1024K)]
  0x0000029efc936e00 WorkerThread "GC Thread#3"                     [id=4404, stack(0x000000ac59c00000,0x000000ac59d00000) (1024K)]
  0x0000029efd55a2b0 WorkerThread "GC Thread#4"                     [id=8936, stack(0x000000ac59d00000,0x000000ac59e00000) (1024K)]
  0x0000029efd7659a0 WorkerThread "GC Thread#5"                     [id=6004, stack(0x000000ac59e00000,0x000000ac59f00000) (1024K)]
  0x0000029efd5d5d80 WorkerThread "GC Thread#6"                     [id=9864, stack(0x000000ac59f00000,0x000000ac5a000000) (1024K)]
  0x0000029efd673610 WorkerThread "GC Thread#7"                     [id=8968, stack(0x000000ac5a000000,0x000000ac5a100000) (1024K)]
  0x0000029efd672ed0 WorkerThread "GC Thread#8"                     [id=18556, stack(0x000000ac5b400000,0x000000ac5b500000) (1024K)]
  0x0000029efd6739b0 WorkerThread "GC Thread#9"                     [id=17724, stack(0x000000ac5b500000,0x000000ac5b600000) (1024K)]
  0x0000029eee4f2480 ConcurrentGCThread "G1 Main Marker"            [id=19256, stack(0x000000ac58700000,0x000000ac58800000) (1024K)]
  0x0000029eee4f2f80 WorkerThread "G1 Conc#0"                       [id=13544, stack(0x000000ac58800000,0x000000ac58900000) (1024K)]
  0x0000029efc010550 ConcurrentGCThread "G1 Refine#0"               [id=17264, stack(0x000000ac58900000,0x000000ac58a00000) (1024K)]
  0x0000029efc012af0 ConcurrentGCThread "G1 Service"                [id=4856, stack(0x000000ac58a00000,0x000000ac58b00000) (1024K)]
Total: 16

Threads with active compile tasks:
C2 CompilerThread0    14232 3663       4       org.springframework.boot.loader.net.protocol.jar.LazyDelegatingInputStream::read (11 bytes)
Total: 1

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000083800000, size: 1992 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000029e8f000000-0x0000029e8fc90000-0x0000029e8fc90000), size 13172736, SharedBaseAddress: 0x0000029e8f000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000029e90000000-0x0000029ed0000000, reserved size: 1073741824
Narrow klass base: 0x0000029e8f000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 12 total, 12 available
 Memory: 7967M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 126M
 Heap Max Capacity: 1992M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 129024K, used 86528K [0x0000000083800000, 0x0000000100000000)
  region size 1024K, 70 young (71680K), 5 survivors (5120K)
 Metaspace       used 19121K, committed 19456K, reserved 1114112K
  class space    used 2146K, committed 2304K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000083800000, 0x0000000083900000, 0x0000000083900000|100%| O|  |TAMS 0x0000000083800000| PB 0x0000000083800000| Untracked 
|   1|0x0000000083900000, 0x0000000083a00000, 0x0000000083a00000|100%| O|  |TAMS 0x0000000083900000| PB 0x0000000083900000| Untracked 
|   2|0x0000000083a00000, 0x0000000083b00000, 0x0000000083b00000|100%| O|  |TAMS 0x0000000083a00000| PB 0x0000000083a00000| Untracked 
|   3|0x0000000083b00000, 0x0000000083c00000, 0x0000000083c00000|100%| O|  |TAMS 0x0000000083b00000| PB 0x0000000083b00000| Untracked 
|   4|0x0000000083c00000, 0x0000000083d00000, 0x0000000083d00000|100%| O|  |TAMS 0x0000000083c00000| PB 0x0000000083c00000| Untracked 
|   5|0x0000000083d00000, 0x0000000083e00000, 0x0000000083e00000|100%| O|  |TAMS 0x0000000083d00000| PB 0x0000000083d00000| Untracked 
|   6|0x0000000083e00000, 0x0000000083f00000, 0x0000000083f00000|100%| O|  |TAMS 0x0000000083e00000| PB 0x0000000083e00000| Untracked 
|   7|0x0000000083f00000, 0x0000000084000000, 0x0000000084000000|100%| O|  |TAMS 0x0000000083f00000| PB 0x0000000083f00000| Untracked 
|   8|0x0000000084000000, 0x0000000084100000, 0x0000000084100000|100%| O|  |TAMS 0x0000000084000000| PB 0x0000000084000000| Untracked 
|   9|0x0000000084100000, 0x0000000084200000, 0x0000000084200000|100%| O|  |TAMS 0x0000000084100000| PB 0x0000000084100000| Untracked 
|  10|0x0000000084200000, 0x0000000084300000, 0x0000000084300000|100%| O|  |TAMS 0x0000000084200000| PB 0x0000000084200000| Untracked 
|  11|0x0000000084300000, 0x0000000084400000, 0x0000000084400000|100%| O|  |TAMS 0x0000000084300000| PB 0x0000000084300000| Untracked 
|  12|0x0000000084400000, 0x0000000084500000, 0x0000000084500000|100%| O|  |TAMS 0x0000000084400000| PB 0x0000000084400000| Untracked 
|  13|0x0000000084500000, 0x0000000084600000, 0x0000000084600000|100%| O|  |TAMS 0x0000000084500000| PB 0x0000000084500000| Untracked 
|  14|0x0000000084600000, 0x0000000084700000, 0x0000000084700000|100%| O|  |TAMS 0x0000000084600000| PB 0x0000000084600000| Untracked 
|  15|0x0000000084700000, 0x0000000084800000, 0x0000000084800000|100%| O|  |TAMS 0x0000000084700000| PB 0x0000000084700000| Untracked 
|  16|0x0000000084800000, 0x0000000084900000, 0x0000000084900000|100%| O|  |TAMS 0x0000000084800000| PB 0x0000000084800000| Untracked 
|  17|0x0000000084900000, 0x0000000084900000, 0x0000000084a00000|  0%| F|  |TAMS 0x0000000084900000| PB 0x0000000084900000| Untracked 
|  18|0x0000000084a00000, 0x0000000084a00000, 0x0000000084b00000|  0%| F|  |TAMS 0x0000000084a00000| PB 0x0000000084a00000| Untracked 
|  19|0x0000000084b00000, 0x0000000084b00000, 0x0000000084c00000|  0%| F|  |TAMS 0x0000000084b00000| PB 0x0000000084b00000| Untracked 
|  20|0x0000000084c00000, 0x0000000084c00000, 0x0000000084d00000|  0%| F|  |TAMS 0x0000000084c00000| PB 0x0000000084c00000| Untracked 
|  21|0x0000000084d00000, 0x0000000084d00000, 0x0000000084e00000|  0%| F|  |TAMS 0x0000000084d00000| PB 0x0000000084d00000| Untracked 
|  22|0x0000000084e00000, 0x0000000084e00000, 0x0000000084f00000|  0%| F|  |TAMS 0x0000000084e00000| PB 0x0000000084e00000| Untracked 
|  23|0x0000000084f00000, 0x0000000084f00000, 0x0000000085000000|  0%| F|  |TAMS 0x0000000084f00000| PB 0x0000000084f00000| Untracked 
|  24|0x0000000085000000, 0x0000000085000000, 0x0000000085100000|  0%| F|  |TAMS 0x0000000085000000| PB 0x0000000085000000| Untracked 
|  25|0x0000000085100000, 0x0000000085100000, 0x0000000085200000|  0%| F|  |TAMS 0x0000000085100000| PB 0x0000000085100000| Untracked 
|  26|0x0000000085200000, 0x0000000085200000, 0x0000000085300000|  0%| F|  |TAMS 0x0000000085200000| PB 0x0000000085200000| Untracked 
|  27|0x0000000085300000, 0x0000000085300000, 0x0000000085400000|  0%| F|  |TAMS 0x0000000085300000| PB 0x0000000085300000| Untracked 
|  28|0x0000000085400000, 0x0000000085400000, 0x0000000085500000|  0%| F|  |TAMS 0x0000000085400000| PB 0x0000000085400000| Untracked 
|  29|0x0000000085500000, 0x0000000085500000, 0x0000000085600000|  0%| F|  |TAMS 0x0000000085500000| PB 0x0000000085500000| Untracked 
|  30|0x0000000085600000, 0x0000000085600000, 0x0000000085700000|  0%| F|  |TAMS 0x0000000085600000| PB 0x0000000085600000| Untracked 
|  31|0x0000000085700000, 0x0000000085700000, 0x0000000085800000|  0%| F|  |TAMS 0x0000000085700000| PB 0x0000000085700000| Untracked 
|  32|0x0000000085800000, 0x0000000085800000, 0x0000000085900000|  0%| F|  |TAMS 0x0000000085800000| PB 0x0000000085800000| Untracked 
|  33|0x0000000085900000, 0x0000000085900000, 0x0000000085a00000|  0%| F|  |TAMS 0x0000000085900000| PB 0x0000000085900000| Untracked 
|  34|0x0000000085a00000, 0x0000000085a00000, 0x0000000085b00000|  0%| F|  |TAMS 0x0000000085a00000| PB 0x0000000085a00000| Untracked 
|  35|0x0000000085b00000, 0x0000000085b00000, 0x0000000085c00000|  0%| F|  |TAMS 0x0000000085b00000| PB 0x0000000085b00000| Untracked 
|  36|0x0000000085c00000, 0x0000000085c00000, 0x0000000085d00000|  0%| F|  |TAMS 0x0000000085c00000| PB 0x0000000085c00000| Untracked 
|  37|0x0000000085d00000, 0x0000000085d00000, 0x0000000085e00000|  0%| F|  |TAMS 0x0000000085d00000| PB 0x0000000085d00000| Untracked 
|  38|0x0000000085e00000, 0x0000000085e00000, 0x0000000085f00000|  0%| F|  |TAMS 0x0000000085e00000| PB 0x0000000085e00000| Untracked 
|  39|0x0000000085f00000, 0x0000000085f00000, 0x0000000086000000|  0%| F|  |TAMS 0x0000000085f00000| PB 0x0000000085f00000| Untracked 
|  40|0x0000000086000000, 0x0000000086000000, 0x0000000086100000|  0%| F|  |TAMS 0x0000000086000000| PB 0x0000000086000000| Untracked 
|  41|0x0000000086100000, 0x0000000086100000, 0x0000000086200000|  0%| F|  |TAMS 0x0000000086100000| PB 0x0000000086100000| Untracked 
|  42|0x0000000086200000, 0x0000000086200000, 0x0000000086300000|  0%| F|  |TAMS 0x0000000086200000| PB 0x0000000086200000| Untracked 
|  43|0x0000000086300000, 0x0000000086300000, 0x0000000086400000|  0%| F|  |TAMS 0x0000000086300000| PB 0x0000000086300000| Untracked 
|  44|0x0000000086400000, 0x0000000086400000, 0x0000000086500000|  0%| F|  |TAMS 0x0000000086400000| PB 0x0000000086400000| Untracked 
|  45|0x0000000086500000, 0x0000000086500000, 0x0000000086600000|  0%| F|  |TAMS 0x0000000086500000| PB 0x0000000086500000| Untracked 
|  46|0x0000000086600000, 0x0000000086600000, 0x0000000086700000|  0%| F|  |TAMS 0x0000000086600000| PB 0x0000000086600000| Untracked 
|  47|0x0000000086700000, 0x0000000086700000, 0x0000000086800000|  0%| F|  |TAMS 0x0000000086700000| PB 0x0000000086700000| Untracked 
|  48|0x0000000086800000, 0x0000000086800000, 0x0000000086900000|  0%| F|  |TAMS 0x0000000086800000| PB 0x0000000086800000| Untracked 
|  49|0x0000000086900000, 0x0000000086900000, 0x0000000086a00000|  0%| F|  |TAMS 0x0000000086900000| PB 0x0000000086900000| Untracked 
|  50|0x0000000086a00000, 0x0000000086a00000, 0x0000000086b00000|  0%| F|  |TAMS 0x0000000086a00000| PB 0x0000000086a00000| Untracked 
|  51|0x0000000086b00000, 0x0000000086b00000, 0x0000000086c00000|  0%| F|  |TAMS 0x0000000086b00000| PB 0x0000000086b00000| Untracked 
|  52|0x0000000086c00000, 0x0000000086c00000, 0x0000000086d00000|  0%| F|  |TAMS 0x0000000086c00000| PB 0x0000000086c00000| Untracked 
|  53|0x0000000086d00000, 0x0000000086d00000, 0x0000000086e00000|  0%| F|  |TAMS 0x0000000086d00000| PB 0x0000000086d00000| Untracked 
|  54|0x0000000086e00000, 0x0000000086e00000, 0x0000000086f00000|  0%| F|  |TAMS 0x0000000086e00000| PB 0x0000000086e00000| Untracked 
|  55|0x0000000086f00000, 0x0000000086f00000, 0x0000000087000000|  0%| F|  |TAMS 0x0000000086f00000| PB 0x0000000086f00000| Untracked 
|  56|0x0000000087000000, 0x0000000087080000, 0x0000000087100000| 50%| S|CS|TAMS 0x0000000087000000| PB 0x0000000087000000| Complete 
|  57|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%| S|CS|TAMS 0x0000000087100000| PB 0x0000000087100000| Complete 
|  58|0x0000000087200000, 0x0000000087300000, 0x0000000087300000|100%| S|CS|TAMS 0x0000000087200000| PB 0x0000000087200000| Complete 
|  59|0x0000000087300000, 0x0000000087400000, 0x0000000087400000|100%| S|CS|TAMS 0x0000000087300000| PB 0x0000000087300000| Complete 
|  60|0x0000000087400000, 0x0000000087500000, 0x0000000087500000|100%| S|CS|TAMS 0x0000000087400000| PB 0x0000000087400000| Complete 
|  61|0x0000000087500000, 0x00000000875e09f0, 0x0000000087600000| 87%| E|  |TAMS 0x0000000087500000| PB 0x0000000087500000| Complete 
|  62|0x0000000087600000, 0x0000000087700000, 0x0000000087700000|100%| E|CS|TAMS 0x0000000087600000| PB 0x0000000087600000| Complete 
|  63|0x0000000087700000, 0x0000000087800000, 0x0000000087800000|100%| E|CS|TAMS 0x0000000087700000| PB 0x0000000087700000| Complete 
|  64|0x0000000087800000, 0x0000000087900000, 0x0000000087900000|100%| E|CS|TAMS 0x0000000087800000| PB 0x0000000087800000| Complete 
|  65|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%| E|CS|TAMS 0x0000000087900000| PB 0x0000000087900000| Complete 
|  66|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%| E|CS|TAMS 0x0000000087a00000| PB 0x0000000087a00000| Complete 
|  67|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%| E|CS|TAMS 0x0000000087b00000| PB 0x0000000087b00000| Complete 
|  68|0x0000000087c00000, 0x0000000087d00000, 0x0000000087d00000|100%| E|CS|TAMS 0x0000000087c00000| PB 0x0000000087c00000| Complete 
|  69|0x0000000087d00000, 0x0000000087e00000, 0x0000000087e00000|100%| E|CS|TAMS 0x0000000087d00000| PB 0x0000000087d00000| Complete 
|  70|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%| E|CS|TAMS 0x0000000087e00000| PB 0x0000000087e00000| Complete 
|  71|0x0000000087f00000, 0x0000000088000000, 0x0000000088000000|100%| E|CS|TAMS 0x0000000087f00000| PB 0x0000000087f00000| Complete 
|  72|0x0000000088000000, 0x0000000088100000, 0x0000000088100000|100%| E|CS|TAMS 0x0000000088000000| PB 0x0000000088000000| Complete 
|  73|0x0000000088100000, 0x0000000088200000, 0x0000000088200000|100%| E|CS|TAMS 0x0000000088100000| PB 0x0000000088100000| Complete 
|  74|0x0000000088200000, 0x0000000088300000, 0x0000000088300000|100%| E|CS|TAMS 0x0000000088200000| PB 0x0000000088200000| Complete 
|  75|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%| E|CS|TAMS 0x0000000088300000| PB 0x0000000088300000| Complete 
|  76|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%| E|CS|TAMS 0x0000000088400000| PB 0x0000000088400000| Complete 
|  77|0x0000000088500000, 0x0000000088600000, 0x0000000088600000|100%| E|CS|TAMS 0x0000000088500000| PB 0x0000000088500000| Complete 
|  78|0x0000000088600000, 0x0000000088700000, 0x0000000088700000|100%| E|CS|TAMS 0x0000000088600000| PB 0x0000000088600000| Complete 
|  79|0x0000000088700000, 0x0000000088800000, 0x0000000088800000|100%| E|CS|TAMS 0x0000000088700000| PB 0x0000000088700000| Complete 
|  80|0x0000000088800000, 0x0000000088900000, 0x0000000088900000|100%| E|CS|TAMS 0x0000000088800000| PB 0x0000000088800000| Complete 
|  81|0x0000000088900000, 0x0000000088a00000, 0x0000000088a00000|100%| E|CS|TAMS 0x0000000088900000| PB 0x0000000088900000| Complete 
|  82|0x0000000088a00000, 0x0000000088b00000, 0x0000000088b00000|100%| E|CS|TAMS 0x0000000088a00000| PB 0x0000000088a00000| Complete 
|  83|0x0000000088b00000, 0x0000000088c00000, 0x0000000088c00000|100%| E|CS|TAMS 0x0000000088b00000| PB 0x0000000088b00000| Complete 
|  84|0x0000000088c00000, 0x0000000088d00000, 0x0000000088d00000|100%| E|CS|TAMS 0x0000000088c00000| PB 0x0000000088c00000| Complete 
|  85|0x0000000088d00000, 0x0000000088e00000, 0x0000000088e00000|100%| E|CS|TAMS 0x0000000088d00000| PB 0x0000000088d00000| Complete 
|  86|0x0000000088e00000, 0x0000000088f00000, 0x0000000088f00000|100%| E|CS|TAMS 0x0000000088e00000| PB 0x0000000088e00000| Complete 
|  87|0x0000000088f00000, 0x0000000089000000, 0x0000000089000000|100%| E|CS|TAMS 0x0000000088f00000| PB 0x0000000088f00000| Complete 
|  88|0x0000000089000000, 0x0000000089100000, 0x0000000089100000|100%| E|CS|TAMS 0x0000000089000000| PB 0x0000000089000000| Complete 
|  89|0x0000000089100000, 0x0000000089200000, 0x0000000089200000|100%| E|CS|TAMS 0x0000000089100000| PB 0x0000000089100000| Complete 
|  90|0x0000000089200000, 0x0000000089300000, 0x0000000089300000|100%| E|CS|TAMS 0x0000000089200000| PB 0x0000000089200000| Complete 
|  91|0x0000000089300000, 0x0000000089400000, 0x0000000089400000|100%| E|CS|TAMS 0x0000000089300000| PB 0x0000000089300000| Complete 
|  92|0x0000000089400000, 0x0000000089500000, 0x0000000089500000|100%| E|CS|TAMS 0x0000000089400000| PB 0x0000000089400000| Complete 
|  93|0x0000000089500000, 0x0000000089600000, 0x0000000089600000|100%| E|CS|TAMS 0x0000000089500000| PB 0x0000000089500000| Complete 
|  94|0x0000000089600000, 0x0000000089700000, 0x0000000089700000|100%| E|CS|TAMS 0x0000000089600000| PB 0x0000000089600000| Complete 
|  95|0x0000000089700000, 0x0000000089800000, 0x0000000089800000|100%| E|CS|TAMS 0x0000000089700000| PB 0x0000000089700000| Complete 
|  96|0x0000000089800000, 0x0000000089900000, 0x0000000089900000|100%| E|CS|TAMS 0x0000000089800000| PB 0x0000000089800000| Complete 
|  97|0x0000000089900000, 0x0000000089a00000, 0x0000000089a00000|100%| E|CS|TAMS 0x0000000089900000| PB 0x0000000089900000| Complete 
|  98|0x0000000089a00000, 0x0000000089b00000, 0x0000000089b00000|100%| E|CS|TAMS 0x0000000089a00000| PB 0x0000000089a00000| Complete 
|  99|0x0000000089b00000, 0x0000000089c00000, 0x0000000089c00000|100%| E|CS|TAMS 0x0000000089b00000| PB 0x0000000089b00000| Complete 
| 100|0x0000000089c00000, 0x0000000089d00000, 0x0000000089d00000|100%| E|CS|TAMS 0x0000000089c00000| PB 0x0000000089c00000| Complete 
| 101|0x0000000089d00000, 0x0000000089e00000, 0x0000000089e00000|100%| E|CS|TAMS 0x0000000089d00000| PB 0x0000000089d00000| Complete 
| 102|0x0000000089e00000, 0x0000000089f00000, 0x0000000089f00000|100%| E|CS|TAMS 0x0000000089e00000| PB 0x0000000089e00000| Complete 
| 103|0x0000000089f00000, 0x000000008a000000, 0x000000008a000000|100%| E|  |TAMS 0x0000000089f00000| PB 0x0000000089f00000| Complete 
| 104|0x000000008a000000, 0x000000008a100000, 0x000000008a100000|100%| E|CS|TAMS 0x000000008a000000| PB 0x000000008a000000| Complete 
| 105|0x000000008a100000, 0x000000008a200000, 0x000000008a200000|100%| E|CS|TAMS 0x000000008a100000| PB 0x000000008a100000| Complete 
| 106|0x000000008a200000, 0x000000008a300000, 0x000000008a300000|100%| E|CS|TAMS 0x000000008a200000| PB 0x000000008a200000| Complete 
| 107|0x000000008a300000, 0x000000008a400000, 0x000000008a400000|100%| E|CS|TAMS 0x000000008a300000| PB 0x000000008a300000| Complete 
| 108|0x000000008a400000, 0x000000008a500000, 0x000000008a500000|100%| E|CS|TAMS 0x000000008a400000| PB 0x000000008a400000| Complete 
| 109|0x000000008a500000, 0x000000008a600000, 0x000000008a600000|100%| E|CS|TAMS 0x000000008a500000| PB 0x000000008a500000| Complete 
| 110|0x000000008a600000, 0x000000008a700000, 0x000000008a700000|100%| E|CS|TAMS 0x000000008a600000| PB 0x000000008a600000| Complete 
| 111|0x000000008a700000, 0x000000008a800000, 0x000000008a800000|100%| E|CS|TAMS 0x000000008a700000| PB 0x000000008a700000| Complete 
| 112|0x000000008a800000, 0x000000008a900000, 0x000000008a900000|100%| E|CS|TAMS 0x000000008a800000| PB 0x000000008a800000| Complete 
| 113|0x000000008a900000, 0x000000008aa00000, 0x000000008aa00000|100%| E|CS|TAMS 0x000000008a900000| PB 0x000000008a900000| Complete 
| 114|0x000000008aa00000, 0x000000008ab00000, 0x000000008ab00000|100%| E|CS|TAMS 0x000000008aa00000| PB 0x000000008aa00000| Complete 
| 115|0x000000008ab00000, 0x000000008ac00000, 0x000000008ac00000|100%| E|CS|TAMS 0x000000008ab00000| PB 0x000000008ab00000| Complete 
| 116|0x000000008ac00000, 0x000000008ad00000, 0x000000008ad00000|100%| E|CS|TAMS 0x000000008ac00000| PB 0x000000008ac00000| Complete 
| 117|0x000000008ad00000, 0x000000008ae00000, 0x000000008ae00000|100%| E|CS|TAMS 0x000000008ad00000| PB 0x000000008ad00000| Complete 
| 118|0x000000008ae00000, 0x000000008af00000, 0x000000008af00000|100%| E|CS|TAMS 0x000000008ae00000| PB 0x000000008ae00000| Complete 
| 119|0x000000008af00000, 0x000000008b000000, 0x000000008b000000|100%| E|CS|TAMS 0x000000008af00000| PB 0x000000008af00000| Complete 
| 120|0x000000008b000000, 0x000000008b100000, 0x000000008b100000|100%| E|CS|TAMS 0x000000008b000000| PB 0x000000008b000000| Complete 
| 121|0x000000008b100000, 0x000000008b200000, 0x000000008b200000|100%| E|CS|TAMS 0x000000008b100000| PB 0x000000008b100000| Complete 
| 122|0x000000008b200000, 0x000000008b300000, 0x000000008b300000|100%| E|CS|TAMS 0x000000008b200000| PB 0x000000008b200000| Complete 
| 123|0x000000008b300000, 0x000000008b400000, 0x000000008b400000|100%| E|CS|TAMS 0x000000008b300000| PB 0x000000008b300000| Complete 
| 124|0x000000008b400000, 0x000000008b500000, 0x000000008b500000|100%| E|CS|TAMS 0x000000008b400000| PB 0x000000008b400000| Complete 
| 125|0x000000008b500000, 0x000000008b600000, 0x000000008b600000|100%| E|CS|TAMS 0x000000008b500000| PB 0x000000008b500000| Complete 

Card table byte_map: [0x0000029ef7220000,0x0000029ef7610000] _byte_map_base: 0x0000029ef6e04000

Marking Bits: (CMBitMap*) 0x0000029eee4e20f0
 Bits: [0x0000029ef7610000, 0x0000029ef9530000)

Polling page: 0x0000029eec2e0000

Metaspace:

Usage:
  Non-class:     16.58 MB used.
      Class:      2.10 MB used.
       Both:     18.67 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      16.75 MB ( 26%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       2.25 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      19.00 MB (  2%) committed. 

Chunk freelists:
   Non-Class:  15.19 MB
       Class:  13.64 MB
        Both:  28.83 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 458.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 304.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 1120.
num_chunk_merges: 0.
num_chunk_splits: 783.
num_chunks_enlarged: 564.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=2222Kb max_used=2222Kb free=117777Kb
 bounds [0x0000029e87ad0000, 0x0000029e87d40000, 0x0000029e8f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=7425Kb max_used=7425Kb free=112575Kb
 bounds [0x0000029e80000000, 0x0000029e80750000, 0x0000029e87530000]
CodeHeap 'non-nmethods': size=5760Kb used=1503Kb max_used=1578Kb free=4256Kb
 bounds [0x0000029e87530000, 0x0000029e877a0000, 0x0000029e87ad0000]
 total_blobs=4238 nmethods=3661 adapters=481
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 3.531 Thread 0x0000029efc1ae0c0 3657       3       java.lang.reflect.Field::setOverrideFieldAccessor (20 bytes)
Event: 3.531 Thread 0x0000029efc1ae0c0 nmethod 3657 0x0000029e8073dc90 code [0x0000029e8073de60, 0x0000029e8073e138]
Event: 3.531 Thread 0x0000029efc1ae0c0 3659       3       java.lang.invoke.MethodHandles$Lookup::unreflectField (126 bytes)
Event: 3.532 Thread 0x0000029efc1ae0c0 nmethod 3659 0x0000029e8073e210 code [0x0000029e8073e440, 0x0000029e8073ece8]
Event: 3.532 Thread 0x0000029efc1ae0c0 3653       3       java.lang.invoke.DirectMethodHandle$Holder::invokeSpecial (14 bytes)
Event: 3.532 Thread 0x0000029efc1ae0c0 nmethod 3653 0x0000029e8073ef90 code [0x0000029e8073f160, 0x0000029e8073f5e0]
Event: 3.533 Thread 0x0000029efc71c550 nmethod 3598 0x0000029e87cea610 code [0x0000029e87cea7c0, 0x0000029e87ceac50]
Event: 3.533 Thread 0x0000029efc71c550 3600       4       java.io.BufferedInputStream::getBufIfOpen (61 bytes)
Event: 3.537 Thread 0x0000029efc71c550 nmethod 3600 0x0000029e87cead90 code [0x0000029e87ceaf20, 0x0000029e87ceb250]
Event: 3.537 Thread 0x0000029efc71c550 3647       4       java.util.Collections$UnmodifiableCollection$1::next (10 bytes)
Event: 3.539 Thread 0x0000029efc71c550 nmethod 3647 0x0000029e87ceb310 code [0x0000029e87ceb4c0, 0x0000029e87ceb698]
Event: 3.539 Thread 0x0000029efc71c550 3658       4       java.lang.invoke.MethodHandle::<init> (37 bytes)
Event: 3.543 Thread 0x0000029efc71c550 nmethod 3658 0x0000029e87ceb890 code [0x0000029e87ceba40, 0x0000029e87cebde0]
Event: 3.593 Thread 0x0000029efc4ab0f0 nmethod 3597 0x0000029e87cebf90 code [0x0000029e87cec4a0, 0x0000029e87cf0708]
Event: 3.601 Thread 0x0000029efc188fb0 nmethod 3523 0x0000029e87cf2990 code [0x0000029e87cf3180, 0x0000029e87cf8e10]
Event: 14.119 Thread 0x0000029efc1ae0c0 3660       3       java.util.Collections$UnmodifiableCollection::iterator (9 bytes)
Event: 14.119 Thread 0x0000029efc1ae0c0 nmethod 3660 0x0000029e8073f710 code [0x0000029e8073f8e0, 0x0000029e8073fc98]
Event: 14.119 Thread 0x0000029efc1ae0c0 3661       3       java.util.Collections$UnmodifiableCollection$1::<init> (26 bytes)
Event: 14.120 Thread 0x0000029efc1ae0c0 nmethod 3661 0x0000029e8073fe10 code [0x0000029e8073ffe0, 0x0000029e807402e0]
Event: 14.122 Thread 0x0000029efc188fb0 3663       4       org.springframework.boot.loader.net.protocol.jar.LazyDelegatingInputStream::read (11 bytes)

GC Heap History (10 events):
Event: 0.713 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 129024K, used 23552K [0x0000000083800000, 0x0000000100000000)
  region size 1024K, 23 young (23552K), 0 survivors (0K)
 Metaspace       used 2776K, committed 3008K, reserved 1114112K
  class space    used 268K, committed 384K, reserved 1048576K
}
Event: 0.719 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 129024K, used 3205K [0x0000000083800000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 2776K, committed 3008K, reserved 1114112K
  class space    used 268K, committed 384K, reserved 1048576K
}
Event: 1.324 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 129024K, used 35973K [0x0000000083800000, 0x0000000100000000)
  region size 1024K, 35 young (35840K), 3 survivors (3072K)
 Metaspace       used 7218K, committed 7488K, reserved 1114112K
  class space    used 806K, committed 896K, reserved 1048576K
}
Event: 1.330 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 129024K, used 4990K [0x0000000083800000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 7218K, committed 7488K, reserved 1114112K
  class space    used 806K, committed 896K, reserved 1048576K
}
Event: 2.009 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total 129024K, used 78718K [0x0000000083800000, 0x0000000100000000)
  region size 1024K, 75 young (76800K), 2 survivors (2048K)
 Metaspace       used 12653K, committed 12992K, reserved 1114112K
  class space    used 1419K, committed 1536K, reserved 1048576K
}
Event: 2.018 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total 129024K, used 8282K [0x0000000083800000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 12653K, committed 12992K, reserved 1114112K
  class space    used 1419K, committed 1536K, reserved 1048576K
}
Event: 2.419 GC heap before
{Heap before GC invocations=3 (full 0):
 garbage-first heap   total 129024K, used 78938K [0x0000000083800000, 0x0000000100000000)
  region size 1024K, 75 young (76800K), 5 survivors (5120K)
 Metaspace       used 14799K, committed 15168K, reserved 1114112K
  class space    used 1673K, committed 1856K, reserved 1048576K
}
Event: 2.437 GC heap after
{Heap after GC invocations=4 (full 0):
 garbage-first heap   total 129024K, used 17273K [0x0000000083800000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 14799K, committed 15168K, reserved 1114112K
  class space    used 1673K, committed 1856K, reserved 1048576K
}
Event: 2.947 GC heap before
{Heap before GC invocations=4 (full 0):
 garbage-first heap   total 129024K, used 82809K [0x0000000083800000, 0x0000000100000000)
  region size 1024K, 75 young (76800K), 10 survivors (10240K)
 Metaspace       used 16355K, committed 16768K, reserved 1114112K
  class space    used 1787K, committed 1984K, reserved 1048576K
}
Event: 2.965 GC heap after
{Heap after GC invocations=5 (full 0):
 garbage-first heap   total 129024K, used 22016K [0x0000000083800000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 16355K, committed 16768K, reserved 1114112K
  class space    used 1787K, committed 1984K, reserved 1048576K
}

Dll operation events (9 events):
Event: 0.020 Loaded shared library C:\Program Files\Java\jdk-21\bin\java.dll
Event: 0.126 Loaded shared library C:\Program Files\Java\jdk-21\bin\jsvml.dll
Event: 0.154 Loaded shared library C:\Program Files\Java\jdk-21\bin\net.dll
Event: 0.157 Loaded shared library C:\Program Files\Java\jdk-21\bin\nio.dll
Event: 0.163 Loaded shared library C:\Program Files\Java\jdk-21\bin\zip.dll
Event: 0.225 Loaded shared library C:\Program Files\Java\jdk-21\bin\jimage.dll
Event: 0.401 Loaded shared library C:\Program Files\Java\jdk-21\bin\verify.dll
Event: 1.083 Loaded shared library C:\Program Files\Java\jdk-21\bin\management.dll
Event: 1.090 Loaded shared library C:\Program Files\Java\jdk-21\bin\management_ext.dll

Deoptimization events (20 events):
Event: 3.455 Thread 0x0000029efc180c40 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000029e87c1ad3c relative=0x000000000000109c
Event: 3.455 Thread 0x0000029efc180c40 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000029e87c1ad3c method=java.util.concurrent.ConcurrentHashMap.putVal(Ljava/lang/Object;Ljava/lang/Object;Z)Ljava/lang/Object; @ 256 c2
Event: 3.455 Thread 0x0000029efc180c40 DEOPT PACKING pc=0x0000029e87c1ad3c sp=0x000000ac5a2fd790
Event: 3.455 Thread 0x0000029efc180c40 DEOPT UNPACKING pc=0x0000029e875846a2 sp=0x000000ac5a2fd718 mode 2
Event: 3.470 Thread 0x0000029efc180c40 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000029e87cbf664 relative=0x00000000000002c4
Event: 3.470 Thread 0x0000029efc180c40 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000029e87cbf664 method=java.util.Arrays.copyOf([BI)[B @ 3 c2
Event: 3.470 Thread 0x0000029efc180c40 DEOPT PACKING pc=0x0000029e87cbf664 sp=0x000000ac5a2fddb0
Event: 3.470 Thread 0x0000029efc180c40 DEOPT UNPACKING pc=0x0000029e875846a2 sp=0x000000ac5a2fdd70 mode 2
Event: 3.473 Thread 0x0000029efc180c40 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000029e87b1113c relative=0x000000000000017c
Event: 3.473 Thread 0x0000029efc180c40 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000029e87b1113c method=java.nio.ByteBuffer.putBuffer(ILjava/nio/ByteBuffer;II)V @ 1 c2
Event: 3.473 Thread 0x0000029efc180c40 DEOPT PACKING pc=0x0000029e87b1113c sp=0x000000ac5a2fdd40
Event: 3.473 Thread 0x0000029efc180c40 DEOPT UNPACKING pc=0x0000029e875846a2 sp=0x000000ac5a2fdcc0 mode 2
Event: 3.531 Thread 0x0000029efc180c40 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000029e87ce9e3c relative=0x000000000000063c
Event: 3.531 Thread 0x0000029efc180c40 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000029e87ce9e3c method=java.lang.String.<init>(Ljava/lang/AbstractStringBuilder;Ljava/lang/Void;)V @ 10 c2
Event: 3.531 Thread 0x0000029efc180c40 DEOPT PACKING pc=0x0000029e87ce9e3c sp=0x000000ac5a2fedc0
Event: 3.531 Thread 0x0000029efc180c40 DEOPT UNPACKING pc=0x0000029e875846a2 sp=0x000000ac5a2fed88 mode 2
Event: 14.124 Thread 0x0000029efc180c40 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000029e87ce9e3c relative=0x000000000000063c
Event: 14.124 Thread 0x0000029efc180c40 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000029e87ce9e3c method=java.lang.String.<init>(Ljava/lang/AbstractStringBuilder;Ljava/lang/Void;)V @ 10 c2
Event: 14.124 Thread 0x0000029efc180c40 DEOPT PACKING pc=0x0000029e87ce9e3c sp=0x000000ac5a2fe8f0
Event: 14.124 Thread 0x0000029efc180c40 DEOPT UNPACKING pc=0x0000029e875846a2 sp=0x000000ac5a2fe8b8 mode 2

Classes loaded (20 events):
Event: 3.447 Loading class java/time/zone/ZoneRulesProvider$1
Event: 3.447 Loading class java/time/zone/ZoneRulesProvider$1 done
Event: 3.447 Loading class java/time/zone/TzdbZoneRulesProvider
Event: 3.447 Loading class java/time/zone/TzdbZoneRulesProvider done
Event: 3.451 Loading class java/time/zone/Ser
Event: 3.452 Loading class java/io/Externalizable
Event: 3.452 Loading class java/io/Externalizable done
Event: 3.452 Loading class java/time/zone/Ser done
Event: 3.452 Loading class java/time/Month
Event: 3.453 Loading class java/time/Month done
Event: 3.453 Loading class java/time/DayOfWeek
Event: 3.453 Loading class java/time/DayOfWeek done
Event: 3.453 Loading class java/time/zone/ZoneOffsetTransitionRule$TimeDefinition
Event: 3.453 Loading class java/time/zone/ZoneOffsetTransitionRule$TimeDefinition done
Event: 3.453 Loading class java/time/zone/ZoneOffsetTransition
Event: 3.454 Loading class java/time/zone/ZoneOffsetTransition done
Event: 3.466 Loading class java/util/ConcurrentModificationException
Event: 3.466 Loading class java/util/ConcurrentModificationException done
Event: 3.470 Loading class sun/nio/cs/UTF_8$Encoder
Event: 3.470 Loading class sun/nio/cs/UTF_8$Encoder done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 1.602 Thread 0x0000029eee48f7b0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008a3e9338}: 'void java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000008a3e9338) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.615 Thread 0x0000029eee48f7b0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008a21abe0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x000000008a21abe0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.617 Thread 0x0000029eee48f7b0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008a22cc40}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x000000008a22cc40) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.619 Thread 0x0000029eee48f7b0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008a238290}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, int, int, int, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008a238290) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.643 Thread 0x0000029eee48f7b0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008a2e9ab8}> (0x000000008a2e9ab8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 1.843 Thread 0x0000029eee48f7b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000087f06830}: 'boolean java.lang.reflect.AccessibleObject.canAccess(java.lang.Object, java.lang.Class)'> (0x0000000087f06830) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.853 Thread 0x0000029eee48f7b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000087fa5040}: method resolution failed> (0x0000000087fa5040) 
thrown [s\open\src\hotspot\share\prims\methodHandles.cpp, line 1144]
Event: 1.875 Thread 0x0000029eee48f7b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000087ddf4e0}: method resolution failed> (0x0000000087ddf4e0) 
thrown [s\open\src\hotspot\share\prims\methodHandles.cpp, line 1144]
Event: 1.928 Thread 0x0000029eee48f7b0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008794e7a8}: method resolution failed> (0x000000008794e7a8) 
thrown [s\open\src\hotspot\share\prims\methodHandles.cpp, line 1144]
Event: 1.928 Thread 0x0000029eee48f7b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000879506e0}: 'boolean java.lang.reflect.AccessibleObject.trySetAccessible(java.lang.Class)'> (0x00000000879506e0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.949 Thread 0x0000029efc181ff0 Exception <a 'java/lang/ClassNotFoundException'{0x0000000087701c98}: org/springframework/boot/loader/net/protocol/jar/Handler> (0x0000000087701c98) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 1.949 Thread 0x0000029efc180c40 Exception <a 'java/lang/ClassNotFoundException'{0x000000008775fa80}: org/springframework/boot/loader/net/protocol/jar/Handler> (0x000000008775fa80) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 1.992 Thread 0x0000029efdb3caf0 Implicit null exception at 0x0000029e87b55d07 to 0x0000029e87b55df4
Event: 2.603 Thread 0x0000029eee48f7b0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008a70c2b0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008a70c2b0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.274 Thread 0x0000029eee48f7b0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x0000000087c85d00}: Found class java.lang.Object, but interface was expected> (0x0000000087c85d00) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 3.274 Thread 0x0000029eee48f7b0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x0000000087c87800}: Found class java.lang.Object, but interface was expected> (0x0000000087c87800) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 3.441 Thread 0x0000029efc180c40 Exception <a 'java/lang/NoSuchMethodError'{0x000000008774cc00}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008774cc00) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.467 Thread 0x0000029efc180c40 Exception <a 'java/lang/NoSuchMethodError'{0x0000000087600190}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000087600190) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 14.120 Thread 0x0000029efc180c40 Exception <a 'java/lang/NoSuchMethodError'{0x00000000875b2d48}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000875b2d48) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 14.120 Thread 0x0000029efc180c40 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000875b60a8}: Found class java.lang.Object, but interface was expected> (0x00000000875b60a8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 840]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 2.009 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 2.018 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 2.151 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 2.151 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 2.151 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 2.151 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 2.290 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 2.290 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 2.419 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 2.437 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 2.686 Executing VM operation: ICBufferFull
Event: 2.686 Executing VM operation: ICBufferFull done
Event: 2.947 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 2.965 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 3.133 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 3.133 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 3.483 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 3.483 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 4.488 Executing VM operation: Cleanup
Event: 4.489 Executing VM operation: Cleanup done

Events (20 events):
Event: 2.147 Thread 0x0000029efdb3b0b0 Thread exited: 0x0000029efdb3b0b0
Event: 2.147 Thread 0x0000029efdb3b740 Thread exited: 0x0000029efdb3b740
Event: 2.147 Thread 0x0000029efdb3c460 Thread exited: 0x0000029efdb3c460
Event: 2.147 Thread 0x0000029efdb375a0 Thread exited: 0x0000029efdb375a0
Event: 2.147 Thread 0x0000029efdb3caf0 Thread exited: 0x0000029efdb3caf0
Event: 2.147 Thread 0x0000029efdb3e530 Thread exited: 0x0000029efdb3e530
Event: 2.147 Thread 0x0000029efc181ff0 Thread exited: 0x0000029efc181ff0
Event: 2.147 Thread 0x0000029efdb3a390 Thread exited: 0x0000029efdb3a390
Event: 2.147 Thread 0x0000029efdb382c0 Thread exited: 0x0000029efdb382c0
Event: 2.147 Thread 0x0000029efdb39d00 Thread exited: 0x0000029efdb39d00
Event: 2.147 Thread 0x0000029efdb39670 Thread exited: 0x0000029efdb39670
Event: 2.147 Thread 0x0000029efdb3aa20 Thread exited: 0x0000029efdb3aa20
Event: 2.147 Thread 0x0000029efdb36f10 Thread exited: 0x0000029efdb36f10
Event: 2.147 Thread 0x0000029efdb37c30 Thread exited: 0x0000029efdb37c30
Event: 3.062 Thread 0x0000029efc181ff0 Thread added: 0x0000029efc181ff0
Event: 3.300 Thread 0x0000029efc180c40 Thread added: 0x0000029efc180c40
Event: 3.301 Thread 0x0000029eee48f7b0 Thread exited: 0x0000029eee48f7b0
Event: 3.301 Thread 0x0000029efc1812d0 Thread added: 0x0000029efc1812d0
Event: 8.601 Thread 0x0000029efc4ab0f0 Thread exited: 0x0000029efc4ab0f0
Event: 13.556 Thread 0x0000029efc71c550 Thread exited: 0x0000029efc71c550


Dynamic libraries:
0x00007ff673f30000 - 0x00007ff673f40000 	C:\Program Files\Java\jdk-21\bin\java.exe
0x00007ffc5cd30000 - 0x00007ffc5cf47000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffc5bbe0000 - 0x00007ffc5bca4000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffc5a600000 - 0x00007ffc5a9d3000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffc5a4e0000 - 0x00007ffc5a5f1000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffc41ea0000 - 0x00007ffc41eb9000 	C:\Program Files\Java\jdk-21\bin\jli.dll
0x00007ffc5cb20000 - 0x00007ffc5cbd1000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffc5c130000 - 0x00007ffc5c1d7000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffc41ac0000 - 0x00007ffc41adb000 	C:\Program Files\Java\jdk-21\bin\VCRUNTIME140.dll
0x00007ffc5aae0000 - 0x00007ffc5ab88000 	C:\WINDOWS\System32\sechost.dll
0x00007ffc5a2a0000 - 0x00007ffc5a2c8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffc5ab90000 - 0x00007ffc5aca4000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffc5c960000 - 0x00007ffc5cb11000 	C:\WINDOWS\System32\USER32.dll
0x00007ffc5a390000 - 0x00007ffc5a3b6000 	C:\WINDOWS\System32\win32u.dll
0x00007ffc44100000 - 0x00007ffc44398000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5124_none_270e8f4f7386d69d\COMCTL32.dll
0x00007ffc5c300000 - 0x00007ffc5c329000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffc59e50000 - 0x00007ffc59f6b000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffc5a440000 - 0x00007ffc5a4da000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffc52900000 - 0x00007ffc5290a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffc5c1e0000 - 0x00007ffc5c211000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffc422d0000 - 0x00007ffc422dc000 	C:\Program Files\Java\jdk-21\bin\vcruntime140_1.dll
0x00007ffc40dc0000 - 0x00007ffc40e4e000 	C:\Program Files\Java\jdk-21\bin\msvcp140.dll
0x00007ffbde0e0000 - 0x00007ffbdedf5000 	C:\Program Files\Java\jdk-21\bin\server\jvm.dll
0x00007ffc5c220000 - 0x00007ffc5c291000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffc58ce0000 - 0x00007ffc58d2d000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffc54c40000 - 0x00007ffc54c74000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffc58cc0000 - 0x00007ffc58cd3000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffc58f70000 - 0x00007ffc58f88000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffc42070000 - 0x00007ffc4207a000 	C:\Program Files\Java\jdk-21\bin\jimage.dll
0x00007ffc57860000 - 0x00007ffc57a92000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffc5c4d0000 - 0x00007ffc5c862000 	C:\WINDOWS\System32\combase.dll
0x00007ffc5a9e0000 - 0x00007ffc5aab7000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffc385c0000 - 0x00007ffc385f2000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffc5a0b0000 - 0x00007ffc5a12b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffc41930000 - 0x00007ffc4194f000 	C:\Program Files\Java\jdk-21\bin\java.dll
0x00007ffc5b340000 - 0x00007ffc5bbd6000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffc59f70000 - 0x00007ffc5a0af000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffc57d90000 - 0x00007ffc586aa000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffc5cbe0000 - 0x00007ffc5ccea000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffc5c3f0000 - 0x00007ffc5c453000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffc59d80000 - 0x00007ffc59dab000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffc1bfe0000 - 0x00007ffc1c0b7000 	C:\Program Files\Java\jdk-21\bin\jsvml.dll
0x00007ffc41c80000 - 0x00007ffc41c90000 	C:\Program Files\Java\jdk-21\bin\net.dll
0x00007ffc564f0000 - 0x00007ffc5661c000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffc593f0000 - 0x00007ffc5945a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffc41850000 - 0x00007ffc41866000 	C:\Program Files\Java\jdk-21\bin\nio.dll
0x00007ffc40fc0000 - 0x00007ffc40fd8000 	C:\Program Files\Java\jdk-21\bin\zip.dll
0x00007ffc41bd0000 - 0x00007ffc41be0000 	C:\Program Files\Java\jdk-21\bin\verify.dll
0x00007ffc40fb0000 - 0x00007ffc40fba000 	C:\Program Files\Java\jdk-21\bin\management.dll
0x00007ffc40fa0000 - 0x00007ffc40fab000 	C:\Program Files\Java\jdk-21\bin\management_ext.dll
0x00007ffc5b0d0000 - 0x00007ffc5b0d8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffc36910000 - 0x00007ffc36927000 	C:\WINDOWS\system32\napinsp.dll
0x00007ffc368d0000 - 0x00007ffc368eb000 	C:\WINDOWS\system32\pnrpnsp.dll
0x00007ffc589f0000 - 0x00007ffc58af2000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffc58980000 - 0x00007ffc589ad000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffc5c3e0000 - 0x00007ffc5c3e9000 	C:\WINDOWS\System32\NSI.dll
0x00007ffc36730000 - 0x00007ffc36741000 	C:\WINDOWS\System32\winrnr.dll
0x00007ffc366b0000 - 0x00007ffc366c5000 	C:\WINDOWS\system32\wshbth.dll
0x00007ffc36680000 - 0x00007ffc366a7000 	C:\WINDOWS\system32\nlansp_c.dll
0x00007ffc550a0000 - 0x00007ffc550aa000 	C:\Windows\System32\rasadhlp.dll
0x00007ffc548c0000 - 0x00007ffc54943000 	C:\WINDOWS\System32\fwpuclnt.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-21\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5124_none_270e8f4f7386d69d;C:\Program Files\Java\jdk-21\bin\server

VM Arguments:
java_command: c:\Users\<USER>\.vscode\extensions\redhat.vscode-apache-camel-1.11.0\jars\language-server.jar
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.vscode-apache-camel-1.11.0\jars\language-server.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 132120576                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2088763392                                {product} {ergonomic}
   size_t MaxNewSize                               = 1253048320                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2088763392                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\oraclexe\app\oracle\product\11.2.0\server\bin;;C:\Program Files (x86)\VMware\VMware Player\bin\;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\libnvvp;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Users\<USER>\Scripts\;C:\Users\<USER>\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files (x86)\Git\cmd;C:\Program Files\dotnet\;C:\xampp\php;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\composer;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Graphviz\bin;C:\Program Files\nodejs\;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.1.0\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\Scripts;;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Void\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\libnvvp;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Users\<USER>\Scripts\;C:\Users\<USER>\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files (x86)\Git\cmd;C:\Program Files\dotnet\;C:\xampp\php;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\composer;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Graphviz\bin;C:\Program Files\nodejs\;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.1.0\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;C:\Users\<USER>\Users\DELL\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin
USERNAME=DELL
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 2, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5124)
OS uptime: 0 days 0:53 hours

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 2 microcode 0x100, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c, pku
Processor Information for all 12 processors :
  Max Mhz: 2496, Current Mhz: 2496, Mhz Limit: 2496

Memory: 4k page, system-wide physical 7967M (249M free)
TotalPageFile size 9857M (AvailPageFile size 21M)
current process WorkingSet (physical memory assigned to process): 193M, peak: 211M
current process commit charge ("private bytes"): 262M, peak: 280M

vm_info: Java HotSpot(TM) 64-Bit Server VM (21.0.1+12-LTS-29) for windows-amd64 JRE (21.0.1+12-LTS-29), built on 2023-10-05T13:27:09Z by "mach5one" with MS VC++ 17.1 (VS2022)

END.
