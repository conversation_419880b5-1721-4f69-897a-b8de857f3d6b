// حالة المحادثات
let conversations = [];
let currentConversationId = null;
const codeContexts = {};
let monacoEditor = null;
let activeFileId = null;

// نظام الملفات
let workspace = {
    files: {},
    folders: {
        'root': {
            id: 'root',
            name: 'root',
            path: '/',
            type: 'folder',
            children: []
        }
    },
    currentPath: '/',
    // إضافة تتبع للملفات حسب المحادثة
    conversationFiles: {}
};

// وظائف نظام الملفات
function createFile(name, content, language, path = '/', conversationId = null) {
    // تنظيف المسار
    path = path.replace(/\/+/g, '/');
    if (!path.endsWith('/')) path += '/';
    if (!path.startsWith('/')) path = '/' + path;

    // إضافة تعليق الملف إذا لم يكن موجودًا
    content = ensureFileComment(content, language, name, path);

    const fileId = 'file_' + Date.now() + Math.random().toString(36).substr(2, 5);
    const filePath = path + name;

    workspace.files[fileId] = {
        id: fileId,
        name: name,
        path: filePath,
        content: content,
        language: language,
        type: 'file',
        // إضافة معرف المحادثة للملف
        conversationId: conversationId || currentConversationId
    };

    // إضافة الملف إلى المجلد مع التحقق من وجود المجلد
    if (!workspace.folders[path]) {
        createFolder(path);
    }
    if (!workspace.folders[path].children) {
        workspace.folders[path].children = [];
    }
    workspace.folders[path].children.push(fileId);

    // إضافة الملف إلى قائمة ملفات المحادثة
    const convId = conversationId || currentConversationId;
    if (convId) {
        if (!workspace.conversationFiles[convId]) {
            workspace.conversationFiles[convId] = [];
        }
        workspace.conversationFiles[convId].push(fileId);
    }

    return fileId;
}

function ensureFileComment(content, language, fileName, filePath) {
    // تحديد نمط التعليق حسب اللغة
    const commentStyle = language === 'python' ? '#' : '//';
    const commentPattern = new RegExp(`^${commentStyle}\\s*file:`);

    // إذا كان التعليق موجودًا بالفعل، لا تغيير
    if (commentPattern.test(content)) {
        return content;
    }

    // إضافة التعليق الجديد
    const fileComment = `${commentStyle} file: ${filePath}${fileName}\n`;
    return fileComment + content;
}

function createFolder(path) {
    const parts = path.split('/').filter(p => p);
    let currentPath = '/';

    for (const part of parts) {
        const newPath = currentPath + part + '/';
        if (!workspace.folders[newPath]) {
            const folderId = 'folder_' + Date.now() + Math.random().toString(36).substr(2, 5);
            workspace.folders[newPath] = {
                id: folderId,
                name: part,
                path: newPath,
                type: 'folder',
                children: []
            };

            // إضافة المجلد إلى المجلد الأب مع التحقق
            if (currentPath !== '/') {
                if (workspace.folders[currentPath] && workspace.folders[currentPath].children) {
                    workspace.folders[currentPath].children.push(folderId);
                }
            } else if (workspace.folders['root'] && workspace.folders['root'].children) {
                workspace.folders['root'].children.push(folderId);
            }
        }
        currentPath = newPath;
    }
}

function updateFileExplorer() {
    const explorerContent = document.getElementById('explorer-content');
    explorerContent.innerHTML = '';

    // Breadcrumb Navigation
    const breadcrumb = document.createElement('div');
    breadcrumb.className = 'breadcrumb';

    const paths = workspace.currentPath.split('/').filter(p => p);
    let currentPath = '/';

    breadcrumb.innerHTML = `<span class="breadcrumb-item" onclick="navigateToFolder('/')">root</span>`;

    paths.forEach((part, index) => {
        currentPath += part + '/';
        breadcrumb.innerHTML += `
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-item" onclick="navigateToFolder('${currentPath}')">${part}</span>
                `;
    });

    explorerContent.appendChild(breadcrumb);

    // Explorer Sections - VS Code style
    const openEditorsSection = document.createElement('div');
    openEditorsSection.className = 'explorer-section';
    openEditorsSection.innerHTML = `
                <div class="explorer-section-header">
                    <span>المحررات المفتوحة</span>
                    <div class="explorer-section-actions">
                        <button class="explorer-section-action" title="حفظ الكل"><i class="fas fa-save"></i></button>
                        <button class="explorer-section-action" title="إغلاق الكل" onclick="closeAllFiles()"><i class="fas fa-times"></i></button>
                    </div>
                </div>
                <div class="explorer-section-content" id="open-editors-content"></div>
            `;
    explorerContent.appendChild(openEditorsSection);

    // Populate Open Editors section - عرض جميع الملفات المفتوحة وليس فقط الملف النشط
    const openEditorsContent = document.getElementById('open-editors-content');
    if (openEditorsContent) {
        // الحصول على قائمة الملفات المفتوحة
        const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');

        if (openFiles.length > 0) {
            openFiles.forEach(fileId => {
                if (workspace.files[fileId]) {
                    const file = workspace.files[fileId];
                    const fileItem = document.createElement('div');
                    fileItem.className = `explorer-item file ${activeFileId === fileId ? 'active' : ''}`;

                    // تحقق مما إذا كان المحرر مفتوحًا حاليًا
                    const isEditorVisible = document.getElementById('code-executor').classList.contains('visible');

                    fileItem.innerHTML = `
                                <div class="explorer-item-content">
                                    <span class="explorer-item-icon">
                                        ${getFileIcon(file.name)}
                                    </span>
                                    <span class="explorer-item-name">${file.name}</span>
                                </div>
                                <div class="explorer-item-actions">
                                    ${!isEditorVisible && activeFileId === fileId ?
                            `<button class="explorer-item-action" onclick="reopenEditor(event)" title="إعادة فتح المحرر">
                                            <i class="fas fa-external-link-alt"></i>
                                        </button>` : ''
                        }
                                    <button class="explorer-item-action" onclick="closeFile('${file.id}', event)" title="إغلاق">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            `;
                    fileItem.onclick = (e) => {
                        if (!e.target.closest('.explorer-item-action')) {
                            // تنشيط هذا الملف
                            activeFileId = file.id;
                            updateFileTabs();

                            // إذا كان المحرر مغلقًا والملف هو النشط، نعيد فتحه
                            if (!isEditorVisible && activeFileId === fileId) {
                                reopenEditor();
                            } else {
                                // إذا كان المحرر مفتوحًا أو الملف ليس هو النشط، نفتح الملف
                                openFile(file.id);
                            }
                        }
                    };
                    openEditorsContent.appendChild(fileItem);
                }
            });
        } else {
            // إذا لم يكن هناك ملف مفتوح، نعرض رسالة
            const emptyMessage = document.createElement('div');
            emptyMessage.className = 'empty-editors-message';
            emptyMessage.innerHTML = `<span style="padding: 8px; color: var(--text-dim); font-size: 12px; display: block;">لا توجد ملفات مفتوحة</span>`;
            openEditorsContent.appendChild(emptyMessage);
        }
    }

    // Project Files Section
    const projectSection = document.createElement('div');
    projectSection.className = 'explorer-section';
    projectSection.innerHTML = `
                <div class="explorer-section-header">
                    <span>${paths.length > 0 ? paths[paths.length - 1].toUpperCase() : 'مساحة العمل'}</span>
                    <div class="explorer-section-actions">
                        <button class="explorer-section-action" onclick="createNewFile()" title="ملف جديد"><i class="fas fa-file"></i></button>
                        <button class="explorer-section-action" onclick="createNewFolder()" title="مجلد جديد"><i class="fas fa-folder"></i></button>
                        <button class="explorer-section-action" onclick="refreshFileExplorer()" title="تحديث المستكشف"><i class="fas fa-sync"></i></button>
                    </div>
                </div>
                <div class="explorer-section-content" id="project-files-content"></div>
            `;
    explorerContent.appendChild(projectSection);

    // Populate Project Files section
    const projectFilesContent = document.getElementById('project-files-content');
    const currentFolder = workspace.folders[workspace.currentPath] || workspace.folders['root'];

    if (currentFolder && Array.isArray(currentFolder.children)) {
        // Sort: Folders first, then files alphabetically
        const sortedItems = [...currentFolder.children].sort((a, b) => {
            const isAFolder = a.startsWith('folder_');
            const isBFolder = b.startsWith('folder_');

            if (isAFolder && !isBFolder) return -1;
            if (!isAFolder && isBFolder) return 1;

            // Sort by name if both are folders or both are files
            const itemA = isAFolder ?
                Object.values(workspace.folders).find(f => f && f.id === a) :
                workspace.files[a];
            const itemB = isBFolder ?
                Object.values(workspace.folders).find(f => f && f.id === b) :
                workspace.files[b];

            if (itemA && itemB) {
                return itemA.name.localeCompare(itemB.name);
            }
            return 0;
        });

        sortedItems.forEach(childId => {
            let item;
            if (childId.startsWith('folder_')) {
                const folderPath = Object.values(workspace.folders).find(f => f && f.id === childId);
                item = folderPath ? workspace.folders[folderPath.path] : null;
            } else {
                item = workspace.files[childId];
            }

            if (!item) return;

            const itemElement = document.createElement('div');
            itemElement.className = `explorer-item ${item.type} ${activeFileId === item.id ? 'active' : ''}`;
            itemElement.innerHTML = `
                        <div class="explorer-item-content">
                            <span class="explorer-item-icon" style="order: -1">
                                ${item.type === 'folder' ? '<i class="fas fa-folder"></i>' : getFileIcon(item.name)}
                        </span>
                        <span class="explorer-item-name">${item.name}</span>
                        </div>
                        <div class="explorer-item-actions">
                            <button class="explorer-item-action" onclick="deleteFile('${item.id}', event)" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                            ${item.type === 'file' ? `
                            <button class="explorer-item-action" onclick="renameFile('${item.id}', event)" title="Rename">
                                <i class="fas fa-edit"></i>
                            </button>
                            ` : ''}
                        </div>
                    `;

            itemElement.onclick = (e) => {
                if (!e.target.closest('.explorer-item-action')) {
                    if (item.type === 'folder') {
                        navigateToFolder(item.path);
                    } else {
                        openFile(item.id);
                    }
                }
            };

            projectFilesContent.appendChild(itemElement);
        });
    }
}

// Helper function to get appropriate file icon based on extension
function getFileIcon(filename) {
    const extension = filename.split('.').pop().toLowerCase();
    const iconMap = {
        'html': '<i class="fas fa-file-code" style="color: #e44d26;"></i>',
        'css': '<i class="fas fa-file-code" style="color: #264de4;"></i>',
        'js': '<i class="fas fa-file-code" style="color: #f7df1e;"></i>',
        'json': '<i class="fas fa-file-code" style="color: #f7df1e;"></i>',
        'ts': '<i class="fas fa-file-code" style="color: #007acc;"></i>',
        'py': '<i class="fas fa-file-code" style="color: #306998;"></i>',
        'php': '<i class="fas fa-file-code" style="color: #777bb4;"></i>',
        'md': '<i class="fas fa-file-alt" style="color: #03a9f4;"></i>',
        'txt': '<i class="fas fa-file-alt" style="color: #9e9e9e;"></i>',
        'jpg': '<i class="fas fa-file-image" style="color: #4caf50;"></i>',
        'jpeg': '<i class="fas fa-file-image" style="color: #4caf50;"></i>',
        'png': '<i class="fas fa-file-image" style="color: #4caf50;"></i>',
        'gif': '<i class="fas fa-file-image" style="color: #4caf50;"></i>',
        'svg': '<i class="fas fa-file-image" style="color: #ff9800;"></i>',
        'pdf': '<i class="fas fa-file-pdf" style="color: #f44336;"></i>',
        'zip': '<i class="fas fa-file-archive" style="color: #ffc107;"></i>',
        'rar': '<i class="fas fa-file-archive" style="color: #ffc107;"></i>'
    };

    return iconMap[extension] || '<i class="fas fa-file-code" style="color: #75beff;"></i>';
}

// Function to refresh the file explorer
function refreshFileExplorer() {
    updateFileExplorer();
}

function navigateToFolder(path) {
    workspace.currentPath = path;
    updateFileExplorer();
}

// Add the VSCode-style status bar
function addStatusBar() {
    // Check if status bar already exists
    if (document.querySelector('.status-bar')) return;

    const codeExecutor = document.getElementById('code-executor');
    if (!codeExecutor) return;

    const statusBar = document.createElement('div');
    statusBar.className = 'status-bar';
    statusBar.innerHTML = `
        <div class="status-items-left">
            <div class="status-item icon-only-on-small">
                <i class="fas fa-code-branch"></i>
                <span class="status-item-text">main</span>
            </div>
            <div class="status-item hide-on-small">
                <i class="fas fa-sync"></i>
            </div>
            <div class="status-item terminal-toggle-btn">
                <i class="fas fa-terminal"></i>
                <span class="status-item-text">Terminal</span>
            </div>
            <div class="status-item hide-on-tiny">
                <i class="fas fa-bell"></i>
            </div>
        </div>
        <div class="status-items-right">
            <div class="status-item cursor-position always-show-text">
                <span class="status-item-text">Ln 1, Col 1</span>
            </div>
            <div class="status-item indent-setting icon-only-on-small">
                <i class="fas fa-indent"></i>
                <span class="status-item-text">Spaces: 4</span>
            </div>
            <div class="status-item hide-on-small">UTF-8</div>
            <div class="status-item language-indicator">
                <i class="fas fa-file-code"></i>
                <span class="status-item-text">JavaScript</span>
            </div>
            <div class="status-item hide-on-small">
                <i class="fas fa-check-circle"></i>
                <span class="status-item-text">Prettier</span>
            </div>
        </div>
    `;
    codeExecutor.appendChild(statusBar);

    // Make status items interactive
    statusBar.querySelectorAll('.status-item').forEach(item => {
        item.addEventListener('click', function() {
            // Show a tooltip or perform an action when clicked
            if (this.classList.contains('indent-setting')) {
                const options = ['Spaces: 2', 'Spaces: 4', 'Tabs: 4'];
                const currentIndex = options.findIndex(opt => {
                    const text = this.querySelector('.status-item-text');
                    return text && opt === text.textContent;
                });
                const nextIndex = (currentIndex + 1) % options.length;
                const text = this.querySelector('.status-item-text');
                if (text) {
                    text.textContent = options[nextIndex];
                }

                // Also update editor if available
                if (monacoEditor && monacoEditor.updateOptions) {
                    const tabSize = parseInt(options[nextIndex].split(':')[1]);
                    monacoEditor.updateOptions({
                        tabSize,
                        insertSpaces: options[nextIndex].startsWith('Spaces')
                    });
                }
            } else if (this.classList.contains('terminal-toggle-btn')) {
                // Toggle terminal visibility
                const executorFooter = document.querySelector('.executor-footer');
                if (executorFooter) {
                    if (executorFooter.classList.contains('hidden')) {
                        executorFooter.classList.remove('hidden');
                        localStorage.setItem('terminalState', 'open');
                    } else if (executorFooter.classList.contains('collapsed')) {
                        executorFooter.classList.remove('collapsed');
                        localStorage.setItem('terminalState', 'open');
                    } else {
                        executorFooter.classList.add('hidden');
                        localStorage.setItem('terminalState', 'hidden');
                    }
                }
            }
        });
    });

    // Update the language indicator based on current file
    const currentFile = workspace.files[activeFileId];
    if (currentFile && currentFile.language) {
        const langIndicator = statusBar.querySelector('.language-indicator .status-item-text');
        if (langIndicator) {
            langIndicator.textContent = currentFile.language.charAt(0).toUpperCase() + currentFile.language.slice(1);
        }
    }
    
    // Adjust status bar for screen size
    updateStatusBarResponsiveness();
    
    // Add window resize listener for responsive status bar
    window.addEventListener('resize', updateStatusBarResponsiveness);
}

// Function to update status bar based on screen width
function updateStatusBarResponsiveness() {
    const statusBar = document.querySelector('.status-bar');
    if (!statusBar) return;
    
    const width = window.innerWidth;
    
    // Very small screens - show only essential items
    if (width < 400) {
        statusBar.querySelectorAll('.status-item:not(.always-show-text)').forEach(item => {
            const text = item.querySelector('.status-item-text');
            if (text) text.style.display = 'none';
        });
    } 
    // Small screens - show icons and some text
    else if (width < 600) {
        statusBar.querySelectorAll('.status-item.icon-only-on-small .status-item-text').forEach(text => {
            text.style.display = 'none';
        });
        statusBar.querySelectorAll('.status-item:not(.icon-only-on-small):not(.hide-on-small) .status-item-text').forEach(text => {
            text.style.display = '';
        });
    } 
    // Larger screens - show everything
    else {
        statusBar.querySelectorAll('.status-item-text').forEach(text => {
            text.style.display = '';
        });
    }
}

function openFile(fileId) {
    const file = workspace.files[fileId];
    if (!file) return;

    // تحديث الملف النشط
    activeFileId = fileId;

    // إضافة الملف إلى قائمة الملفات المفتوحة إذا لم يكن موجودًا
    const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
    if (!openFiles.includes(fileId)) {
        openFiles.push(fileId);
        localStorage.setItem('openFiles', JSON.stringify(openFiles));
    }

    const editorContainer = document.getElementById('editor-container');

    if (typeof monaco === 'undefined') {
        console.error('Monaco Editor not loaded. Please wait...');
        editorContainer.innerHTML = '<div style="padding: 20px; color: #fff;">Loading editor...</div>';
        setTimeout(() => openFile(fileId), 1000);
        return;
    }

    if (!monacoEditor) {
        try {
            const model = monaco.editor.createModel(
                file.content || '',
                file.language || 'plaintext'
            );

            // VS Code-like options
            monacoEditor = monaco.editor.create(editorContainer, {
                model: model,
                theme: 'vs-dark',
                automaticLayout: true,
                fontSize: 14,
                lineNumbers: 'on',
                minimap: { enabled: true },
                scrollBeyondLastLine: false,
                renderLineHighlight: 'all',
                cursorBlinking: 'smooth',
                cursorSmoothCaretAnimation: true,
                smoothScrolling: true,
                wordWrap: 'on',
                formatOnPaste: true,
                formatOnType: true,
                suggest: {
                    showMethods: true,
                    showFunctions: true,
                    showConstructors: true,
                    showFields: true,
                    showVariables: true,
                    showClasses: true,
                    showStructs: true,
                    showInterfaces: true,
                    showModules: true,
                    showProperties: true,
                    showEvents: true,
                    showOperators: true,
                    showUnits: true,
                    showValues: true,
                    showConstants: true,
                    showEnums: true,
                    showEnumMembers: true,
                    showKeywords: true,
                    showWords: true,
                    showColors: true,
                    showFiles: true,
                    showReferences: true,
                    showFolders: true,
                    showTypeParameters: true,
                    showIssues: true,
                    showUsers: true,
                    showSnippets: true
                }
            });

            // Set direction to LTR for code
            if (monacoEditor.updateOptions) {
                monacoEditor.updateOptions({ direction: 'ltr' });
            }

            if (monacoEditor.getDomNode) {
                monacoEditor.getDomNode().style.direction = 'ltr';
            }

            // Update content when changed
            if (monacoEditor.onDidChangeModelContent) {
                monacoEditor.onDidChangeModelContent(function () {
                    if (fileId && workspace.files[fileId]) {
                        workspace.files[fileId].content = monacoEditor.getValue();
                    }
                });
            }

            // Add cursor position tracking (VS Code status bar)
            monacoEditor.onDidChangeCursorPosition(function (e) {
                const statusItems = document.querySelectorAll('.status-items-right .status-item');
                if (statusItems.length > 0) {
                    statusItems[0].textContent = `Ln ${e.position.lineNumber}, Col ${e.position.column}`;
                }
            });

            // Add quick action buttons (like VS Code)
            const quickActions = document.createElement('div');
            quickActions.className = 'quick-actions';
            quickActions.innerHTML = `
                        <div class="quick-action" title="Split Editor"><i class="fas fa-columns"></i></div>
                        <div class="quick-action" title="More Options"><i class="fas fa-ellipsis-v"></i></div>
                    `;
            editorContainer.appendChild(quickActions);

        } catch (e) {
            console.error('Error initializing Monaco Editor:', e);
            editorContainer.innerHTML = '<div style="padding: 20px; color: #fff;">Error loading editor: ' + e.message + '</div>';
        }
    } else {
        try {
            // تحقق من وجود النماذج
            let model = null;
            const modelUri = monaco.Uri.parse('inmemory://' + fileId);

            // البحث عن النموذج الحالي
            const existingModels = monaco.editor.getModels();
            model = existingModels.find(m => m.uri && m.uri.toString() === modelUri.toString());

            // إنشاء نموذج جديد إذا لم يكن موجودًا
            if (!model) {
                try {
                    model = monaco.editor.createModel(
                        file.content || '',
                        file.language || 'plaintext',
                        modelUri
                    );
                } catch (e) {
                    console.warn('Error creating model, trying to reuse existing model:', e);
                    // محاولة استخدام النموذج الحالي للمحرر
                    model = monacoEditor.getModel();
                    if (model) {
                        model.setValue(file.content || '');
                        try {
                            if (file.language) {
                                monaco.editor.setModelLanguage(model, file.language);
                            }
                        } catch (langError) {
                            console.warn('Could not set language:', langError);
                        }
                    } else {
                        // إنشاء نموذج جديد بدون URI محدد
                        model = monaco.editor.createModel(file.content || '');
                    }
                }
            } else {
                // تحديث النموذج الموجود
                model.setValue(file.content || '');
                try {
                    if (file.language) {
                        monaco.editor.setModelLanguage(model, file.language);
                    }
                } catch (langError) {
                    console.warn('Could not set language:', langError);
                }
            }

            // تعيين النموذج للمحرر
            monacoEditor.setModel(model);

            // تحديث مؤشر اللغة في شريط الحالة
            try {
                if (file.language) {
                    const statusItems = document.querySelectorAll('.status-items-right .status-item');
                    if (statusItems.length > 3) {
                        statusItems[3].textContent = file.language.charAt(0).toUpperCase() + file.language.slice(1);
                    }
                }
            } catch (e) {
                console.warn('Could not update language indicator:', e);
            }
        } catch (e) {
            console.error('Error changing editor model:', e);
        }
    }

    updateFileTabs();
    document.getElementById('code-executor').classList.add('visible');

    // Add VS Code status bar
    addStatusBar();

    // Focus editor after opening
    setTimeout(() => {
        if (monacoEditor && monacoEditor.focus) {
            try {
                monacoEditor.focus();
            } catch (e) {
                console.warn('Could not focus editor:', e);
            }
        }
    }, 100);
}

function renameFile(fileId, event) {
    event.stopPropagation();
    const file = workspace.files[fileId];
    if (!file) return;

    const newName = prompt('أدخل الاسم الجديد:', file.name);
    if (newName && newName !== file.name) {
        file.name = newName;
        updateFileExplorer();
    }
}

function deleteFile(itemId, event) {
    event.stopPropagation();
    if (!confirm('هل أنت متأكد من حذف هذا العنصر؟')) return;

    if (itemId.startsWith('folder_')) {
        const folderObj = Object.values(workspace.folders).find(f => f && f.id === itemId);
        if (folderObj) {
            delete workspace.folders[folderObj.path];
        }
    } else {
        // إذا كان الملف المحذوف هو الملف المفتوح حاليًا، نغلق المحرر
        if (activeFileId === itemId) {
            hideCodeExecutor();
        }

        delete workspace.files[itemId];
    }

    // إزالة العنصر من المجلد الأب مع التحقق
    for (const folder of Object.values(workspace.folders)) {
        if (folder && Array.isArray(folder.children)) {
            const index = folder.children.indexOf(itemId);
            if (index !== -1) {
                folder.children.splice(index, 1);
                break;
            }
        }
    }

    updateFileExplorer();
    updateFileTabs();
}

function toggleExplorer() {
    const explorer = document.getElementById('file-explorer');
    const toggle = document.getElementById('explorer-toggle');

    explorer.classList.toggle('visible');
    toggle.classList.toggle('active');
}

function createNewFile() {
    const fileName = prompt('أدخل اسم الملف (مثال: index.html):');
    if (fileName) {
        const lang = fileName.split('.').pop();
        const fileId = createFile(fileName, '', lang, workspace.currentPath);
        openFile(fileId);
        updateFileExplorer();
    }
}

function createNewFolder() {
    const folderName = prompt('أدخل اسم المجلد:');
    if (folderName) {
        const newPath = workspace.currentPath + folderName + '/';
        createFolder(newPath);
        updateFileExplorer();
    }
}

function uploadFile() {
    document.getElementById('file-upload').click();
}

function handleFileUpload(event) {
    const files = event.target.files;
    if (!files.length) return;

    Array.from(files).forEach(file => {
        const reader = new FileReader();
        reader.onload = (e) => {
            const content = e.target.result;
            const lang = file.name.split('.').pop();
            createFile(file.name, content, lang, workspace.currentPath);
            updateFileExplorer();
        };
        reader.readAsText(file);
    });
}

// وظائف لواجهة تشغيل الأكواد
function showCodeExecutor(codeBlock, suggestedName, lang, content) {
    const executor = document.getElementById('code-executor');
    const editorContainer = document.getElementById('editor-container');
    
    // Create file if it doesn't exist
    const fileId = createFile(suggestedName, content.trim(), lang || 'javascript');
    activeFileId = fileId;

    // إضافة الملف إلى قائمة الملفات المفتوحة
    const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
    if (!openFiles.includes(fileId)) {
        openFiles.push(fileId);
        localStorage.setItem('openFiles', JSON.stringify(openFiles));
    }

    if (typeof monaco === 'undefined') {
        console.log('Monaco Editor not loaded yet. Waiting...');
        editorContainer.innerHTML = '<div style="padding: 20px; color: #fff;">Loading editor...</div>';

        setTimeout(() => {
            showCodeExecutor(codeBlock, suggestedName, lang, content);
        }, 1000);
        return;
    }

    openFile(fileId);
    
    // Ensure terminal element exists
    let terminalContainer = document.querySelector('.executor-footer');
    if (!terminalContainer) {
        console.warn('Terminal container not found, creating it');
        terminalContainer = document.createElement('div');
        terminalContainer.className = 'executor-footer';
        executor.appendChild(terminalContainer);
        
        // Create terminal header
        const terminalHeader = document.createElement('div');
        terminalHeader.className = 'terminal-header';
        terminalHeader.innerHTML = `
            <span>النتائج</span>
            <button onclick="clearExecutorResult()">مسح</button>
        `;
        terminalContainer.appendChild(terminalHeader);
        
        // Create terminal output area
        const terminalOutput = document.createElement('div');
        terminalOutput.className = 'terminal';
        terminalOutput.id = 'executor-result';
        terminalContainer.appendChild(terminalOutput);
    }
    
    updateTerminalHeader();
    addStatusBar();

    // Update language indicator in status bar
    if (lang) {
        const langIndicator = document.querySelector('.language-indicator .status-item-text');
        if (langIndicator) {
            langIndicator.textContent = lang.charAt(0).toUpperCase() + lang.slice(1);
        }
    }

    executor.classList.add('visible');
}

// تحسين وظائف التيرمنال
function updateTerminalHeader() {
    const terminalHeader = document.querySelector('.terminal-header');
    if (terminalHeader) {
        terminalHeader.innerHTML = `
            <div class="terminal-tabs">
                <div class="terminal-tab active" data-tab="terminal">TERMINAL</div>
                <div class="terminal-tab" data-tab="output">OUTPUT</div>
                <div class="terminal-tab" data-tab="problems">PROBLEMS</div>
                <div class="terminal-tab" data-tab="debug">DEBUG CONSOLE</div>
            </div>
            <div class="terminal-actions">
                <button title="Clear Terminal" onclick="clearExecutorResult()"><i class="fas fa-trash-alt"></i></button>
                <button title="Kill Terminal" onclick="killTerminal()"><i class="fas fa-times-circle"></i></button>
                <button title="New Terminal"><i class="fas fa-plus"></i></button>
                <button title="Split Terminal"><i class="fas fa-columns"></i></button>
                <button title="Toggle Terminal" onclick="toggleTerminal()"><i class="fas fa-chevron-down"></i></button>
                <button title="Maximize Terminal" onclick="maximizeTerminal()"><i class="fas fa-expand-alt"></i></button>
                <button title="Close Terminal" onclick="hideTerminal()"><i class="fas fa-times"></i></button>
            </div>
        `;

        // جعل التبويبات قابلة للنقر
        const tabs = terminalHeader.querySelectorAll('.terminal-tab');
        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                tabs.forEach(t => t.classList.remove('active'));
                this.classList.add('active');

                // تحديث محتوى التيرمنال بناءً على التبويب النشط
                const tabName = this.dataset.tab;
                const terminalElem = document.getElementById('executor-result');

                switch(tabName) {
                    case 'problems':
                        terminalElem.innerHTML = '<div class="terminal-message success">No problems have been detected in the workspace.</div>';
                        break;
                    case 'output':
                        terminalElem.innerHTML = '<div class="terminal-message">Output channel is empty.</div>';
                        break;
                    case 'debug':
                        terminalElem.innerHTML = '<div class="terminal-message">Debug console is available in debug mode.</div>';
                        break;
                    case 'terminal':
                    default:
                        // استعادة محتوى التيرمنال الأصلي إذا كان موجوداً
                        if (window._terminalContent) {
                            terminalElem.innerHTML = window._terminalContent;
                        } else {
                            terminalElem.innerHTML = '<div class="terminal-welcome">Terminal ready.</div>';
                        }
                }
            });
        });
    }
}

// حفظ محتوى التيرمنال قبل تغيير التبويب
function saveTerminalContent() {
    const terminalElem = document.getElementById('executor-result');
    if (terminalElem && terminalElem.innerHTML.trim() !== '') {
        window._terminalContent = terminalElem.innerHTML;
    }
}

// إخفاء/إظهار التيرمنال بشكل كامل
function toggleTerminal() {
    const executorFooter = document.querySelector('.executor-footer');
    if (executorFooter) {
        if (executorFooter.classList.contains('collapsed')) {
            // إذا كان مطوياً، نفتحه
            executorFooter.classList.remove('collapsed');
            localStorage.setItem('terminalState', 'open');
        } else if (executorFooter.classList.contains('hidden')) {
            // إذا كان مخفياً، نظهره
            executorFooter.classList.remove('hidden');
            localStorage.setItem('terminalState', 'open');
        } else {
            // إذا كان مفتوحاً، نطويه
            executorFooter.classList.add('collapsed');
            localStorage.setItem('terminalState', 'collapsed');
        }
    }
}

// إخفاء التيرمنال تماماً
function hideTerminal() {
    const executorFooter = document.querySelector('.executor-footer');
    if (executorFooter) {
        executorFooter.classList.add('hidden');
        localStorage.setItem('terminalState', 'hidden');
    }
}

// تكبير التيرمنال
function maximizeTerminal() {
    const executorFooter = document.querySelector('.executor-footer');
    if (executorFooter) {
        executorFooter.classList.toggle('maximized');
        if (executorFooter.classList.contains('maximized')) {
            localStorage.setItem('terminalState', 'maximized');
        } else {
            localStorage.setItem('terminalState', 'open');
        }
    }
}

// إيقاف عمليات التيرمنال
function killTerminal() {
    const terminalElem = document.getElementById('executor-result');
    if (terminalElem) {
        saveTerminalContent();
        terminalElem.innerHTML += '<div class="terminal-message error">Process terminated.</div>';
    }
}

// استعادة حالة التيرمنال عند تحميل الصفحة
function restoreTerminalState() {
    const state = localStorage.getItem('terminalState') || 'open';
    const executorFooter = document.querySelector('.executor-footer');
    
    if (executorFooter) {
        // إزالة جميع الحالات أولاً
        executorFooter.classList.remove('collapsed', 'hidden', 'maximized');
        executorFooter.style.height = '';
        
        // تطبيق الحالة المحفوظة
        switch (state) {
            case 'collapsed':
                executorFooter.classList.add('collapsed');
                break;
            case 'hidden':
                executorFooter.classList.add('hidden');
                break;
            case 'maximized':
                executorFooter.classList.add('maximized');
                break;
            case 'custom':
                const savedHeight = localStorage.getItem('terminalHeight');
                if (savedHeight) {
                    executorFooter.style.height = savedHeight + 'px';
                }
                break;
        }
    }
}

// إضافة زر لفتح/إغلاق التيرمنال في شريط الحالة
function addTerminalToggleButton() {
    // تحقق مما إذا كان شريط الحالة موجوداً
    const statusBar = document.querySelector('.status-bar');
    if (!statusBar) return;
    
    // تحقق مما إذا كان الزر موجوداً بالفعل
    if (statusBar.querySelector('.terminal-toggle-btn')) return;
    
    // إنشاء زر التبديل
    const toggleBtn = document.createElement('div');
    toggleBtn.className = 'status-item terminal-toggle-btn';
    toggleBtn.innerHTML = '<i class="fas fa-terminal"></i> Terminal';
    toggleBtn.title = 'Toggle Terminal (Ctrl+`)';
    toggleBtn.onclick = function() {
        const executorFooter = document.querySelector('.executor-footer');
        if (executorFooter) {
            if (executorFooter.classList.contains('hidden')) {
                executorFooter.classList.remove('hidden');
                localStorage.setItem('terminalState', 'open');
            } else if (executorFooter.classList.contains('collapsed')) {
                executorFooter.classList.remove('collapsed');
                localStorage.setItem('terminalState', 'open');
            } else {
                executorFooter.classList.add('hidden');
                localStorage.setItem('terminalState', 'hidden');
            }
        }
    };
    
    // إضافة الزر إلى شريط الحالة
    const leftItems = statusBar.querySelector('.status-items-left');
    if (leftItems) {
        leftItems.appendChild(toggleBtn);
    } else {
        statusBar.appendChild(toggleBtn);
    }
}

// إضافة مقبض تغيير حجم التيرمنال
function addTerminalResizer() {
    const executorFooter = document.querySelector('.executor-footer');
    if (!executorFooter) return;
    
    // تحقق مما إذا كان المقبض موجوداً بالفعل
    if (executorFooter.querySelector('.terminal-resizer')) return;
    
    // إنشاء مقبض التغيير
    const resizer = document.createElement('div');
    resizer.className = 'terminal-resizer';
    executorFooter.appendChild(resizer);
    
    // تفعيل وظيفة السحب
    let startY, startHeight;
    
    resizer.addEventListener('mousedown', function(e) {
        startY = e.clientY;
        startHeight = parseInt(getComputedStyle(executorFooter).height);
        document.addEventListener('mousemove', doDrag, false);
        document.addEventListener('mouseup', stopDrag, false);
        document.body.style.cursor = 'row-resize';
        e.preventDefault();
    });
    
    function doDrag(e) {
        // حساب الارتفاع الجديد (السحب لأعلى يقلل الارتفاع)
        const newHeight = startHeight - (e.clientY - startY);
        // تحديد حد أدنى وأقصى للارتفاع
        const minHeight = 100; // الحد الأدنى للارتفاع
        const maxHeight = window.innerHeight * 0.8; // 80% من ارتفاع النافذة
        
        if (newHeight > minHeight && newHeight < maxHeight) {
            executorFooter.style.height = newHeight + 'px';
            executorFooter.classList.remove('collapsed', 'maximized');
            localStorage.setItem('terminalHeight', newHeight);
            localStorage.setItem('terminalState', 'custom');
        }
    }
    
    function stopDrag() {
        document.removeEventListener('mousemove', doDrag, false);
        document.removeEventListener('mouseup', stopDrag, false);
        document.body.style.cursor = '';
    }
}

// تحديث دالة openFile لإضافة مقبض تغيير الحجم
const originalOpenFile = openFile;
openFile = function(fileId) {
    originalOpenFile(fileId);
    
    // إضافة زر التيرمنال ومقبض تغيير الحجم بعد فتح الملف
    setTimeout(() => {
        addTerminalToggleButton();
        addTerminalResizer();
        restoreTerminalState();
        updateTerminalHeader();
    }, 200);
};

// تحديث دالة addStatusBar لتضيف زر التيرمنال
const originalAddStatusBar = addStatusBar;
addStatusBar = function() {
    originalAddStatusBar();
    addTerminalToggleButton();
};

// تعديل دالة clearExecutorResult للحفاظ على تنسيق التيرمنال
function clearExecutorResult() {
    const resultElement = document.getElementById('executor-result');
    if (resultElement) {
        resultElement.innerHTML = '<div class="terminal-welcome">Terminal cleared.</div>';
        window._terminalContent = resultElement.innerHTML;
    }
}

// إضافة اختصار لوحة المفاتيح للتيرمنال (Ctrl+`)
document.addEventListener('keydown', function(e) {
    // Ctrl+` لفتح/إغلاق التيرمنال
    if (e.ctrlKey && (e.key === '`' || e.key === 'Backquote')) {
        const executorFooter = document.querySelector('.executor-footer');
        if (executorFooter) {
            if (executorFooter.classList.contains('hidden')) {
                executorFooter.classList.remove('hidden');
                localStorage.setItem('terminalState', 'open');
            } else {
                executorFooter.classList.toggle('collapsed');
                localStorage.setItem('terminalState', 
                    executorFooter.classList.contains('collapsed') ? 'collapsed' : 'open');
            }
            e.preventDefault();
        }
    }
});

function updateFileTabs() {
    const tabsContainer = document.getElementById('file-tabs');
    tabsContainer.innerHTML = '';

    // تعديل: عرض جميع الملفات المفتوحة بدلاً من الملف النشط فقط
    // نحتفظ بقائمة من الملفات المفتوحة
    const openFiles = [];

    // إذا كان هناك ملف نشط، نضيفه أولاً
    if (activeFileId && workspace.files[activeFileId]) {
        const activeFile = workspace.files[activeFileId];
        // نتحقق إذا كان الملف النشط ينتمي للمحادثة الحالية أو ليس له محادثة محددة
        if (!activeFile.conversationId || activeFile.conversationId === currentConversationId) {
            openFiles.push(activeFileId);
        }
    }

    // نضيف باقي الملفات المفتوحة من localStorage
    const savedOpenFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
    savedOpenFiles.forEach(fileId => {
        if (workspace.files[fileId] && !openFiles.includes(fileId)) {
            const file = workspace.files[fileId];
            // نتحقق إذا كان الملف ينتمي للمحادثة الحالية أو ليس له محادثة محددة
            if (!file.conversationId || file.conversationId === currentConversationId) {
                openFiles.push(fileId);
            }
        }
    });

    // عرض جميع الملفات المفتوحة كتبويبات
    openFiles.forEach(fileId => {
        const file = workspace.files[fileId];
        const tab = document.createElement('div');
        tab.className = `file-tab ${fileId === activeFileId ? 'active' : ''}`;

        // تحقق مما إذا كان المحرر مفتوحاً حاليًا
        const isEditorVisible = document.getElementById('code-executor').classList.contains('visible');

        tab.innerHTML = `
                    <span class="file-tab-name">${file.name}</span>
                    ${!isEditorVisible && fileId === activeFileId ?
                `<span class="file-tab-open" onclick="reopenEditor(event)" title="إعادة فتح المحرر">
                            <i class="fas fa-external-link-alt"></i>
                        </span>` : ''
            }
                    <span class="file-tab-close" onclick="closeFile('${file.id}', event)">×</span>
                `;
        tab.onclick = (e) => {
            if (!e.target.closest('.file-tab-close') && !e.target.closest('.file-tab-open')) {
                // تنشيط هذا الملف
                activeFileId = file.id;
                updateFileTabs();

                // إذا كان المحرر مغلقًا، نعيد فتحه
                if (!document.getElementById('code-executor').classList.contains('visible')) {
                    reopenEditor();
                } else {
                    // إذا كان المحرر مفتوحًا، نقوم بتحميل هذا الملف
                    openFile(file.id);
                }
            }
        };
        tabsContainer.appendChild(tab);
    });

    // حفظ الملفات المفتوحة في localStorage
    localStorage.setItem('openFiles', JSON.stringify(openFiles));

    // تمرير التبويب النشط إلى منطقة العرض
    setTimeout(() => {
        const activeTab = tabsContainer.querySelector('.file-tab.active');
        if (activeTab) {
            activeTab.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
        }
    }, 100);

    // Call our addStatusBar function instead of duplicating code
    addStatusBar();
}

// دالة لإعادة فتح المحرر للملف النشط
function reopenEditor(event) {
    if (event) {
        event.stopPropagation();
    }

    if (activeFileId && workspace.files[activeFileId]) {
        const file = workspace.files[activeFileId];

        // إعادة فتح المحرر بالملف النشط
        document.getElementById('code-executor').classList.add('visible');

        // تحديث المحرر بالمحتوى الحالي
        if (monacoEditor) {
            try {
                // إيجاد أو إنشاء نموذج
                let model = null;
                const existingModels = monaco.editor.getModels();

                if (existingModels && existingModels.length > 0) {
                    model = existingModels.find(m => m.uri && m.uri.path === '/' + activeFileId);
                }

                if (!model) {
                    try {
                        model = monaco.editor.createModel(
                            file.content || '',
                            file.language || 'plaintext',
                            monaco.Uri.parse('inmemory://' + activeFileId)
                        );
                    } catch (e) {
                        console.error('Error creating model:', e);
                        model = monaco.editor.createModel(file.content || '');
                    }
                }

                // تعيين النموذج وتحديثه
                monacoEditor.setModel(model);
                monacoEditor.setValue(file.content || '');

                // محاولة تعيين اللغة
                try {
                    if (file.language && monaco.editor.setModelLanguage) {
                        monaco.editor.setModelLanguage(model, file.language);

                        // تحديث مؤشر اللغة في شريط الحالة
                        const statusItems = document.querySelectorAll('.status-items-right .status-item');
                        if (statusItems.length > 3) {
                            statusItems[3].textContent = file.language.charAt(0).toUpperCase() + file.language.slice(1);
                        }
                    }
                } catch (e) {
                    console.warn('Could not set language:', e);
                }

                // تركيز المحرر
                setTimeout(() => {
                    if (monacoEditor && monacoEditor.focus) {
                        try {
                            monacoEditor.focus();
                        } catch (e) {
                            console.warn('Could not focus editor:', e);
                        }
                    }
                }, 100);
            } catch (e) {
                console.error('Error reopening editor:', e);
            }
        } else {
            // إذا لم يكن المحرر موجودًا، نستخدم openFile
            openFile(activeFileId);
        }

        // تحديث علامات التبويب
        updateFileTabs();
    }
}

// إضافة أنماط CSS للزر الجديد
document.addEventListener('DOMContentLoaded', function () {
    const style = document.createElement('style');
    style.textContent = `
                .file-tab-open {
                    margin-left: 8px;
                    width: 16px;
                    height: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 3px;
                    color: var(--text-dim);
                    font-size: 12px;
                    opacity: 0.7;
                    transition: all 0.2s ease;
                    cursor: pointer;
                }

                .file-tab:hover .file-tab-open {
                    opacity: 1;
                }

                .file-tab-open:hover {
                    color: white;
                    background: rgba(94, 53, 177, 0.5);
                }
            `;
    document.head.appendChild(style);
});

function closeFile(fileId, event) {
    if (event) {
        event.stopPropagation();
    }

    // إزالة الملف من قائمة الملفات المفتوحة
    const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
    const updatedOpenFiles = openFiles.filter(id => id !== fileId);
    localStorage.setItem('openFiles', JSON.stringify(updatedOpenFiles));

    // إذا كان الملف المغلق هو الملف النشط
    if (activeFileId === fileId) {
        // إذا كان هناك ملفات مفتوحة أخرى، نجعل الملف الأول هو النشط
        if (updatedOpenFiles.length > 0) {
            activeFileId = updatedOpenFiles[0];

            // إذا كان المحرر مفتوحًا، نفتح الملف الجديد
            if (document.getElementById('code-executor').classList.contains('visible')) {
                openFile(activeFileId);
            }
        } else {
            // إذا لم يكن هناك ملفات مفتوحة أخرى، نغلق المحرر
            activeFileId = null;
            document.getElementById('code-executor').classList.remove('visible');

            // تنظيف iframe التنفيذ
            if (window._executorIframe && window._executorIframe.parentNode) {
                window._executorIframe.parentNode.removeChild(window._executorIframe);
                window._executorIframe = null;
            }
        }
    }

    updateFileTabs();
    updateFileExplorer();
}

function hideCodeExecutor() {
    document.getElementById('code-executor').classList.remove('visible');
    // لا نقوم بإلغاء تعيين activeFileId هنا لكي تبقى التبويبة نشطة
    // activeFileId = null;

    // تنظيف iframe التنفيذ
    if (window._executorIframe && window._executorIframe.parentNode) {
        window._executorIframe.parentNode.removeChild(window._executorIframe);
        window._executorIframe = null;
    }

    // تحديث مستكشف الملفات لإظهار الملف النشط في قسم المحررات المفتوحة
    updateFileExplorer();
}

async function runCodeInExecutor() {
    const resultElement = document.getElementById('executor-result');
    resultElement.textContent = 'جاري التشغيل...\n';
    
    // Debug: Check if the terminal element exists
    if (!resultElement) {
        console.error('Terminal element not found (executor-result)');
        return;
    }
    
    if (!activeFileId || !workspace.files[activeFileId]) {
        resultElement.textContent += 'لا يوجد ملف نشط للتنفيذ.\n';
        return;
    }
    const file = workspace.files[activeFileId];
    const lang = (file.language || '').toLowerCase();
    
    // Log debugging info
    console.log(`Executing file: ${file.name}, Language: ${lang}`);
    
    // دعم HTML: عرض النتيجة في نافذة جانبية (web-preview-sidebar)
    if (lang === 'html') {
        resultElement.textContent = 'جاري تحضير عرض HTML مع الملفات المرتبطة...\n';
        // إزالة iframe قديم من التيرمنال إذا وجد
        if (window._htmlPreviewIframe && window._htmlPreviewIframe.parentNode) {
            window._htmlPreviewIframe.parentNode.removeChild(window._htmlPreviewIframe);
            window._htmlPreviewIframe = null;
        }
        // إظهار نافذة المعاينة الجانبية
        const sidebar = document.getElementById('web-preview-sidebar');
        const iframe = document.getElementById('web-preview-iframe');
        
        if (!sidebar) {
            resultElement.textContent += 'خطأ: عنصر web-preview-sidebar غير موجود في الصفحة!\n';
            console.error('web-preview-sidebar element not found');
            return;
        }
        
        if (!iframe) {
            resultElement.textContent += 'خطأ: عنصر web-preview-iframe غير موجود في الصفحة!\n';
            console.error('web-preview-iframe element not found');
            return;
        }
        
        try {
            sidebar.style.display = 'flex';
            setTimeout(() => {
                try {
                    // تحليل محتوى HTML للعثور على الملفات المرتبطة
                    const linkedFiles = findLinkedFiles(file.content);
                    
                    // إنشاء blob URL للملفات المرتبطة
                    const resourceMap = createResourceMap(linkedFiles);
                    
                    // تعديل HTML لاستخدام blob URLs
                    const modifiedHTML = replaceLinkedResources(file.content, resourceMap);
                    
                    // عرض HTML المعدل
                    iframe.contentDocument.open();
                    iframe.contentDocument.write(modifiedHTML);
                    iframe.contentDocument.close();
                    
                    resultElement.textContent = 'تم عرض HTML مع الملفات المرتبطة:\n';
                    
                    // عرض الملفات التي تم ربطها
                    if (Object.keys(resourceMap).length > 0) {
                        resultElement.textContent += '- الملفات المرتبطة:\n';
                        for (const [path, url] of Object.entries(resourceMap)) {
                            resultElement.textContent += `  - ${path}\n`;
                        }
                    } else {
                        resultElement.textContent += '- لا توجد ملفات مرتبطة\n';
                    }
                } catch (e) {
                    resultElement.textContent += `خطأ في عرض HTML: ${e.message}\n`;
                    console.error('Error displaying HTML:', e);
                }
            }, 100);
        } catch (e) {
            resultElement.textContent += `خطأ في تهيئة نافذة العرض: ${e.message}\n`;
            console.error('Error initializing preview window:', e);
        }
        return;
    }

    // دعم جافاسكريبت
    if (["js", "javascript"].includes(lang)) {
        try {
            if (window._executorIframe && window._executorIframe.parentNode) {
                window._executorIframe.parentNode.removeChild(window._executorIframe);
            }
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            document.body.appendChild(iframe);
            window._executorIframe = iframe;
            let context = {
                iframe,
                win: iframe.contentWindow,
                consoleOutput: []
            };
            
            // Override console methods in the iframe
            try {
                context.win.console.log = function (...args) {
                    context.consoleOutput.push(args.join(' '));
                    console.log(...args);
                };
                context.win.console.error = function (...args) {
                    context.consoleOutput.push('ERROR: ' + args.join(' '));
                    console.error(...args);
                };
                context.win.__files = {};
                for (const [id, f] of Object.entries(workspace.files)) {
                    context.win.__files[f.name] = f.content;
                }
            } catch (e) {
                resultElement.textContent += `خطأ في تهيئة بيئة التنفيذ: ${e.message}\n`;
                console.error('Error setting up execution environment:', e);
                return;
            }
            
            codeContexts[currentConversationId] = context;
            context.consoleOutput = [];
            
            const wrappedCode = `
                try {
                    function require(name) {
                        if (__files[name]) {
                            const module = { exports: {} };
                            const func = new Function('module', 'exports', __files[name]);
                            func(module, module.exports);
                            return module.exports;
                        }
                        throw new Error('Module not found: ' + name);
                    }
                    ${file.content}
                } catch(e) {
                    console.error('Error:', e);
                }
            `;
            
            console.log('Executing JavaScript code');
            let result;
            try {
                result = context.win.eval(wrappedCode);
            } catch (e) {
                context.consoleOutput.push(`ERROR: ${e.message}`);
                console.error('Error during evaluation:', e);
            }
            
            if (result !== undefined) {
                context.consoleOutput.push(result.toString());
            }
            
            resultElement.textContent += context.consoleOutput.join('\n') || 'تم التنفيذ بنجاح بدون إخراج.\n';
        } catch (e) {
            resultElement.textContent += `خطأ: ${e.message}\n`;
            console.error('Error executing code:', e);
        }
        resultElement.scrollTop = resultElement.scrollHeight;
        return;
    }

    // دعم بايثون
    if (lang === 'python' || lang === 'py') {
        try {
            resultElement.textContent = 'جاري تحميل بايثون (Pyodide)...\n';
            
            // Check if Pyodide is available
            if (typeof loadPyodide !== 'function') {
                resultElement.textContent += 'خطأ: مكتبة Pyodide غير متوفرة! تأكد من تضمين الملف في الصفحة.\n';
                console.error('Pyodide library not available. Make sure to include the script.');
                return;
            }
            
            if (!window.pyodide) {
                try {
                    window.pyodide = await loadPyodide();
                } catch (e) {
                    resultElement.textContent += `خطأ في تحميل Pyodide: ${e.message}\n`;
                    console.error('Error loading Pyodide:', e);
                    return;
                }
            }
            
            resultElement.textContent = 'جاري تنفيذ كود بايثون...\n';
            let output = '';
            
            try {
                window.pyodide.setStdout({
                    batched: (s) => { output += s; }
                });
                window.pyodide.setStderr({
                    batched: (s) => { output += s; }
                });
            } catch (e) {
                resultElement.textContent += `خطأ في تهيئة المخرجات: ${e.message}\n`;
                console.error('Error setting up stdout/stderr:', e);
            }
            
            try {
                await window.pyodide.runPythonAsync(file.content);
                resultElement.textContent += output || 'تم التنفيذ بنجاح بدون إخراج.\n';
            } catch (e) {
                resultElement.textContent += `خطأ بايثون: ${e.message}\n`;
                console.error('Python execution error:', e);
            }
        } catch (e) {
            resultElement.textContent += `خطأ: ${e.message}\n`;
            console.error('General error in Python execution:', e);
        }
        resultElement.scrollTop = resultElement.scrollHeight;
        return;
    }

    // لغة غير مدعومة
    resultElement.textContent += `اللغة "${lang}" غير مدعومة. تنفيذ الكود متاح فقط لجافاسكريبت، بايثون، وHTML حالياً.\n`;
}

function clearExecutorResult() {
    document.getElementById('executor-result').textContent = '';
}

// إضافة اختصارات لوحة المفاتيح
document.addEventListener('keydown', function (e) {
    const executor = document.getElementById('code-executor');
    if (executor.classList.contains('visible')) {
        if (e.key === 'Escape') {
            hideCodeExecutor(); // فقط إخفاء النافذة دون إغلاق التبويب
            e.preventDefault();
        }

        if (e.key === 'F5') {
            runCodeInExecutor();
            e.preventDefault();
        }
    }

    // اختصار لإرسال الرسالة
    if (e.key === 'Enter' && !e.shiftKey && document.activeElement.id === 'chat-input') {
        e.preventDefault();
        sendMessage();
    }
});

function handleKeyDown(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
}

function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const menuToggle = document.getElementById('menu-toggle');

    sidebar.classList.toggle('visible');
    menuToggle.classList.toggle('active');

    // إزالة أي تأثير على المحتوى الرئيسي
    const mainContent = document.getElementById('main-content');
    mainContent.classList.remove('sidebar-visible');
}

function createNewChat() {
    currentConversationId = Date.now().toString();
    const newConversation = {
        id: currentConversationId,
        title: 'محادثة جديدة',
        messages: [],
        timestamp: new Date().toISOString()
    };

    conversations.unshift(newConversation);
    
    // إنشاء ملفات خاصة بالمحادثة الجديدة
    const conversationFiles = createTestFilesForConversation(currentConversationId);
    
    // إضافة رسالة ترحيبية تشير إلى الملفات التي تم إنشاؤها
    newConversation.messages.push({
        role: 'bot',
        content: `
        <div class="message-content">
            <p>مرحباً بك في محادثة جديدة!</p>
            <p>لقد قمت بإنشاء ملفات خاصة بهذه المحادثة:</p>
            <ul>
                <li>HTML: test-conversation.html</li>
                <li>CSS: conversation-style.css</li>
                <li>JavaScript: conversation-script.js</li>
            </ul>
            <p>يمكنك تشغيل هذه الملفات من خلال مستكشف الملفات.</p>
        </div>
        `,
        timestamp: new Date().toISOString()
    });
    
    saveConversations();
    renderConversations();
    loadConversation(currentConversationId);
    toggleSidebar();
}

function saveConversations() {
    localStorage.setItem('conversations', JSON.stringify(conversations));
    localStorage.setItem('workspace', JSON.stringify(workspace));
    
    // حفظ معلومات ربط الملفات بالمحادثات
    localStorage.setItem('conversationFiles', JSON.stringify(workspace.conversationFiles));
}

function loadConversations() {
    const saved = localStorage.getItem('conversations');
    if (saved) {
        conversations = JSON.parse(saved);
        // فرز المحادثات حسب التاريخ من الأحدث
        conversations.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        renderConversations();

        // إذا كان هناك محادثات، نفتح الأخيرة
        if (conversations.length > 0) {
            currentConversationId = conversations[0].id;
            loadConversation(currentConversationId);
        }
    }

    // تحميل مساحة العمل
    const savedWorkspace = localStorage.getItem('workspace');
    if (savedWorkspace) {
        workspace = JSON.parse(savedWorkspace);
    } else {
        // إنشاء هيكل افتراضي للمشروع إذا لم يكن موجوداً
        workspace.folders['root'] = {
            id: 'root',
            name: 'root',
            path: '/',
            type: 'folder',
            children: []
        };
        createFolder('/project/');
        createFolder('/scripts/');
        createFile('main.js', '// file: /scripts/main.js\nconsole.log("Hello, World!");', 'javascript', '/scripts/');
        createFile('index.html', '<!-- file: /project/index.html -->\n<!DOCTYPE html>\n<html>\n<head>\n    <title>My App</title>\n</head>\n<body>\n    <h1>Welcome</h1>\n</body>\n</html>', 'html', '/project/');
    }
    
    // استعادة معلومات ربط الملفات بالمحادثات
    const savedConversationFiles = localStorage.getItem('conversationFiles');
    if (savedConversationFiles) {
        workspace.conversationFiles = JSON.parse(savedConversationFiles);
    }

    updateFileExplorer();
}

function renderConversations() {
    const list = document.getElementById('conversations-list');
    list.innerHTML = '';

    conversations.forEach(conv => {
        // إضافة أيقونة للرسائل الغنية
        const hasRichContent = conv.messages.some(m => m.content.includes('<div class="code-block">'));
        const icon = hasRichContent ? '📄' : '💬';
        const item = document.createElement('div');
        item.className = 'conversation-item';
        item.innerHTML = `
            <div class="conv-title">${icon} ${conv.title}</div>
            <div class="conversation-actions">
                <button class="conversation-action" onclick="deleteConversation('${conv.id}', event)">
                    <i class="fas fa-trash"></i>
                </button>
                <button class="conversation-action" onclick="shareConversation('${conv.id}', event)">
                    <i class="fas fa-share"></i>
                </button>
            </div>
        `;
        item.addEventListener('click', (e) => {
            if (!e.target.closest('.conversation-action')) {
                loadConversation(conv.id);
            }
        });
        list.appendChild(item);
    });
}

function deleteConversation(id, event) {
    event.stopPropagation();
    if (confirm('هل تريد حذف هذه المحادثة بشكل دائم؟')) {
        conversations = conversations.filter(conv => conv.id !== id);
        if (currentConversationId === id) {
            currentConversationId = null;
            document.getElementById('chat-window').innerHTML = '';
        }
        saveConversations();
        renderConversations();
    }
}

function shareConversation(id, event) {
    event.stopPropagation();
    const conversation = conversations.find(conv => conv.id === id);
    if (conversation) {
        const shareContent = conversation.messages
            .map(m => `${m.role === 'user' ? 'أنت' : 'المساعد'}: ${m.content}`)
            .join('\n\n');

        if (navigator.share) {
            navigator.share({
                title: `محادثة: ${conversation.title}`,
                text: shareContent,
                url: window.location.href
            });
        } else {
            const textArea = document.createElement('textarea');
            textArea.value = shareContent;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            alert('تم نسخ المحادثة إلى الحافظة!');
        }
    }
}

function loadConversation(id) {
    const conversation = conversations.find(conv => conv.id === id);
    if (conversation) {
        // تحديث معرف المحادثة الحالية
        currentConversationId = id;
        
        // إغلاق الملفات غير المرتبطة بالمحادثة الجديدة
        if (activeFileId) {
            const activeFile = workspace.files[activeFileId];
            if (activeFile && activeFile.conversationId && activeFile.conversationId !== currentConversationId) {
                hideCodeExecutor();
                activeFileId = null;
            }
        }
        
        // تحديث واجهة المستخدم
        const chatWindow = document.getElementById('chat-window');
        chatWindow.innerHTML = '';
    document.getElementById('code-executor').classList.remove('visible');

    // تنظيف iframe التنفيذ
    if (window._executorIframe && window._executorIframe.parentNode) {
        window._executorIframe.parentNode.removeChild(window._executorIframe);
        window._executorIframe = null;
    }

    updateFileTabs();
    updateFileExplorer();
}

// دالة إغلاق نافذة المعاينة الجانبية
function closeWebPreview() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (sidebar) sidebar.style.display = 'none';
}

// تهيئة التيرمنال عند تحميل الصفحة
function initializeTerminal() {
    // إضافة مقبض تغيير الحجم
    addTerminalResizer();
    
    // استعادة حالة التيرمنال
    restoreTerminalState();
    
    // تحديث رأس التيرمنال
    updateTerminalHeader();
    
    // إضافة زر التيرمنال في شريط الحالة
    addTerminalToggleButton();
    
    // إضافة مستمع لمفتاح ESC لإغلاق التيرمنال
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && document.getElementById('code-executor').classList.contains('visible')) {
            const executorFooter = document.querySelector('.executor-footer');
            if (executorFooter && !executorFooter.classList.contains('collapsed') && !executorFooter.classList.contains('hidden')) {
                executorFooter.classList.add('collapsed');
                localStorage.setItem('terminalState', 'collapsed');
                e.preventDefault();
            }
        }
    });
}

// تحديث دالة openFile لتحديث مؤشر اللغة في شريط الحالة
const originalOpenFileWithStatusBar = openFile;
openFile = function(fileId) {
    originalOpenFileWithStatusBar(fileId);
    
    // تحديث مؤشر اللغة في شريط الحالة
    const file = workspace.files[fileId];
    if (file && file.language) {
        const statusBar = document.querySelector('.status-bar');
        if (statusBar) {
            const langIndicator = statusBar.querySelector('.language-indicator .status-item-text');
            if (langIndicator) {
                langIndicator.textContent = file.language.charAt(0).toUpperCase() + file.language.slice(1);
            }
        }
    }
};

// Window resize event listener
window.addEventListener('resize', function() {
    // Update Monaco editor layout
    if (monacoEditor) {
        monacoEditor.layout();
    }
    
    // Update status bar responsiveness
    updateStatusBarResponsiveness();
    
    // Other window resize handlers...
});

// دوال لدعم ربط الملفات في HTML

// العثور على الملفات المرتبطة في HTML
function findLinkedFiles(htmlContent) {
    const linkedFiles = [];
    
    // البحث عن روابط CSS (تحسين التعبير النمطي ليشمل المزيد من الحالات)
    const cssRegex = /<link[^>]*href=["']([^"']+)["'][^>]*>/gi;
    let cssMatch;
    while ((cssMatch = cssRegex.exec(htmlContent)) !== null) {
        const href = cssMatch[1];
        if (href.endsWith('.css') || cssMatch[0].includes('stylesheet')) {
            linkedFiles.push({
                path: href,
                type: 'css'
            });
        }
    }
    
    // البحث عن روابط JavaScript (تحسين التعبير النمطي)
    const jsRegex = /<script[^>]*src=["']([^"']+)["'][^>]*>/gi;
    let jsMatch;
    while ((jsMatch = jsRegex.exec(htmlContent)) !== null) {
        const src = jsMatch[1];
        linkedFiles.push({
            path: src,
            type: 'js'
        });
    }
    
    // البحث عن روابط الصور (تحسين التعبير النمطي)
    const imgRegex = /<img[^>]*src=["']([^"']+)["'][^>]*>/gi;
    let imgMatch;
    while ((imgMatch = imgRegex.exec(htmlContent)) !== null) {
        const imgPath = imgMatch[1];
        // تجاهل الصور التي تبدأ بـ http:// أو https:// أو data:
        if (!imgPath.startsWith('http://') && !imgPath.startsWith('https://') && !imgPath.startsWith('data:')) {
            linkedFiles.push({
                path: imgPath,
                type: 'img'
            });
        }
    }
    
    // البحث عن روابط الخطوط (fonts)
    const fontRegex = /@font-face\s*{[^}]*src\s*:\s*url\(['"]?([^'"()]+)['"]?\)/gi;
    let fontMatch;
    while ((fontMatch = fontRegex.exec(htmlContent)) !== null) {
        const fontPath = fontMatch[1];
        if (!fontPath.startsWith('http://') && !fontPath.startsWith('https://') && !fontPath.startsWith('data:')) {
            linkedFiles.push({
                path: fontPath,
                type: 'font'
            });
        }
    }
    
    // البحث عن روابط الفيديو
    const videoRegex = /<video[^>]*>\s*<source[^>]*src=["']([^"']+)["'][^>]*>/gi;
    let videoMatch;
    while ((videoMatch = videoRegex.exec(htmlContent)) !== null) {
        const videoPath = videoMatch[1];
        if (!videoPath.startsWith('http://') && !videoPath.startsWith('https://') && !videoPath.startsWith('data:')) {
            linkedFiles.push({
                path: videoPath,
                type: 'video'
            });
        }
    }
    
    // البحث عن روابط الصوت
    const audioRegex = /<audio[^>]*>\s*<source[^>]*src=["']([^"']+)["'][^>]*>/gi;
    let audioMatch;
    while ((audioMatch = audioRegex.exec(htmlContent)) !== null) {
        const audioPath = audioMatch[1];
        if (!audioPath.startsWith('http://') && !audioPath.startsWith('https://') && !audioPath.startsWith('data:')) {
            linkedFiles.push({
                path: audioPath,
                type: 'audio'
            });
        }
    }
    
    // البحث عن روابط الخلفيات في CSS المضمن
    const bgRegex = /background(?:-image)?\s*:\s*url\(['"]?([^'"()]+)['"]?\)/gi;
    let bgMatch;
    while ((bgMatch = bgRegex.exec(htmlContent)) !== null) {
        const bgPath = bgMatch[1];
        if (!bgPath.startsWith('http://') && !bgPath.startsWith('https://') && !bgPath.startsWith('data:')) {
            linkedFiles.push({
                path: bgPath,
                type: 'img'
            });
        }
    }
    
    // إزالة التكرارات
    const uniqueFiles = [];
    const paths = new Set();
    
    for (const file of linkedFiles) {
        if (!paths.has(file.path)) {
            paths.add(file.path);
            uniqueFiles.push(file);
        }
    }
    
    return uniqueFiles;
}

// إيجاد الملف في workspace حسب المسار
function findFileByPath(path) {
    // تنظيف المسار
    const normalizedPath = normalizePath(path);
    
    // البحث عن الملف في workspace
    for (const fileId in workspace.files) {
        const file = workspace.files[fileId];
        const filePath = normalizePath(file.path + file.name);
        
        if (filePath === normalizedPath || filePath.endsWith('/' + normalizedPath)) {
            return file;
        }
    }
    
    // البحث عن الملف بالاسم فقط إذا لم يتم العثور عليه بالمسار الكامل
    const fileName = path.split('/').pop();
    for (const fileId in workspace.files) {
        const file = workspace.files[fileId];
        if (file.name === fileName) {
            return file;
        }
    }
    
    return null;
}

// تنظيم المسار
function normalizePath(path) {
    // إزالة / من البداية والنهاية
    let normalized = path.replace(/^\/+|\/+$/g, '');
    // إزالة المسارات المتكررة
    normalized = normalized.replace(/\/+/g, '/');
    return normalized;
}

// إنشاء خريطة الموارد (Blob URLs)
function createResourceMap(linkedFiles) {
    const resourceMap = {};
    
    for (const linkedFile of linkedFiles) {
        const file = findFileByPath(linkedFile.path);
        if (file) {
            // تحديد نوع المحتوى
            let mimeType = 'text/plain';
            if (linkedFile.type === 'css') {
                mimeType = 'text/css';
            } else if (linkedFile.type === 'js') {
                mimeType = 'application/javascript';
            } else if (linkedFile.type === 'img') {
                // تحديد نوع MIME للصورة بناءً على الامتداد
                const extension = linkedFile.path.split('.').pop().toLowerCase();
                switch (extension) {
                    case 'png':
                        mimeType = 'image/png';
                        break;
                    case 'jpg':
                    case 'jpeg':
                        mimeType = 'image/jpeg';
                        break;
                    case 'gif':
                        mimeType = 'image/gif';
                        break;
                    case 'svg':
                        mimeType = 'image/svg+xml';
                        break;
                    case 'webp':
                        mimeType = 'image/webp';
                        break;
                    default:
                        mimeType = 'image/png'; // افتراضي للصور غير المعروفة
                }
                
                // للصور، نحاول إنشاء صورة وهمية إذا كان المحتوى نصيًا
                if (typeof file.content === 'string' && !isBase64Image(file.content)) {
                    // إنشاء صورة وهمية باستخدام canvas
                    const dataUrl = createPlaceholderImage(linkedFile.path, 300, 200);
                    resourceMap[linkedFile.path] = dataUrl;
                    continue;
                }
            }
            
            // إنشاء Blob URL للملف
            const blob = new Blob([file.content], { type: mimeType });
            const url = URL.createObjectURL(blob);
            resourceMap[linkedFile.path] = url;
        } else {
            // إذا لم يتم العثور على الملف، نستخدم صورة وهمية للصور
            if (linkedFile.type === 'img') {
                const dataUrl = createPlaceholderImage(linkedFile.path, 300, 200);
                resourceMap[linkedFile.path] = dataUrl;
            }
        }
    }
    
    return resourceMap;
}

// التحقق مما إذا كان المحتوى صورة base64
function isBase64Image(content) {
    return /^data:image\/[a-z]+;base64,/.test(content);
}

// إنشاء صورة وهمية باستخدام canvas
function createPlaceholderImage(filename, width, height) {
    // إنشاء canvas
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d');
    
    // تعبئة الخلفية
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(0, 0, width, height);
    
    // رسم حدود
    ctx.strokeStyle = '#cccccc';
    ctx.lineWidth = 2;
    ctx.strokeRect(2, 2, width - 4, height - 4);
    
    // إضافة نص
    ctx.fillStyle = '#666666';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    // استخراج اسم الملف من المسار
    const name = filename.split('/').pop();
    
    ctx.fillText('صورة وهمية', width / 2, height / 2 - 10);
    ctx.fillText(name, width / 2, height / 2 + 20);
    
    // تحويل إلى data URL
    return canvas.toDataURL('image/png');
}

// استبدال روابط الموارد في HTML
function replaceLinkedResources(htmlContent, resourceMap) {
    let modifiedHTML = htmlContent;
    
    // استبدال روابط CSS
    for (const [path, url] of Object.entries(resourceMap)) {
        if (path.endsWith('.css')) {
            const regex = new RegExp(`href=["']${escapeRegExp(path)}["']`, 'gi');
            modifiedHTML = modifiedHTML.replace(regex, `href="${url}"`);
        }
    }
    
    // استبدال روابط JavaScript
    for (const [path, url] of Object.entries(resourceMap)) {
        if (path.endsWith('.js')) {
            const regex = new RegExp(`src=["']${escapeRegExp(path)}["']`, 'gi');
            modifiedHTML = modifiedHTML.replace(regex, `src="${url}"`);
        }
    }
    
    // استبدال روابط الصور
    for (const [path, url] of Object.entries(resourceMap)) {
        if (!path.endsWith('.css') && !path.endsWith('.js')) {
            const regex = new RegExp(`src=["']${escapeRegExp(path)}["']`, 'gi');
            modifiedHTML = modifiedHTML.replace(regex, `src="${url}"`);
        }
    }
    
    return modifiedHTML;
}

// هروب الأحرف الخاصة في التعبيرات النمطية
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// إنشاء ملف HTML جديد في محادثة جديدة
function createTestFilesForConversation(conversationId) {
    // إنشاء ملف HTML
    const htmlFileId = createFile(
        'test-conversation.html',
        `<!-- file: /project/test-conversation.html -->
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ملف خاص بالمحادثة ${conversationId}</title>
    <link rel="stylesheet" href="conversation-style.css">
</head>
<body>
    <div class="container">
        <h1>هذا ملف خاص بالمحادثة ${conversationId}</h1>
        <div class="content">
            <p>هذا الملف وملفات CSS و JavaScript المرتبطة به خاصة بهذه المحادثة فقط.</p>
            <button id="testButton">اختبار</button>
        </div>
    </div>
    <script src="conversation-script.js"></script>
</body>
</html>`,
        'html',
        '/project/',
        conversationId
    );

    // إنشاء ملف CSS
    const cssFileId = createFile(
        'conversation-style.css',
        `/* file: /project/conversation-style.css */
body {
    font-family: Arial, sans-serif;
    background-color: #f5f5f5;
    margin: 0;
    padding: 20px;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

h1 {
    color: #333;
    text-align: center;
    border-bottom: 2px solid #eee;
    padding-bottom: 10px;
}

.content {
    margin-top: 20px;
}

button {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

button:hover {
    background-color: #45a049;
}`,
        'css',
        '/project/',
        conversationId
    );

    // إنشاء ملف JavaScript
    const jsFileId = createFile(
        'conversation-script.js',
        `// file: /project/conversation-script.js
document.addEventListener('DOMContentLoaded', function() {
    const button = document.getElementById('testButton');
    if (button) {
        button.addEventListener('click', function() {
            alert('هذا الزر خاص بالمحادثة ${conversationId}');
        });
    }
    
    console.log('تم تحميل الملف الخاص بالمحادثة ${conversationId}');
});`,
        'javascript',
        '/project/',
        conversationId
    );

    return {
        htmlFileId,
        cssFileId,
        jsFileId
    };
}

// الحصول على ملفات المحادثة الحالية
function getFilesForCurrentConversation() {
    // إذا لم تكن هناك محادثة حالية، نعرض جميع الملفات التي ليس لها محادثة محددة
    if (!currentConversationId) {
        return Object.keys(workspace.files).filter(fileId => !workspace.files[fileId].conversationId);
    }
    
    // الملفات الخاصة بالمحادثة الحالية + الملفات العامة (التي ليس لها محادثة محددة)
    return Object.keys(workspace.files).filter(fileId => {
        const file = workspace.files[fileId];
        return !file.conversationId || file.conversationId === currentConversationId;
    });
}
