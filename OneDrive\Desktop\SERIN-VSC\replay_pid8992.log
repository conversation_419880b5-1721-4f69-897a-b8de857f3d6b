version 2
JvmtiExport can_access_local_variables 0
JvmtiExport can_hotswap_or_post_breakpoint 0
JvmtiExport can_post_on_exceptions 0
# 513 ciObject found
instanceKlass org/springframework/boot/loader/net/protocol/jar/LazyDelegatingInputStream
ciInstanceKlass java/lang/Cloneable 1 0 7 100 1 100 1 1 1
instanceKlass com/github/cameltooling/lsp/internal/telemetry/TelemetryEvent
instanceKlass com/github/cameltooling/lsp/internal/telemetry/Memory
instanceKlass com/github/cameltooling/lsp/internal/telemetry/JVM
instanceKlass com/github/cameltooling/lsp/internal/telemetry/OS
instanceKlass com/github/cameltooling/lsp/internal/telemetry/Platform
instanceKlass com/github/cameltooling/lsp/internal/telemetry/InitializationTelemetryInfo
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e9025c400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e9025c000
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/RemoteEndpoint handleRequest (Lorg/eclipse/lsp4j/jsonrpc/messages/RequestMessage;)V 142 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/RemoteEndpoint$$Lambda+0x0000029e9025a8c8
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/RemoteEndpoint handleRequest (Lorg/eclipse/lsp4j/jsonrpc/messages/RequestMessage;)V 132 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/RemoteEndpoint$$Lambda+0x0000029e9025a688
instanceKlass org/eclipse/lsp4j/SemanticTokensServerFull
instanceKlass org/eclipse/lsp4j/SemanticTokensLegend
instanceKlass org/eclipse/lsp4j/FileOperationPatternOptions
instanceKlass org/eclipse/lsp4j/FileOperationPattern
instanceKlass org/eclipse/lsp4j/FileOperationFilter
instanceKlass org/eclipse/lsp4j/FileOperationOptions
instanceKlass org/eclipse/lsp4j/FileOperationsServerCapabilities
instanceKlass org/eclipse/lsp4j/WorkspaceFoldersOptions
instanceKlass org/eclipse/lsp4j/DocumentFilter
instanceKlass org/eclipse/lsp4j/CompletionItemOptions
instanceKlass org/eclipse/lsp4j/NotebookSelectorCell
instanceKlass org/eclipse/lsp4j/NotebookDocumentFilter
instanceKlass org/eclipse/lsp4j/NotebookSelector
instanceKlass org/eclipse/lsp4j/SaveOptions
instanceKlass org/eclipse/lsp4j/TextDocumentSyncOptions
instanceKlass org/eclipse/lsp4j/TextDocumentRegistrationOptions
instanceKlass org/eclipse/lsp4j/WorkspaceServerCapabilities
instanceKlass org/eclipse/lsp4j/DocumentOnTypeFormattingOptions
instanceKlass org/eclipse/lsp4j/NotebookDocumentSyncRegistrationOptions
instanceKlass org/eclipse/lsp4j/ServerInfo
instanceKlass com/google/gson/internal/Streams
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/RemoteEndpoint handleRequest (Lorg/eclipse/lsp4j/jsonrpc/messages/RequestMessage;)V 121 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/RemoteEndpoint$$Lambda+0x0000029e90256b38
instanceKlass org/eclipse/lsp4j/AbstractWorkDoneProgressOptions
instanceKlass org/eclipse/lsp4j/WorkDoneProgressOptions
instanceKlass  @bci com/github/cameltooling/lsp/internal/CamelTextDocumentService updateCatalog (Ljava/lang/String;Ljava/lang/String;Ljava/util/List;)V 5 <appendix> member <vmtarget> ; # com/github/cameltooling/lsp/internal/CamelTextDocumentService$$Lambda+0x0000029e902559a0
instanceKlass com/google/gson/internal/bind/ObjectTypeAdapter$2
instanceKlass com/google/gson/internal/LinkedTreeMap$LinkedTreeMapIterator
instanceKlass  @bci com/google/gson/internal/ConstructorConstructor newDefaultImplementationConstructor (Ljava/lang/reflect/Type;Ljava/lang/Class;)Lcom/google/gson/internal/ObjectConstructor; 152 <appendix> argL0 ; # com/google/gson/internal/ConstructorConstructor$$Lambda+0x0000029e90254ce8
instanceKlass com/github/cameltooling/lsp/internal/settings/JSONUtility
instanceKlass org/apache/logging/log4j/core/layout/ByteBufferDestinationHelper
instanceKlass org/apache/logging/log4j/core/layout/TextEncoderHelper
instanceKlass java/time/Clock
instanceKlass java/time/InstantSource
instanceKlass  @bci org/apache/logging/log4j/util/SortedArrayStringMap <clinit> ()V 0 <appendix> argL0 ; # org/apache/logging/log4j/util/SortedArrayStringMap$$Lambda+0x0000029e9024f7b8
instanceKlass  @cpi org/apache/logging/log4j/util/SortedArrayStringMap 405 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000029e90250400
instanceKlass org/apache/logging/log4j/util/TriConsumer
instanceKlass org/apache/logging/log4j/util/SortedArrayStringMap
instanceKlass org/apache/logging/log4j/util/IndexedStringMap
instanceKlass org/apache/logging/log4j/util/IndexedReadOnlyStringMap
instanceKlass org/apache/logging/log4j/core/impl/ContextDataFactory
instanceKlass org/apache/logging/log4j/message/SimpleMessage
instanceKlass org/apache/logging/log4j/core/impl/MutableLogEvent
instanceKlass  @bci org/apache/logging/log4j/util/StackLocator calcLocation (Ljava/lang/String;)Ljava/lang/StackTraceElement; 15 <appendix> argL0 ; # org/apache/logging/log4j/util/StackLocator$$Lambda+0x0000029e9024e240
instanceKlass  @bci org/apache/logging/log4j/util/StackLocator lambda$calcLocation$15 (Ljava/lang/String;Ljava/util/stream/Stream;)Ljava/util/Optional; 13 <appendix> member <vmtarget> ; # org/apache/logging/log4j/util/StackLocator$$Lambda+0x0000029e9024dff0
instanceKlass  @bci org/apache/logging/log4j/util/StackLocator lambda$calcLocation$15 (Ljava/lang/String;Ljava/util/stream/Stream;)Ljava/util/Optional; 2 <appendix> member <vmtarget> ; # org/apache/logging/log4j/util/StackLocator$$Lambda+0x0000029e9024dda0
instanceKlass  @bci org/apache/logging/log4j/util/StackLocator calcLocation (Ljava/lang/String;)Ljava/lang/StackTraceElement; 4 <appendix> member <vmtarget> ; # org/apache/logging/log4j/util/StackLocator$$Lambda+0x0000029e9024db60
instanceKlass java/time/zone/ZoneOffsetTransition
instanceKlass java/time/LocalTime
instanceKlass java/time/zone/Ser
instanceKlass java/io/Externalizable
instanceKlass java/time/zone/ZoneRulesProvider$1
instanceKlass java/time/zone/ZoneRulesProvider
instanceKlass org/apache/logging/log4j/message/ParameterFormatter
instanceKlass org/apache/logging/log4j/message/ParameterFormatter$MessagePatternAnalysis
instanceKlass org/apache/logging/log4j/message/ReusableParameterizedMessage
instanceKlass org/apache/logging/log4j/message/Clearable
instanceKlass org/apache/logging/log4j/message/ParameterVisitable
instanceKlass org/apache/logging/log4j/message/ReusableMessage
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90250000
instanceKlass com/google/gson/internal/LinkedTreeMap$Node
instanceKlass com/google/gson/internal/LinkedTreeMap$1
instanceKlass com/google/gson/internal/bind/JsonElementTypeAdapter$1
instanceKlass org/eclipse/lsp4j/jsonrpc/util/Preconditions
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/JsonElementTypeAdapter$Factory
instanceKlass org/eclipse/lsp4j/StaleRequestCapabilities
instanceKlass org/eclipse/lsp4j/MarkdownCapabilities
instanceKlass org/eclipse/lsp4j/RegularExpressionsCapabilities
instanceKlass org/eclipse/lsp4j/WindowShowMessageRequestActionItemCapabilities
instanceKlass org/eclipse/lsp4j/ShowDocumentCapabilities
instanceKlass org/eclipse/lsp4j/WindowShowMessageRequestCapabilities
instanceKlass org/eclipse/lsp4j/InlayHintResolveSupportCapabilities
instanceKlass org/eclipse/lsp4j/SemanticTokensClientCapabilitiesRequestsFull
instanceKlass org/eclipse/lsp4j/SemanticTokensClientCapabilitiesRequests
instanceKlass org/eclipse/lsp4j/FoldingRangeSupportCapabilities
instanceKlass org/eclipse/lsp4j/FoldingRangeKindSupportCapabilities
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/EitherTypeAdapter$EitherTypeArgument
instanceKlass org/eclipse/lsp4j/DiagnosticsTagSupport
instanceKlass org/eclipse/lsp4j/CodeActionKindCapabilities
instanceKlass org/eclipse/lsp4j/CodeActionResolveSupportCapabilities
instanceKlass org/eclipse/lsp4j/CodeActionLiteralSupportCapabilities
instanceKlass org/eclipse/lsp4j/ParameterInformationCapabilities
instanceKlass org/eclipse/lsp4j/SignatureInformationCapabilities
instanceKlass org/eclipse/lsp4j/CompletionItemInsertTextModeSupportCapabilities
instanceKlass org/eclipse/lsp4j/CompletionItemResolveSupportCapabilities
instanceKlass org/eclipse/lsp4j/CompletionItemTagSupportCapabilities
instanceKlass org/eclipse/lsp4j/CompletionListCapabilities
instanceKlass org/eclipse/lsp4j/CompletionItemCapabilities
instanceKlass org/eclipse/lsp4j/CompletionItemKindCapabilities
instanceKlass org/eclipse/lsp4j/PublishDiagnosticsCapabilities
instanceKlass org/eclipse/lsp4j/WorkspaceSymbolResolveSupportCapabilities
instanceKlass org/eclipse/lsp4j/SymbolTagSupportCapabilities
instanceKlass org/eclipse/lsp4j/SymbolKindCapabilities
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/json/adapters/CollectionTypeAdapter$Factory getConstructor (Ljava/lang/Class;)Ljava/util/function/Supplier; 62 <appendix> argL0 ; # org/eclipse/lsp4j/jsonrpc/json/adapters/CollectionTypeAdapter$Factory$$Lambda+0x0000029e902420c8
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/TypeUtils$ParameterizedTypeImpl
instanceKlass org/eclipse/lsp4j/WorkspaceEditChangeAnnotationSupportCapabilities
instanceKlass org/eclipse/lsp4j/DiagnosticWorkspaceCapabilities
instanceKlass org/eclipse/lsp4j/InlineValueWorkspaceCapabilities
instanceKlass org/eclipse/lsp4j/InlayHintWorkspaceCapabilities
instanceKlass org/eclipse/lsp4j/CodeLensWorkspaceCapabilities
instanceKlass org/eclipse/lsp4j/SemanticTokensWorkspaceCapabilities
instanceKlass org/eclipse/lsp4j/DynamicRegistrationCapabilities
instanceKlass org/eclipse/lsp4j/WorkspaceEditCapabilities
instanceKlass org/eclipse/lsp4j/GeneralClientCapabilities
instanceKlass org/eclipse/lsp4j/NotebookDocumentClientCapabilities
instanceKlass org/eclipse/lsp4j/WindowClientCapabilities
instanceKlass org/eclipse/lsp4j/TextDocumentClientCapabilities
instanceKlass org/eclipse/lsp4j/WorkspaceClientCapabilities
instanceKlass org/eclipse/lsp4j/ClientCapabilities
instanceKlass com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$FieldsData
instanceKlass com/google/gson/internal/Primitives
instanceKlass org/eclipse/lsp4j/jsonrpc/validation/NonNull
instanceKlass com/google/gson/annotations/SerializedName
instanceKlass org/eclipse/lsp4j/ClientInfo
instanceKlass  @bci com/google/gson/internal/ConstructorConstructor newDefaultConstructor (Ljava/lang/Class;Lcom/google/gson/ReflectionAccessFilter$FilterResult;)Lcom/google/gson/internal/ObjectConstructor; 130 <appendix> member <vmtarget> ; # com/google/gson/internal/ConstructorConstructor$$Lambda+0x0000029e9023dde0
instanceKlass com/google/gson/internal/ObjectConstructor
instanceKlass com/google/gson/internal/ReflectionAccessFilterHelper
instanceKlass org/eclipse/lsp4j/adapters/InitializeParamsTypeAdapter$Factory
instanceKlass com/google/gson/annotations/JsonAdapter
instanceKlass org/eclipse/lsp4j/jsonrpc/messages/Tuple$Two
instanceKlass org/eclipse/lsp4j/jsonrpc/messages/Tuple
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/TypeUtils
instanceKlass com/google/gson/internal/reflect/ReflectionHelper$RecordHelper
instanceKlass com/google/gson/internal/reflect/ReflectionHelper
instanceKlass com/google/gson/internal/JsonReaderInternalAccess
instanceKlass org/eclipse/lsp4j/jsonrpc/json/StreamMessageProducer$Headers
instanceKlass org/eclipse/lsp4j/jsonrpc/json/ConcurrentMessageProcessor$1
instanceKlass java/util/concurrent/Executors$RunnableAdapter
instanceKlass org/eclipse/lsp4j/jsonrpc/StandardLauncher
instanceKlass java/util/concurrent/SynchronousQueue$TransferStack$SNode
instanceKlass java/util/concurrent/SynchronousQueue$Transferer
instanceKlass org/eclipse/lsp4j/jsonrpc/json/ConcurrentMessageProcessor
instanceKlass org/eclipse/lsp4j/jsonrpc/json/StreamMessageProducer
instanceKlass  @bci java/lang/reflect/Proxy getProxyConstructor (Ljava/lang/Class;Ljava/lang/ClassLoader;[Ljava/lang/Class;)Ljava/lang/reflect/Constructor; 80 <appendix> argL0 ; # java/lang/reflect/Proxy$$Lambda+0x0000029e90157778
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/EndpointProxy <init> (Lorg/eclipse/lsp4j/jsonrpc/Endpoint;Ljava/util/Collection;)V 197 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/services/EndpointProxy$$Lambda+0x0000029e90239bd8
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/EndpointProxy <init> (Lorg/eclipse/lsp4j/jsonrpc/Endpoint;Ljava/util/Collection;)V 178 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/services/EndpointProxy$$Lambda+0x0000029e902399a8
instanceKlass org/eclipse/lsp4j/jsonrpc/services/EndpointProxy
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/RemoteEndpoint <clinit> ()V 11 <appendix> argL0 ; # org/eclipse/lsp4j/jsonrpc/RemoteEndpoint$$Lambda+0x0000029e90239540
instanceKlass org/eclipse/lsp4j/jsonrpc/messages/ResponseError
instanceKlass org/eclipse/lsp4j/jsonrpc/messages/Message
instanceKlass org/eclipse/lsp4j/jsonrpc/RemoteEndpoint
instanceKlass org/eclipse/lsp4j/TextDocumentItem
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 22 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000044
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 17 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000042
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 12 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x80000003f
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 7 <appendix> member <vmtarget> ; # java/util/stream/Collectors$$Lambda+0x800000047
instanceKlass  @bci java/lang/reflect/Executable sharedToString (IZ[Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/String; 29 <appendix> argL0 ; # java/lang/reflect/Executable$$Lambda+0x0000029e90157540
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint recursiveFindRpcMethods (Ljava/lang/Object;Ljava/util/Set;Ljava/util/Set;)V 24 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint$$Lambda+0x0000029e90237760
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint lambda$recursiveFindRpcMethods$1 (Ljava/lang/Object;Lorg/eclipse/lsp4j/jsonrpc/services/AnnotationUtil$MethodInfo;)V 3 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint$$Lambda+0x0000029e90237520
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint recursiveFindRpcMethods (Ljava/lang/Object;Ljava/util/Set;Ljava/util/Set;)V 7 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint$$Lambda+0x0000029e902372f0
instanceKlass jdk/internal/logger/DefaultLoggerFinder$1
instanceKlass java/util/logging/Logger$SystemLoggerHelper$1
instanceKlass java/util/logging/Logger$SystemLoggerHelper
instanceKlass java/util/logging/LogManager$4
instanceKlass jdk/internal/logger/BootstrapLogger$BootstrapExecutors
instanceKlass jdk/internal/logger/BootstrapLogger$RedirectedLoggers
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend$1
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend
instanceKlass jdk/internal/logger/BootstrapLogger
instanceKlass sun/util/logging/PlatformLogger$ConfigurableBridge
instanceKlass sun/util/logging/PlatformLogger$Bridge
instanceKlass java/util/stream/Streams
instanceKlass java/util/stream/Stream$Builder
instanceKlass java/util/stream/Streams$AbstractStreamBuilderImpl
instanceKlass  @bci java/util/logging/Level$KnownLevel findByName (Ljava/lang/String;Ljava/util/function/Function;)Ljava/util/Optional; 29 <appendix> argL0 ; # java/util/logging/Level$KnownLevel$$Lambda+0x800000025
instanceKlass java/util/ArrayList$ArrayListSpliterator
instanceKlass  @bci java/util/logging/Level findLevel (Ljava/lang/String;)Ljava/util/logging/Level; 13 <appendix> argL0 ; # java/util/logging/Level$$Lambda+0x800000013
instanceKlass java/util/Hashtable$Enumerator
instanceKlass java/util/Collections$3
instanceKlass java/util/logging/LogManager$LoggerContext$1
instanceKlass java/util/logging/LogManager$VisitedLoggers
instanceKlass java/util/logging/LogManager$2
instanceKlass java/lang/System$LoggerFinder
instanceKlass java/util/logging/LogManager$LoggingProviderAccess
instanceKlass sun/util/logging/internal/LoggingProviderImpl$LogManagerAccess
instanceKlass java/util/Collections$SynchronizedMap
instanceKlass java/util/logging/LogManager$LogNode
instanceKlass java/util/logging/LogManager$LoggerContext
instanceKlass java/util/logging/LogManager$1
instanceKlass java/util/logging/Logger$ConfigurationData
instanceKlass java/util/logging/Logger$LoggerBundle
instanceKlass  @bci java/util/logging/Level$KnownLevel add (Ljava/util/logging/Level;)V 49 <appendix> argL0 ; # java/util/logging/Level$KnownLevel$$Lambda+0x800000024
instanceKlass  @bci java/util/logging/Level$KnownLevel add (Ljava/util/logging/Level;)V 19 <appendix> argL0 ; # java/util/logging/Level$KnownLevel$$Lambda+0x800000023
instanceKlass java/util/logging/Level
instanceKlass java/util/logging/Handler
instanceKlass java/util/logging/Logger
instanceKlass org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint
instanceKlass org/eclipse/lsp4j/jsonrpc/json/StreamMessageConsumer
instanceKlass org/eclipse/lsp4j/jsonrpc/json/MessageConstants
instanceKlass com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$BoundField
instanceKlass com/google/gson/internal/bind/ReflectiveTypeAdapterFactory
instanceKlass com/google/gson/internal/bind/JsonAdapterAnnotationTypeAdapterFactory$DummyTypeAdapterFactory
instanceKlass com/google/gson/internal/bind/JsonAdapterAnnotationTypeAdapterFactory
instanceKlass com/google/gson/internal/bind/MapTypeAdapterFactory
instanceKlass com/google/gson/internal/bind/CollectionTypeAdapterFactory
instanceKlass com/google/gson/internal/bind/ArrayTypeAdapter$1
instanceKlass com/google/gson/internal/bind/DefaultDateTypeAdapter$1
instanceKlass com/google/gson/internal/bind/NumberTypeAdapter$1
instanceKlass com/google/gson/internal/bind/ObjectTypeAdapter$1
instanceKlass com/google/gson/internal/bind/EnumTypeAdapter$1
instanceKlass com/google/gson/internal/bind/TypeAdapters$31
instanceKlass com/google/gson/internal/bind/TypeAdapters$32
instanceKlass java/util/concurrent/atomic/AtomicIntegerArray
instanceKlass com/google/gson/internal/bind/TypeAdapters$30
instanceKlass com/google/gson/internal/bind/TypeAdapters$29
instanceKlass com/google/gson/internal/bind/TypeAdapters
instanceKlass com/google/gson/internal/ConstructorConstructor
instanceKlass com/google/gson/internal/sql/SqlTimestampTypeAdapter$1
instanceKlass com/google/gson/internal/sql/SqlTimeTypeAdapter$1
instanceKlass com/google/gson/internal/sql/SqlDateTypeAdapter$1
instanceKlass com/google/gson/internal/bind/DefaultDateTypeAdapter$DateType
instanceKlass com/google/gson/internal/sql/SqlTypesSupport
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/MessageTypeAdapter$Factory
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/EnumTypeAdapter$Factory
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/TupleTypeAdapters$TwoTypeAdapterFactory
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/EitherTypeAdapter$Factory
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/ThrowableTypeAdapter$Factory
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/CollectionTypeAdapter$Factory
instanceKlass com/google/gson/FormattingStyle
instanceKlass com/google/gson/stream/JsonWriter
instanceKlass com/google/gson/stream/JsonReader
instanceKlass com/google/gson/ToNumberStrategy
instanceKlass com/google/gson/Gson
instanceKlass com/google/gson/internal/Excluder
instanceKlass com/google/gson/FieldNamingStrategy
instanceKlass com/google/gson/GsonBuilder
instanceKlass org/eclipse/lsp4j/jsonrpc/messages/CancelParams
instanceKlass com/google/gson/JsonElement
instanceKlass org/eclipse/lsp4j/jsonrpc/json/MessageJsonHandler
instanceKlass org/eclipse/lsp4j/CallHierarchyItem
instanceKlass org/eclipse/lsp4j/SemanticTokensDelta
instanceKlass org/eclipse/lsp4j/adapters/SemanticTokensFullDeltaResponseAdapter
instanceKlass org/eclipse/lsp4j/CallHierarchyOutgoingCall
instanceKlass org/eclipse/lsp4j/TypeHierarchyItem
instanceKlass org/eclipse/lsp4j/CallHierarchyIncomingCall
instanceKlass org/eclipse/lsp4j/InlineValueEvaluatableExpression
instanceKlass org/eclipse/lsp4j/InlineValueVariableLookup
instanceKlass org/eclipse/lsp4j/adapters/InlineValueResponseAdapter
instanceKlass org/eclipse/lsp4j/SemanticTokens
instanceKlass org/eclipse/lsp4j/ColorPresentation
instanceKlass org/eclipse/lsp4j/SignatureHelp
instanceKlass org/eclipse/lsp4j/LinkedEditingRanges
instanceKlass org/eclipse/lsp4j/DocumentSymbol
instanceKlass org/eclipse/lsp4j/adapters/DocumentSymbolResponseAdapter
instanceKlass org/eclipse/lsp4j/SelectionRange
instanceKlass org/eclipse/lsp4j/TextEdit
instanceKlass org/eclipse/lsp4j/PrepareRenameDefaultBehavior
instanceKlass org/eclipse/lsp4j/PrepareRenameResult
instanceKlass org/eclipse/lsp4j/Range
instanceKlass org/eclipse/lsp4j/adapters/PrepareRenameResponseAdapter
instanceKlass org/eclipse/lsp4j/ColorInformation
instanceKlass org/eclipse/lsp4j/FoldingRange
instanceKlass org/eclipse/lsp4j/Moniker
instanceKlass org/eclipse/lsp4j/adapters/DocumentDiagnosticReportTypeAdapter
instanceKlass org/eclipse/lsp4j/Command
instanceKlass org/eclipse/lsp4j/adapters/CodeActionResponseAdapter
instanceKlass org/eclipse/lsp4j/CompletionList
instanceKlass org/eclipse/lsp4j/Hover
instanceKlass org/eclipse/lsp4j/LocationLink
instanceKlass org/eclipse/lsp4j/Location
instanceKlass org/eclipse/lsp4j/adapters/LocationLinkListAdapter
instanceKlass org/eclipse/lsp4j/CompletionItem
instanceKlass org/eclipse/lsp4j/DocumentLink
instanceKlass org/eclipse/lsp4j/InlineValueParams
instanceKlass org/eclipse/lsp4j/DocumentRangeFormattingParams
instanceKlass org/eclipse/lsp4j/CodeLens
instanceKlass org/eclipse/lsp4j/CodeAction
instanceKlass org/eclipse/lsp4j/InlayHint
instanceKlass org/eclipse/lsp4j/DidCloseTextDocumentParams
instanceKlass org/eclipse/lsp4j/DidSaveTextDocumentParams
instanceKlass org/eclipse/lsp4j/DidChangeTextDocumentParams
instanceKlass org/eclipse/lsp4j/WillSaveTextDocumentParams
instanceKlass org/eclipse/lsp4j/InlayHintParams
instanceKlass org/eclipse/lsp4j/DocumentFormattingParams
instanceKlass org/eclipse/lsp4j/DidOpenTextDocumentParams
instanceKlass org/eclipse/lsp4j/TextDocumentPositionParams
instanceKlass org/eclipse/lsp4j/DidCloseNotebookDocumentParams
instanceKlass org/eclipse/lsp4j/DidSaveNotebookDocumentParams
instanceKlass org/eclipse/lsp4j/DidChangeNotebookDocumentParams
instanceKlass org/eclipse/lsp4j/DidOpenNotebookDocumentParams
instanceKlass org/eclipse/lsp4j/WorkspaceEdit
instanceKlass org/eclipse/lsp4j/WorkspaceDiagnosticReport
instanceKlass org/eclipse/lsp4j/SymbolInformation
instanceKlass com/google/gson/internal/$Gson$Types$WildcardTypeImpl
instanceKlass com/google/gson/internal/$Gson$Preconditions
instanceKlass com/google/gson/internal/$Gson$Types$ParameterizedTypeImpl
instanceKlass com/google/gson/internal/$Gson$Types
instanceKlass com/google/gson/TypeAdapter
instanceKlass com/google/gson/reflect/TypeToken
instanceKlass org/eclipse/lsp4j/jsonrpc/messages/Either
instanceKlass org/eclipse/lsp4j/adapters/WorkspaceSymbolResponseAdapter
instanceKlass com/google/gson/TypeAdapterFactory
instanceKlass org/eclipse/lsp4j/DidChangeWorkspaceFoldersParams
instanceKlass org/eclipse/lsp4j/DidChangeWatchedFilesParams
instanceKlass org/eclipse/lsp4j/DidChangeConfigurationParams
instanceKlass org/eclipse/lsp4j/WorkspaceSymbol
instanceKlass org/eclipse/lsp4j/CreateFilesParams
instanceKlass org/eclipse/lsp4j/DeleteFilesParams
instanceKlass org/eclipse/lsp4j/RenameFilesParams
instanceKlass org/eclipse/lsp4j/ExecuteCommandParams
instanceKlass org/eclipse/lsp4j/WorkDoneProgressAndPartialResultParams
instanceKlass org/eclipse/lsp4j/PartialResultParams
instanceKlass com/github/cameltooling/lsp/internal/telemetry/TelemetryManager
instanceKlass org/eclipse/lsp4j/ServerCapabilities
instanceKlass org/eclipse/lsp4j/InitializeResult
instanceKlass org/eclipse/lsp4j/services/NotebookDocumentService
instanceKlass org/eclipse/lsp4j/WorkDoneProgressCancelParams
instanceKlass org/eclipse/lsp4j/SetTraceParams
instanceKlass org/eclipse/lsp4j/InitializedParams
instanceKlass org/eclipse/lsp4j/InitializeParams
instanceKlass org/eclipse/lsp4j/WorkDoneProgressParams
instanceKlass jdk/internal/vm/annotation/IntrinsicCandidate
instanceKlass org/eclipse/lsp4j/jsonrpc/json/JsonRpcMethodProvider
instanceKlass org/eclipse/lsp4j/jsonrpc/services/JsonDelegate
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/ServiceEndpoints getSupportedMethods (Ljava/lang/Class;Ljava/util/Set;)Ljava/util/Map; 29 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/services/ServiceEndpoints$$Lambda+0x0000029e9020ca38
instanceKlass org/eclipse/lsp4j/ApplyWorkspaceEditResponse
instanceKlass org/eclipse/lsp4j/WorkspaceFolder
instanceKlass org/eclipse/lsp4j/MessageActionItem
instanceKlass org/eclipse/lsp4j/ShowDocumentResult
instanceKlass org/eclipse/lsp4j/jsonrpc/services/JsonNotification
instanceKlass org/eclipse/lsp4j/jsonrpc/json/JsonRpcMethod
instanceKlass org/eclipse/lsp4j/jsonrpc/json/ResponseJsonAdapter
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/AnnotationUtil getParameterTypes (Ljava/lang/reflect/Method;)[Ljava/lang/reflect/Type; 17 <appendix> argL0 ; # org/eclipse/lsp4j/jsonrpc/services/AnnotationUtil$$Lambda+0x0000029e9020fc48
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/AnnotationUtil getParameterTypes (Ljava/lang/reflect/Method;)[Ljava/lang/reflect/Type; 7 <appendix> argL0 ; # org/eclipse/lsp4j/jsonrpc/services/AnnotationUtil$$Lambda+0x0000029e9020fa10
instanceKlass org/eclipse/lsp4j/jsonrpc/services/JsonRequest
instanceKlass org/eclipse/lsp4j/LogTraceParams
instanceKlass org/eclipse/lsp4j/ApplyWorkspaceEditParams
instanceKlass org/eclipse/lsp4j/WorkDoneProgressCreateParams
instanceKlass org/eclipse/lsp4j/RegistrationParams
instanceKlass org/eclipse/lsp4j/PublishDiagnosticsParams
instanceKlass org/eclipse/lsp4j/ShowDocumentParams
instanceKlass org/eclipse/lsp4j/ProgressParams
instanceKlass org/eclipse/lsp4j/UnregistrationParams
instanceKlass org/eclipse/lsp4j/MessageParams
instanceKlass org/eclipse/lsp4j/ConfigurationParams
instanceKlass org/eclipse/lsp4j/jsonrpc/services/JsonSegment
instanceKlass org/eclipse/lsp4j/jsonrpc/services/AnnotationUtil
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/ServiceEndpoints getSupportedMethods (Ljava/lang/Class;Ljava/util/Set;)Ljava/util/Map; 11 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/services/ServiceEndpoints$$Lambda+0x0000029e90207790
instanceKlass org/eclipse/lsp4j/jsonrpc/services/AnnotationUtil$MethodInfo
instanceKlass org/eclipse/lsp4j/jsonrpc/services/ServiceEndpoints
instanceKlass org/eclipse/lsp4j/services/LanguageClient
instanceKlass org/eclipse/lsp4j/jsonrpc/Launcher
instanceKlass org/eclipse/lsp4j/jsonrpc/Endpoint
instanceKlass org/eclipse/lsp4j/jsonrpc/json/MethodProvider
instanceKlass org/eclipse/lsp4j/jsonrpc/MessageProducer
instanceKlass org/eclipse/lsp4j/jsonrpc/MessageConsumer
instanceKlass org/eclipse/lsp4j/jsonrpc/MessageIssueHandler
instanceKlass org/eclipse/lsp4j/jsonrpc/Launcher$Builder
instanceKlass org/eclipse/lsp4j/launch/LSPLauncher
instanceKlass com/github/cameltooling/lsp/internal/CamelWorkspaceService
instanceKlass com/github/cameltooling/lsp/internal/settings/SettingsManager
instanceKlass java/util/concurrent/ForkJoinTask$Aux
instanceKlass java/util/concurrent/ForkJoinTask
instanceKlass java/util/concurrent/CompletableFuture$AsynchronousCompletionTask
instanceKlass java/util/concurrent/CompletableFuture$AltResult
instanceKlass java/util/concurrent/CompletableFuture
instanceKlass java/util/concurrent/CompletionStage
instanceKlass  @bci com/github/cameltooling/lsp/internal/CamelTextDocumentService <init> (Lcom/github/cameltooling/lsp/internal/CamelLanguageServer;)V 32 <appendix> argL0 ; # com/github/cameltooling/lsp/internal/CamelTextDocumentService$$Lambda+0x0000029e90205740
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$PreparedASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryConverter
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ExceptionalBinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIConverter
instanceKlass jdk/internal/math/FloatingDecimal
instanceKlass com/fasterxml/jackson/core/io/NumberInput
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e9020c000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e9020bc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e9020b800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e9020b400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e9020b000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e9020ac00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e9020a800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e9020a400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e9020a000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90209c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90209800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90209400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90209000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90208c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90208800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90208400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90208000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90203c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90203800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90203400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90203000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90202c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90202800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90202400
instanceKlass org/yaml/snakeyaml/parser/ParserImpl$ParseFlowMappingKey
instanceKlass org/yaml/snakeyaml/parser/ParserImpl$ParseFlowMappingFirstKey
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90202000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90201c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90201800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90201400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90201000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90200c00
instanceKlass org/yaml/snakeyaml/scanner/ScannerImpl$Chomping
instanceKlass org/yaml/snakeyaml/parser/ParserImpl$ParseFlowSequenceEntry
instanceKlass org/yaml/snakeyaml/parser/ParserImpl$ParseFlowSequenceFirstEntry
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90200800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90200400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90200000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e901fcc00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e901fc800
instanceKlass org/yaml/snakeyaml/parser/ParserImpl$ParseBlockSequenceEntryValue
instanceKlass org/yaml/snakeyaml/parser/ParserImpl$ParseBlockSequenceEntryKey
instanceKlass com/fasterxml/jackson/databind/deser/std/UntypedObjectDeserializerNR$Scope
instanceKlass org/yaml/snakeyaml/parser/ParserImpl$ParseBlockSequenceFirstEntry
instanceKlass org/yaml/snakeyaml/parser/ParserImpl$ParseIndentlessSequenceEntryValue
instanceKlass org/yaml/snakeyaml/parser/ParserImpl$ParseIndentlessSequenceEntryKey
instanceKlass com/fasterxml/jackson/databind/deser/BeanDeserializer$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e901fc400
instanceKlass org/yaml/snakeyaml/resolver/Resolver$1
instanceKlass io/fabric8/kubernetes/model/annotation/Kind
instanceKlass io/fabric8/kubernetes/client/utils/CachedSingleThreadScheduler
instanceKlass io/fabric8/kubernetes/client/utils/Utils
instanceKlass org/yaml/snakeyaml/events/ImplicitTuple
instanceKlass org/yaml/snakeyaml/parser/ParserImpl$ParseBlockMappingValue
instanceKlass org/yaml/snakeyaml/parser/ParserImpl$ParseBlockMappingKey
instanceKlass io/fabric8/kubernetes/api/model/FieldsV1
instanceKlass io/fabric8/kubernetes/api/model/ManagedFieldsEntry
instanceKlass org/apache/camel/v1/kameletspec/types/schema/Properties
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e901fc000
instanceKlass org/apache/camel/v1/kameletspec/types/schema/ExternalDocs
instanceKlass org/apache/camel/v1/kameletspec/types/Schema
instanceKlass org/apache/camel/v1/kameletspec/datatypes/types/schema/Properties
instanceKlass org/apache/camel/v1/kameletspec/datatypes/types/schema/ExternalDocs
instanceKlass org/apache/camel/v1/kameletspec/datatypes/types/Headers
instanceKlass org/apache/camel/v1/kameletspec/datatypes/types/Schema
instanceKlass org/apache/camel/v1/kameletspec/datatypes/Types
instanceKlass org/apache/camel/v1/kameletspec/datatypes/Headers
instanceKlass org/apache/camel/v1/kameletspec/versions/types/schema/Properties
instanceKlass org/apache/camel/v1/kameletspec/versions/types/schema/ExternalDocs
instanceKlass org/apache/camel/v1/kameletspec/versions/types/Schema
instanceKlass org/apache/camel/v1/kameletspec/versions/datatypes/types/schema/Properties
instanceKlass org/apache/camel/v1/kameletspec/versions/datatypes/types/schema/ExternalDocs
instanceKlass org/apache/camel/v1/kameletspec/versions/datatypes/types/Headers
instanceKlass org/apache/camel/v1/kameletspec/versions/datatypes/types/Schema
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e901f0400
instanceKlass org/apache/camel/v1/kameletspec/versions/datatypes/Types
instanceKlass org/apache/camel/v1/kameletspec/versions/datatypes/Headers
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e901f0000
instanceKlass org/apache/camel/v1/kameletspec/versions/definition/Properties
instanceKlass org/apache/camel/v1/kameletspec/versions/definition/ExternalDocs
instanceKlass org/apache/camel/v1/kameletspec/versions/Types
instanceKlass org/apache/camel/v1/kameletspec/versions/Sources
instanceKlass org/apache/camel/v1/kameletspec/versions/DataTypes
instanceKlass org/apache/camel/v1/kameletspec/versions/Template
instanceKlass org/apache/camel/v1/kameletspec/versions/Definition
instanceKlass com/fasterxml/jackson/databind/deser/SettableAnyProperty
instanceKlass com/fasterxml/jackson/databind/deser/ContextualKeyDeserializer
instanceKlass com/fasterxml/jackson/databind/BeanProperty$Std
instanceKlass com/fasterxml/jackson/databind/annotation/JacksonStdImpl
instanceKlass com/fasterxml/jackson/databind/deser/impl/CreatorCandidate$Param
instanceKlass com/fasterxml/jackson/databind/deser/impl/CreatorCandidate
instanceKlass com/fasterxml/jackson/databind/introspect/POJOPropertiesCollector$1
instanceKlass com/fasterxml/jackson/databind/introspect/PotentialCreator
instanceKlass com/fasterxml/jackson/annotation/JsonCreator
instanceKlass com/fasterxml/jackson/databind/introspect/AnnotationCollector$TwoAnnotations
instanceKlass org/apache/camel/v1/kameletspec/definition/Properties
instanceKlass org/apache/camel/v1/kameletspec/definition/ExternalDocs
instanceKlass io/fabric8/kubernetes/api/model/AnyType
instanceKlass org/apache/camel/v1/kameletspec/Versions
instanceKlass org/apache/camel/v1/kameletspec/Types
instanceKlass org/apache/camel/v1/kameletspec/Sources
instanceKlass org/apache/camel/v1/kameletspec/DataTypes
instanceKlass org/apache/camel/v1/kameletspec/Template
instanceKlass org/apache/camel/v1/kameletspec/Definition
instanceKlass com/fasterxml/jackson/databind/deser/std/DateDeserializers
instanceKlass com/fasterxml/jackson/databind/introspect/AnnotationCollector$OneAnnotation
instanceKlass jdk/internal/ValueBased
instanceKlass java/time/LocalDate
instanceKlass java/time/chrono/ChronoLocalDate
instanceKlass io/fabric8/generator/annotation/Required
instanceKlass java/time/ZonedDateTime
instanceKlass java/time/chrono/ChronoZonedDateTime
instanceKlass com/fasterxml/jackson/databind/util/LinkedNode
instanceKlass com/fasterxml/jackson/databind/deser/std/NumberDeserializers
instanceKlass com/fasterxml/jackson/databind/deser/BasicDeserializerFactory$ContainerDefaultMappings
instanceKlass org/apache/camel/v1/kameletstatus/Properties
instanceKlass org/apache/camel/v1/kameletstatus/Conditions
instanceKlass com/fasterxml/jackson/databind/deser/impl/ReadableObjectId$Referring
instanceKlass com/fasterxml/jackson/databind/deser/impl/BeanPropertyMap
instanceKlass com/fasterxml/jackson/annotation/JsonAlias
instanceKlass com/fasterxml/jackson/databind/introspect/POJOPropertyBuilder$1
instanceKlass com/fasterxml/jackson/databind/introspect/POJOPropertyBuilder$4
instanceKlass com/fasterxml/jackson/databind/introspect/POJOPropertyBuilder$2
instanceKlass com/fasterxml/jackson/databind/deser/impl/NullsConstantProvider
instanceKlass com/fasterxml/jackson/databind/annotation/JsonTypeResolver
instanceKlass com/fasterxml/jackson/annotation/JsonIgnoreType
instanceKlass com/fasterxml/jackson/databind/util/IgnorePropertiesUtil
instanceKlass com/fasterxml/jackson/annotation/JsonIncludeProperties$Value
instanceKlass com/fasterxml/jackson/annotation/JsonIncludeProperties
instanceKlass com/fasterxml/jackson/annotation/JsonIgnoreProperties$Value
instanceKlass com/fasterxml/jackson/annotation/JsonIgnoreProperties
instanceKlass com/fasterxml/jackson/databind/deser/BeanDeserializerBuilder
instanceKlass com/fasterxml/jackson/databind/deser/impl/CreatorCollector
instanceKlass sun/reflect/generics/tree/VoidDescriptor
instanceKlass com/fasterxml/jackson/databind/PropertyMetadata
instanceKlass com/fasterxml/jackson/annotation/JsonPropertyDescription
instanceKlass com/fasterxml/jackson/databind/annotation/JsonNaming
instanceKlass com/fasterxml/jackson/annotation/JacksonInject
instanceKlass java/util/LinkedList$ListItr
instanceKlass com/fasterxml/jackson/databind/introspect/POJOPropertyBuilder$6
instanceKlass com/fasterxml/jackson/databind/introspect/POJOPropertyBuilder$5
instanceKlass com/fasterxml/jackson/databind/cfg/ConstructorDetector
instanceKlass sun/reflect/generics/tree/MethodTypeSignature
instanceKlass com/fasterxml/jackson/annotation/JsonGetter
instanceKlass com/fasterxml/jackson/databind/introspect/AnnotatedMethodMap
instanceKlass io/fabric8/kubernetes/api/model/OwnerReference
instanceKlass com/fasterxml/jackson/databind/introspect/AnnotatedMethodCollector$MethodBuilder
instanceKlass com/fasterxml/jackson/databind/introspect/MemberKey
instanceKlass com/fasterxml/jackson/databind/introspect/POJOPropertyBuilder$Linked
instanceKlass com/fasterxml/jackson/databind/AnnotationIntrospector$ReferenceProperty
instanceKlass com/fasterxml/jackson/databind/introspect/POJOPropertyBuilder$WithMember
instanceKlass com/fasterxml/jackson/annotation/JsonIgnore
instanceKlass com/fasterxml/jackson/annotation/JsonAutoDetect$1
instanceKlass com/fasterxml/jackson/annotation/JsonSetter
instanceKlass com/fasterxml/jackson/annotation/JsonAnySetter
instanceKlass com/fasterxml/jackson/annotation/JsonAnyGetter
instanceKlass com/fasterxml/jackson/annotation/JsonValue
instanceKlass com/fasterxml/jackson/annotation/JsonKey
instanceKlass com/fasterxml/jackson/annotation/JsonProperty
instanceKlass com/fasterxml/jackson/databind/introspect/AnnotatedFieldCollector$FieldBuilder
instanceKlass io/fabric8/kubernetes/api/model/ObjectMeta
instanceKlass com/fasterxml/jackson/databind/introspect/TypeResolutionContext$Basic
instanceKlass com/fasterxml/jackson/databind/introspect/CollectorBase
instanceKlass com/fasterxml/jackson/databind/introspect/PotentialCreators
instanceKlass com/fasterxml/jackson/core/JsonLocation
instanceKlass com/fasterxml/jackson/databind/deser/ValueInstantiator
instanceKlass com/fasterxml/jackson/databind/deser/impl/JDKValueInstantiators
instanceKlass com/fasterxml/jackson/databind/annotation/JsonValueInstantiator
instanceKlass com/fasterxml/jackson/databind/util/BeanUtil
instanceKlass com/fasterxml/jackson/databind/jsontype/impl/SubTypeValidator
instanceKlass java/net/SocketAddress
instanceKlass com/fasterxml/jackson/databind/deser/std/JdkDeserializers
instanceKlass com/fasterxml/jackson/databind/ext/Java7Handlers
instanceKlass org/w3c/dom/Document
instanceKlass org/w3c/dom/Node
instanceKlass com/fasterxml/jackson/databind/ext/OptionalHandlerFactory
instanceKlass com/fasterxml/jackson/databind/util/ArrayIterator
instanceKlass com/fasterxml/jackson/databind/annotation/NoClass
instanceKlass com/fasterxml/jackson/annotation/JsonIdentityInfo
instanceKlass com/fasterxml/jackson/annotation/JsonAutoDetect
instanceKlass com/fasterxml/jackson/databind/introspect/POJOPropertiesCollector
instanceKlass com/fasterxml/jackson/databind/introspect/AnnotationMap
instanceKlass com/fasterxml/jackson/annotation/JacksonAnnotationsInside
instanceKlass com/fasterxml/jackson/annotation/JsonInclude
instanceKlass com/fasterxml/jackson/annotation/JsonPropertyOrder
instanceKlass com/fasterxml/jackson/annotation/JacksonAnnotation
instanceKlass com/fasterxml/jackson/databind/util/Converter$None
instanceKlass com/fasterxml/jackson/databind/util/Converter
instanceKlass io/fabric8/kubernetes/model/annotation/Plural
instanceKlass io/fabric8/kubernetes/model/annotation/Singular
instanceKlass io/fabric8/kubernetes/model/annotation/Group
instanceKlass io/fabric8/kubernetes/model/annotation/Version
instanceKlass org/yaml/snakeyaml/parser/ParserImpl$ParseBlockMappingFirstKey
instanceKlass org/yaml/snakeyaml/parser/ParserImpl$ParseBlockNode
instanceKlass org/yaml/snakeyaml/parser/ParserImpl$ParseDocumentEnd
instanceKlass org/yaml/snakeyaml/scanner/SimpleKey
instanceKlass org/yaml/snakeyaml/scanner/Constant
instanceKlass org/yaml/snakeyaml/parser/ParserImpl$ParseImplicitDocumentStart
instanceKlass com/fasterxml/jackson/databind/type/TypeBindings$AsKey
instanceKlass com/fasterxml/jackson/databind/util/internal/PrivateMaxEntriesMap$AddTask
instanceKlass com/fasterxml/jackson/databind/util/internal/PrivateMaxEntriesMap$WeightedValue
instanceKlass com/fasterxml/jackson/databind/type/TypeBindings$TypeParamStash
instanceKlass sun/reflect/generics/tree/TypeVariableSignature
instanceKlass io/fabric8/kubernetes/api/builder/BaseFluent
instanceKlass io/fabric8/kubernetes/api/builder/VisitableBuilder
instanceKlass io/fabric8/kubernetes/api/builder/Visitable
instanceKlass io/fabric8/kubernetes/api/builder/Builder
instanceKlass org/apache/camel/v1/KameletStatus
instanceKlass org/apache/camel/v1/KameletSpec
instanceKlass com/fasterxml/jackson/databind/type/ClassStack
instanceKlass org/yaml/snakeyaml/resolver/ResolverTuple
instanceKlass org/yaml/snakeyaml/external/com/google/gdata/util/common/base/UnicodeEscaper
instanceKlass org/yaml/snakeyaml/external/com/google/gdata/util/common/base/Escaper
instanceKlass org/yaml/snakeyaml/util/UriEncoder
instanceKlass org/yaml/snakeyaml/nodes/Tag
instanceKlass org/yaml/snakeyaml/resolver/Resolver
instanceKlass org/yaml/snakeyaml/parser/ParserImpl$ParseStreamStart
instanceKlass org/yaml/snakeyaml/parser/VersionTagsTuple
instanceKlass org/yaml/snakeyaml/error/Mark
instanceKlass org/yaml/snakeyaml/util/ArrayStack
instanceKlass org/yaml/snakeyaml/tokens/Token
instanceKlass org/yaml/snakeyaml/scanner/ScannerImpl
instanceKlass org/yaml/snakeyaml/inspector/UnTrustedTagInspector
instanceKlass org/yaml/snakeyaml/inspector/TagInspector
instanceKlass org/yaml/snakeyaml/LoaderOptions
instanceKlass org/yaml/snakeyaml/reader/StreamReader
instanceKlass org/yaml/snakeyaml/events/Event
instanceKlass org/yaml/snakeyaml/parser/Production
instanceKlass org/yaml/snakeyaml/scanner/Scanner
instanceKlass org/yaml/snakeyaml/parser/ParserImpl
instanceKlass org/yaml/snakeyaml/parser/Parser
instanceKlass com/fasterxml/jackson/core/JsonStreamContext
instanceKlass com/fasterxml/jackson/core/util/TextBuffer
instanceKlass com/fasterxml/jackson/core/io/IOContext
instanceKlass com/fasterxml/jackson/core/util/BufferRecycler
instanceKlass com/fasterxml/jackson/core/util/BufferRecyclers
instanceKlass com/fasterxml/jackson/core/io/ContentReference
instanceKlass io/fabric8/kubernetes/client/CustomResource
instanceKlass io/fabric8/kubernetes/api/model/HasMetadata
instanceKlass io/fabric8/kubernetes/api/model/KubernetesResource
instanceKlass io/fabric8/kubernetes/api/builder/Editable
instanceKlass io/fabric8/kubernetes/api/model/Namespaced
instanceKlass io/github/classgraph/PotentiallyUnmodifiableList$1
instanceKlass io/github/classgraph/ResourceList$1
instanceKlass io/github/classgraph/ResourceList$ResourceFilter
instanceKlass java/util/IdentityHashMap$IdentityHashMapIterator
instanceKlass nonapi/io/github/classgraph/utils/URLPathEncoder
instanceKlass io/github/classgraph/Scanner$4
instanceKlass io/github/classgraph/Scanner$3
instanceKlass nonapi/io/github/classgraph/fastzipfilereader/NestedJarHandler$RecyclableInflater
instanceKlass nonapi/io/github/classgraph/recycler/Resettable
instanceKlass nonapi/io/github/classgraph/fastzipfilereader/FastZipEntry
instanceKlass nonapi/io/github/classgraph/utils/StringUtils
instanceKlass nonapi/io/github/classgraph/fileslice/reader/RandomAccessArrayReader
instanceKlass sun/nio/ch/Util$BufferCache
instanceKlass sun/nio/ch/Util
instanceKlass nonapi/io/github/classgraph/fileslice/reader/RandomAccessFileChannelReader
instanceKlass nonapi/io/github/classgraph/fastzipfilereader/ZipFileSlice
instanceKlass nonapi/io/github/classgraph/fileslice/reader/RandomAccessReader
instanceKlass nonapi/io/github/classgraph/fastzipfilereader/PhysicalZipFile
instanceKlass java/util/concurrent/ForkJoinPool$2
instanceKlass jdk/internal/access/JavaUtilConcurrentFJPAccess
instanceKlass sun/nio/fs/WindowsLinkSupport
instanceKlass java/util/concurrent/ForkJoinPool$DefaultForkJoinWorkerThreadFactory
instanceKlass java/util/concurrent/ForkJoinPool$WorkQueue
instanceKlass java/util/concurrent/ForkJoinPool$ForkJoinWorkerThreadFactory
instanceKlass io/github/classgraph/Resource
instanceKlass io/github/classgraph/ClasspathElement
instanceKlass io/github/classgraph/Scanner$2$1
instanceKlass nonapi/io/github/classgraph/concurrency/WorkQueue$1
instanceKlass nonapi/io/github/classgraph/concurrency/WorkQueue$WorkUnitWrapper
instanceKlass nonapi/io/github/classgraph/concurrency/WorkQueue
instanceKlass nonapi/io/github/classgraph/concurrency/SingletonMap$NewInstanceFactory
instanceKlass io/github/classgraph/Scanner$2
instanceKlass io/github/classgraph/Scanner$ClasspathEntryWorkUnit
instanceKlass java/lang/System$Logger
instanceKlass java/io/Console
instanceKlass java/util/concurrent/FutureTask$WaitNode
instanceKlass java/util/concurrent/FutureTask
instanceKlass java/util/concurrent/RunnableFuture
instanceKlass nonapi/io/github/classgraph/classpath/SystemJarFinder$1
instanceKlass nonapi/io/github/classgraph/classpath/SystemJarFinder
instanceKlass nonapi/io/github/classgraph/utils/JarUtils
instanceKlass nonapi/io/github/classgraph/classpath/ClasspathOrder$ClasspathEntry
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e901a9000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e901a8c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e901a8800
# instanceKlass nonapi/io/github/classgraph/reflection/StandardReflectionDriver$$InjectedInvoker+0x0000029e901a8400
instanceKlass nonapi/io/github/classgraph/reflection/StandardReflectionDriver$PrivilegedActionInvocationHandler
instanceKlass nonapi/io/github/classgraph/reflection/StandardReflectionDriver$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e901a8000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e901a7c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e901a7800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e901a7400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e901a7000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e901a6c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e901a6800
instanceKlass  @cpi org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint 220 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000029e901a6400
instanceKlass java/util/AbstractMap$SimpleEntry
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e901a6000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e901a5c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e901a5800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e901a5400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e901a5000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e901a4c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e901a4800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e901a4400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e901a4000
instanceKlass nonapi/io/github/classgraph/classpath/ClassLoaderFinder
instanceKlass nonapi/io/github/classgraph/classloaderhandler/FallbackClassLoaderHandler
instanceKlass nonapi/io/github/classgraph/classloaderhandler/ClassGraphClassLoaderHandler
instanceKlass nonapi/io/github/classgraph/classloaderhandler/URLClassLoaderHandler
instanceKlass nonapi/io/github/classgraph/classloaderhandler/JPMSClassLoaderHandler
instanceKlass nonapi/io/github/classgraph/classloaderhandler/ParentLastDelegationOrderTestClassLoaderHandler
instanceKlass nonapi/io/github/classgraph/classloaderhandler/UnoOneJarClassLoaderHandler
instanceKlass nonapi/io/github/classgraph/classloaderhandler/QuarkusClassLoaderHandler
instanceKlass nonapi/io/github/classgraph/classloaderhandler/PlexusClassWorldsClassRealmClassLoaderHandler
instanceKlass nonapi/io/github/classgraph/classloaderhandler/CxfContainerClassLoaderHandler
instanceKlass nonapi/io/github/classgraph/classloaderhandler/TomcatWebappClassLoaderBaseHandler
instanceKlass nonapi/io/github/classgraph/classloaderhandler/SpringBootRestartClassLoaderHandler
instanceKlass nonapi/io/github/classgraph/classloaderhandler/OSGiDefaultClassLoaderHandler
instanceKlass nonapi/io/github/classgraph/classloaderhandler/WebsphereTraditionalClassLoaderHandler
instanceKlass nonapi/io/github/classgraph/classloaderhandler/WebsphereLibertyClassLoaderHandler
instanceKlass nonapi/io/github/classgraph/classloaderhandler/WeblogicClassLoaderHandler
instanceKlass nonapi/io/github/classgraph/classloaderhandler/JBossClassLoaderHandler
instanceKlass nonapi/io/github/classgraph/classloaderhandler/FelixClassLoaderHandler
instanceKlass nonapi/io/github/classgraph/classloaderhandler/EquinoxContextFinderClassLoaderHandler
instanceKlass nonapi/io/github/classgraph/classloaderhandler/EquinoxClassLoaderHandler
instanceKlass nonapi/io/github/classgraph/classpath/ClassLoaderOrder
instanceKlass nonapi/io/github/classgraph/utils/LogNode
instanceKlass nonapi/io/github/classgraph/classloaderhandler/AntClassLoaderHandler
instanceKlass nonapi/io/github/classgraph/classloaderhandler/ClassLoaderHandler
instanceKlass nonapi/io/github/classgraph/classloaderhandler/ClassLoaderHandlerRegistry$ClassLoaderHandlerRegistryEntry
instanceKlass nonapi/io/github/classgraph/classloaderhandler/ClassLoaderHandlerRegistry
instanceKlass nonapi/io/github/classgraph/classpath/ClasspathOrder
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e9019ac00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e9019a800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e9019a400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e9019a000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90199c00
instanceKlass io/github/classgraph/ModuleRef
instanceKlass jdk/internal/classfile/ClassElement
instanceKlass jdk/internal/classfile/ClassfileElement
instanceKlass jdk/internal/classfile/ClassBuilder
instanceKlass jdk/internal/classfile/ClassfileBuilder
instanceKlass java/util/LinkedList$Node
instanceKlass java/lang/reflect/AnnotatedType
instanceKlass java/lang/Class$EnclosingMethodInfo
instanceKlass  @bci java/lang/StackWalker forEach (Ljava/util/function/Consumer;)V 7 <appendix> member <vmtarget> ; # java/lang/StackWalker$$Lambda+0x0000029e9014a5c0
# instanceKlass nonapi/io/github/classgraph/classpath/CallStackReader$$InjectedInvoker+0x0000029e90199800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90199400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90199000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90198c00
instanceKlass nonapi/io/github/classgraph/classpath/CallStackReader$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90198800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90198400
instanceKlass nonapi/io/github/classgraph/classpath/CallStackReader$2
instanceKlass nonapi/io/github/classgraph/classpath/CallStackReader
instanceKlass nonapi/io/github/classgraph/classpath/ModuleFinder
instanceKlass nonapi/io/github/classgraph/classpath/ClasspathFinder
instanceKlass nonapi/io/github/classgraph/fileslice/Slice
instanceKlass nonapi/io/github/classgraph/recycler/Recycler
instanceKlass nonapi/io/github/classgraph/fastzipfilereader/NestedJarHandler
instanceKlass java/util/ComparableTimSort
instanceKlass nonapi/io/github/classgraph/utils/CollectionUtils
instanceKlass nonapi/io/github/classgraph/concurrency/WorkQueue$WorkUnitProcessor
instanceKlass io/github/classgraph/Scanner
instanceKlass nonapi/io/github/classgraph/concurrency/InterruptionChecker
instanceKlass nonapi/io/github/classgraph/concurrency/SimpleThreadFactory
instanceKlass nonapi/io/github/classgraph/utils/FastPathResolver
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90198000
instanceKlass nonapi/io/github/classgraph/utils/FileUtils$2
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90195c00
instanceKlass sun/misc/Unsafe
instanceKlass nonapi/io/github/classgraph/utils/VersionFinder
# instanceKlass nonapi/io/github/classgraph/reflection/ReflectionUtils$$InjectedInvoker+0x0000029e90195800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90195400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90195000
instanceKlass nonapi/io/github/classgraph/reflection/ReflectionUtils$PrivilegedActionInvocationHandler
instanceKlass nonapi/io/github/classgraph/utils/FileUtils$1
instanceKlass nonapi/io/github/classgraph/utils/FileUtils$FileAttributesGetter
instanceKlass nonapi/io/github/classgraph/utils/FileUtils
instanceKlass io/github/classgraph/ScanResult
# instanceKlass nonapi/io/github/classgraph/reflection/ReflectionDriver$$InjectedInvoker+0x0000029e90194c00
instanceKlass java/lang/invoke/MethodHandleImpl$BindCaller$InjectedInvokerHolder
instanceKlass java/lang/invoke/MethodHandleImpl$BindCaller
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90194800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90194400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90194000
instanceKlass java/security/DomainCombiner
instanceKlass nonapi/io/github/classgraph/reflection/ReflectionDriver$ClassMemberCache
instanceKlass java/util/concurrent/CountDownLatch
instanceKlass nonapi/io/github/classgraph/concurrency/SingletonMap$SingletonHolder
instanceKlass nonapi/io/github/classgraph/concurrency/SingletonMap
instanceKlass nonapi/io/github/classgraph/reflection/ReflectionDriver
instanceKlass nonapi/io/github/classgraph/reflection/ReflectionUtils
instanceKlass io/github/classgraph/ModulePathInfo
instanceKlass nonapi/io/github/classgraph/scanspec/AcceptReject
instanceKlass nonapi/io/github/classgraph/scanspec/ScanSpec
instanceKlass io/github/classgraph/ClassGraph
instanceKlass org/apache/camel/catalog/CamelCatalogJSonSchemaResolver
instanceKlass org/apache/camel/catalog/DefaultRuntimeProvider
instanceKlass org/apache/camel/catalog/DefaultVersionManager
instanceKlass org/apache/camel/catalog/VersionHelper
instanceKlass org/apache/camel/catalog/JSonSchemaResolver
instanceKlass org/apache/camel/catalog/RuntimeProvider
instanceKlass org/apache/camel/tooling/model/BaseModel
instanceKlass org/apache/camel/catalog/impl/AbstractCamelCatalog
instanceKlass com/fasterxml/jackson/databind/ser/BeanSerializerModifier
instanceKlass com/fasterxml/jackson/databind/ser/Serializers
instanceKlass com/fasterxml/jackson/databind/cfg/SerializerFactoryConfig
instanceKlass java/util/Currency
instanceKlass com/fasterxml/jackson/databind/ser/std/StdJdkSerializers
instanceKlass com/fasterxml/jackson/databind/ser/std/NumberSerializers
instanceKlass com/fasterxml/jackson/databind/deser/DeserializerCache
instanceKlass com/fasterxml/jackson/databind/KeyDeserializer
instanceKlass com/fasterxml/jackson/databind/deser/std/StdKeyDeserializers
instanceKlass com/fasterxml/jackson/databind/deser/KeyDeserializers
instanceKlass com/fasterxml/jackson/databind/deser/ValueInstantiators
instanceKlass com/fasterxml/jackson/databind/AbstractTypeResolver
instanceKlass com/fasterxml/jackson/databind/deser/BeanDeserializerModifier
instanceKlass com/fasterxml/jackson/databind/cfg/DeserializerFactoryConfig
instanceKlass com/fasterxml/jackson/databind/PropertyName
instanceKlass com/fasterxml/jackson/databind/deser/Deserializers
instanceKlass com/fasterxml/jackson/annotation/ObjectIdGenerator
instanceKlass com/fasterxml/jackson/databind/deser/ResolvableDeserializer
instanceKlass com/fasterxml/jackson/databind/deser/ValueInstantiator$Gettable
instanceKlass com/fasterxml/jackson/databind/deser/ContextualDeserializer
instanceKlass com/fasterxml/jackson/databind/JsonDeserializer
instanceKlass com/fasterxml/jackson/databind/deser/NullValueProvider
instanceKlass com/fasterxml/jackson/databind/ser/SerializerCache
instanceKlass com/fasterxml/jackson/databind/ser/ResolvableSerializer
instanceKlass com/fasterxml/jackson/databind/ser/ContextualSerializer
instanceKlass com/fasterxml/jackson/databind/jsonschema/SchemaAware
instanceKlass com/fasterxml/jackson/databind/JsonSerializer
instanceKlass com/fasterxml/jackson/databind/jsonFormatVisitors/JsonFormatVisitable
instanceKlass com/fasterxml/jackson/databind/node/JsonNodeFactory
instanceKlass com/fasterxml/jackson/databind/node/JsonNodeCreator
instanceKlass com/fasterxml/jackson/databind/cfg/ContextAttributes
instanceKlass com/fasterxml/jackson/databind/cfg/DatatypeFeature
instanceKlass com/fasterxml/jackson/databind/cfg/DatatypeFeatures$DefaultHolder
instanceKlass com/fasterxml/jackson/databind/cfg/DatatypeFeatures
instanceKlass com/fasterxml/jackson/core/util/DefaultPrettyPrinter$NopIndenter
instanceKlass com/fasterxml/jackson/core/util/Separators
instanceKlass com/fasterxml/jackson/core/util/DefaultPrettyPrinter$Indenter
instanceKlass com/fasterxml/jackson/core/util/DefaultPrettyPrinter
instanceKlass com/fasterxml/jackson/core/util/Instantiatable
instanceKlass com/fasterxml/jackson/databind/cfg/ConfigFeature
instanceKlass com/fasterxml/jackson/databind/cfg/ConfigOverride
instanceKlass com/fasterxml/jackson/annotation/JsonFormat$Features
instanceKlass com/fasterxml/jackson/annotation/JsonFormat$Value
instanceKlass com/fasterxml/jackson/core/PrettyPrinter
instanceKlass com/fasterxml/jackson/databind/cfg/CoercionConfig
instanceKlass com/fasterxml/jackson/databind/cfg/CoercionConfigs
instanceKlass com/fasterxml/jackson/databind/introspect/VisibilityChecker$Std
instanceKlass com/fasterxml/jackson/annotation/JsonSetter$Value
instanceKlass com/fasterxml/jackson/annotation/JsonInclude$Value
instanceKlass com/fasterxml/jackson/annotation/JacksonAnnotationValue
instanceKlass com/fasterxml/jackson/databind/cfg/ConfigOverrides
instanceKlass com/fasterxml/jackson/databind/introspect/AnnotatedClass$Creators
instanceKlass com/fasterxml/jackson/databind/introspect/AnnotationCollector$NoAnnotations
instanceKlass com/fasterxml/jackson/databind/util/Annotations
instanceKlass com/fasterxml/jackson/databind/introspect/AnnotationCollector
instanceKlass com/fasterxml/jackson/databind/introspect/AnnotatedClassResolver
instanceKlass com/fasterxml/jackson/databind/BeanDescription
instanceKlass com/fasterxml/jackson/databind/cfg/MapperConfig
instanceKlass com/fasterxml/jackson/databind/introspect/SimpleMixInResolver
instanceKlass com/fasterxml/jackson/databind/introspect/ClassIntrospector$MixInResolver
instanceKlass com/fasterxml/jackson/databind/util/RootNameLookup
instanceKlass com/fasterxml/jackson/core/sym/CharsToNameCanonicalizer$Bucket
instanceKlass com/fasterxml/jackson/core/sym/CharsToNameCanonicalizer$TableInfo
instanceKlass com/fasterxml/jackson/core/sym/CharsToNameCanonicalizer
instanceKlass com/fasterxml/jackson/core/ErrorReportConfiguration
instanceKlass com/fasterxml/jackson/core/StreamWriteConstraints
instanceKlass com/fasterxml/jackson/core/StreamReadConstraints
instanceKlass com/fasterxml/jackson/core/util/RecyclerPool$WithPool
instanceKlass com/fasterxml/jackson/core/util/RecyclerPool$ThreadLocalPoolBase
instanceKlass com/fasterxml/jackson/core/util/RecyclerPool
instanceKlass com/fasterxml/jackson/core/util/JsonRecyclerPools
instanceKlass com/fasterxml/jackson/core/sym/ByteQuadsCanonicalizer$TableInfo
instanceKlass com/fasterxml/jackson/core/sym/ByteQuadsCanonicalizer
instanceKlass org/apache/camel/kamelets/catalog/KameletsCatalog
instanceKlass com/github/cameltooling/lsp/internal/catalog/util/KameletsCatalogManager
instanceKlass org/apache/camel/catalog/VersionManager
instanceKlass org/apache/camel/catalog/CamelCatalog
instanceKlass com/github/cameltooling/lsp/internal/CamelTextDocumentService
instanceKlass org/apache/logging/log4j/core/Logger$PrivateConfig
instanceKlass  @bci org/apache/logging/log4j/core/util/internal/InternalLoggerRegistry computeIfAbsent (Ljava/lang/String;Lorg/apache/logging/log4j/message/MessageFactory;Ljava/util/function/BiFunction;)Lorg/apache/logging/log4j/core/Logger; 51 <appendix> argL0 ; # org/apache/logging/log4j/core/util/internal/InternalLoggerRegistry$$Lambda+0x0000029e9011baf0
instanceKlass  @bci org/apache/logging/log4j/core/util/internal/InternalLoggerRegistry getLogger (Ljava/lang/String;Lorg/apache/logging/log4j/message/MessageFactory;)Lorg/apache/logging/log4j/core/Logger; 48 <appendix> argL0 ; # org/apache/logging/log4j/core/util/internal/InternalLoggerRegistry$$Lambda+0x0000029e9011b8b8
instanceKlass  @bci org/apache/logging/log4j/core/util/internal/InternalLoggerRegistry getLogger (Ljava/lang/String;Lorg/apache/logging/log4j/message/MessageFactory;)Lorg/apache/logging/log4j/core/Logger; 40 <appendix> member <vmtarget> ; # org/apache/logging/log4j/core/util/internal/InternalLoggerRegistry$$Lambda+0x0000029e9011b678
instanceKlass  @bci org/apache/logging/log4j/core/util/internal/InternalLoggerRegistry getLogger (Ljava/lang/String;Lorg/apache/logging/log4j/message/MessageFactory;)Lorg/apache/logging/log4j/core/Logger; 31 <appendix> member <vmtarget> ; # org/apache/logging/log4j/core/util/internal/InternalLoggerRegistry$$Lambda+0x0000029e9011b438
instanceKlass  @bci org/apache/logging/log4j/core/LoggerContext getLogger (Ljava/lang/String;Lorg/apache/logging/log4j/message/MessageFactory;)Lorg/apache/logging/log4j/core/Logger; 19 <appendix> member <vmtarget> ; # org/apache/logging/log4j/core/LoggerContext$$Lambda+0x0000029e9011b208
instanceKlass org/apache/logging/slf4j/Log4jLogger
instanceKlass org/slf4j/spi/LocationAwareLogger
instanceKlass org/apache/logging/log4j/core/util/DefaultShutdownCallbackRegistry$RegisteredCancellable
instanceKlass org/apache/logging/log4j/core/LoggerContext$1
instanceKlass org/apache/logging/log4j/core/util/ExecutorServices
instanceKlass org/apache/logging/log4j/core/impl/Log4jLogEvent
instanceKlass org/apache/logging/log4j/core/jmx/internal/JmxUtil
instanceKlass java/util/EventObject
instanceKlass  @bci org/apache/logging/log4j/core/LoggerContext updateLoggers (Lorg/apache/logging/log4j/core/config/Configuration;)V 13 <appendix> member <vmtarget> ; # org/apache/logging/log4j/core/LoggerContext$$Lambda+0x0000029e901170c8
instanceKlass  @bci org/apache/logging/log4j/core/util/internal/InternalLoggerRegistry getLoggers ()Ljava/util/Collection; 33 <appendix> argL0 ; # org/apache/logging/log4j/core/util/internal/InternalLoggerRegistry$$Lambda+0x0000029e90116e90
instanceKlass  @bci org/apache/logging/log4j/core/util/internal/InternalLoggerRegistry getLoggers ()Ljava/util/Collection; 23 <appendix> argL0 ; # org/apache/logging/log4j/core/util/internal/InternalLoggerRegistry$$Lambda+0x0000029e90116c58
instanceKlass java/util/WeakHashMap$WeakHashMapSpliterator
instanceKlass org/apache/logging/log4j/core/util/NameUtil
instanceKlass jdk/internal/vm/ThreadContainers
instanceKlass jdk/internal/vm/StackableScope
instanceKlass java/util/concurrent/ThreadPoolExecutor$AbortPolicy
instanceKlass java/util/concurrent/RejectedExecutionHandler
instanceKlass java/util/concurrent/AbstractExecutorService
instanceKlass java/time/Instant
instanceKlass java/nio/file/attribute/FileTime$1
instanceKlass org/apache/logging/log4j/core/appender/ConfigurationFactoryData
instanceKlass org/apache/logging/log4j/core/util/Log4jThreadFactory
instanceKlass java/util/concurrent/Semaphore
instanceKlass org/apache/logging/log4j/core/appender/rolling/RollingFileManager$RollingFileManagerFactory
instanceKlass org/apache/logging/log4j/core/appender/FileManager$FileManagerFactory
instanceKlass org/apache/logging/log4j/core/filter/AbstractFilterable$Builder
instanceKlass org/apache/logging/log4j/core/appender/rolling/PatternProcessor
instanceKlass org/apache/logging/log4j/core/appender/rolling/DefaultRolloverStrategy$Builder
instanceKlass org/apache/logging/log4j/core/appender/rolling/RolloverDescription
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90113000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90112c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90112800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90112400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90112000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90111c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90111800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90111400
# instanceKlass java/lang/invoke/LambdaForm$BMH+0x0000029e90111000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90110c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90110800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90110400
instanceKlass java/lang/invoke/ClassSpecializer$Factory$1Var
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90110000
instanceKlass sun/invoke/util/ValueConversions$1
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e9010dc00
instanceKlass java/nio/file/FileVisitor
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e9010d800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e9010d400
instanceKlass org/apache/logging/log4j/core/appender/rolling/action/IfLastModified$Builder
instanceKlass java/text/ParsePosition
instanceKlass  @bci java/util/regex/Pattern SingleI (II)Ljava/util/regex/Pattern$BmpCharPredicate; 2 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x0000029e90142d28
instanceKlass  @bci java/util/regex/Pattern CIRange (II)Ljava/util/regex/Pattern$CharPredicate; 2 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x0000029e90142ad0
instanceKlass org/apache/logging/log4j/core/appender/rolling/FileSize
instanceKlass  @bci jdk/internal/reflect/MethodHandleIntegerFieldAccessorImpl setInt (Ljava/lang/Object;I)V 41 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000029e9010d000
instanceKlass org/apache/logging/log4j/core/appender/rolling/TimeBasedTriggeringPolicy$Builder
instanceKlass org/apache/logging/log4j/core/layout/PatternLayout$NoFormatPatternSerializer
instanceKlass org/apache/logging/log4j/core/util/datetime/FastDatePrinter$TwoDigitNumberField
instanceKlass org/apache/logging/log4j/core/util/datetime/FastDatePrinter$TwoDigitMonthField
instanceKlass org/apache/logging/log4j/core/util/datetime/FastDatePrinter$CharacterLiteral
instanceKlass org/apache/logging/log4j/core/util/datetime/FastDatePrinter$PaddedNumberField
instanceKlass org/apache/logging/log4j/core/util/datetime/FastDatePrinter$NumberRule
instanceKlass org/apache/logging/log4j/core/util/datetime/FastDatePrinter$Rule
instanceKlass org/apache/logging/log4j/core/util/datetime/FastDatePrinter
instanceKlass org/apache/logging/log4j/core/util/datetime/FormatCache$MultipartKey
instanceKlass org/apache/logging/log4j/core/util/datetime/FormatCache
instanceKlass org/apache/logging/log4j/core/util/datetime/Format
instanceKlass org/apache/logging/log4j/core/util/datetime/DatePrinter
instanceKlass org/apache/logging/log4j/core/config/Loggers
instanceKlass  @bci jdk/internal/reflect/MethodHandleIntegerFieldAccessorImpl getInt (Ljava/lang/Object;)I 20 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000029e9010cc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e9010c800
# instanceKlass java/lang/invoke/LambdaForm$BMH+0x0000029e9010c400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e9010c000
instanceKlass org/apache/logging/log4j/core/config/LoggerConfig$RootLogger$Builder
instanceKlass java/util/concurrent/atomic/AtomicBoolean
instanceKlass org/apache/logging/log4j/core/config/AwaitCompletionReliabilityStrategy
instanceKlass org/apache/logging/log4j/core/config/ReliabilityStrategyFactory
instanceKlass org/apache/logging/log4j/core/util/Assert
instanceKlass org/apache/logging/log4j/core/config/plugins/validation/validators/RequiredValidator
instanceKlass org/apache/logging/log4j/core/config/plugins/validation/ConstraintValidator
instanceKlass org/apache/logging/log4j/core/config/plugins/validation/constraints/Required
instanceKlass  @bci jdk/internal/reflect/MethodHandleObjectFieldAccessorImpl set (Ljava/lang/Object;Ljava/lang/Object;)V 41 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000029e90103000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90102c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90102800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90102400
instanceKlass org/apache/logging/log4j/core/config/plugins/PluginBuilderAttribute
instanceKlass org/apache/logging/log4j/core/util/TypeUtil
instanceKlass java/lang/Deprecated
instanceKlass org/apache/logging/log4j/core/config/LoggerConfig$LevelAndRefs
instanceKlass org/apache/logging/log4j/core/config/LoggerConfig$Builder
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90102000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90101c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90101800
instanceKlass org/apache/logging/log4j/core/lookup/PropertiesLookup$ConfigurationPropertyResult
instanceKlass org/apache/logging/log4j/core/config/plugins/PluginElement
instanceKlass org/apache/logging/log4j/core/lookup/DefaultLookupResult
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90101400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90101000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90100400
instanceKlass org/apache/logging/log4j/core/config/plugins/validation/Constraint
instanceKlass org/apache/logging/log4j/core/config/plugins/validation/ConstraintValidators
instanceKlass org/apache/logging/log4j/util/StringBuilders
instanceKlass org/apache/logging/log4j/core/appender/rolling/action/Duration
instanceKlass org/apache/logging/log4j/core/util/CronExpression
instanceKlass java/lang/reflect/WildcardType
instanceKlass sun/reflect/generics/tree/Wildcard
instanceKlass sun/reflect/generics/tree/BottomSignature
instanceKlass sun/reflect/generics/tree/CharSignature
instanceKlass sun/reflect/generics/tree/ArrayTypeSignature
instanceKlass sun/reflect/generics/tree/ByteSignature
instanceKlass sun/reflect/generics/reflectiveObjects/LazyReflectiveObjectGenerator
instanceKlass sun/reflect/generics/reflectiveObjects/ParameterizedTypeImpl
instanceKlass java/lang/reflect/ParameterizedType
instanceKlass sun/reflect/generics/tree/ClassSignature
instanceKlass sun/reflect/generics/tree/Signature
instanceKlass sun/reflect/generics/tree/FormalTypeParameter
instanceKlass java/lang/reflect/TypeVariable
instanceKlass sun/reflect/generics/repository/AbstractRepository
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverterRegistry
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters
instanceKlass org/apache/logging/log4j/core/config/plugins/visitors/AbstractPluginVisitor
instanceKlass org/apache/logging/log4j/core/config/plugins/visitors/PluginVisitor
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint recursiveFindRpcMethods (Ljava/lang/Object;Ljava/util/Set;Ljava/util/Set;)V 24 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000029e90100000
instanceKlass  @bci sun/reflect/annotation/AnnotationParser parseEnumArray (ILjava/lang/Class;Ljava/nio/ByteBuffer;Ljdk/internal/reflect/ConstantPool;Ljava/lang/Class;)Ljava/lang/Object; 16 <appendix> member <vmtarget> ; # sun/reflect/annotation/AnnotationParser$$Lambda+0x0000029e900fd730
instanceKlass org/apache/logging/log4j/core/config/plugins/visitors/PluginVisitors
instanceKlass org/apache/logging/log4j/core/config/plugins/PluginConfiguration
instanceKlass org/apache/logging/log4j/core/config/plugins/PluginValue
instanceKlass org/apache/logging/log4j/core/config/plugins/PluginVisitorStrategy
instanceKlass java/lang/Byte$ByteCache
instanceKlass java/lang/Short$ShortCache
instanceKlass org/apache/logging/log4j/core/config/plugins/PluginAttribute
instanceKlass org/apache/logging/log4j/core/config/plugins/PluginFactory
instanceKlass org/apache/logging/log4j/core/config/plugins/PluginBuilderFactory
instanceKlass org/apache/logging/log4j/core/config/plugins/util/PluginBuilder
instanceKlass org/apache/logging/log4j/core/config/plugins/PluginAliases
instanceKlass org/apache/logging/log4j/core/config/Scheduled
instanceKlass java/util/Formatter$Flags
instanceKlass java/util/Formattable
instanceKlass java/util/Formatter$FixedString
instanceKlass java/util/Formatter$FormatSpecifier
instanceKlass java/util/Formatter$FormatString
instanceKlass java/util/Formatter$Conversion
instanceKlass java/util/Formatter
instanceKlass org/apache/logging/log4j/core/Version
instanceKlass java/net/InetAddress$CachedLocalHost
instanceKlass java/util/concurrent/atomic/Striped64$1
instanceKlass jdk/internal/util/random/RandomSupport
instanceKlass java/util/Random
instanceKlass java/util/random/RandomGenerator
instanceKlass java/net/InetAddress$CachedLookup
instanceKlass sun/net/InetAddressCachePolicy$1
instanceKlass sun/net/InetAddressCachePolicy
instanceKlass  @bci java/net/InetAddress getAddressesFromNameService (Ljava/lang/String;)[Ljava/net/InetAddress; 94 <appendix> argL0 ; # java/net/InetAddress$$Lambda+0x0000029e9007f278
instanceKlass  @bci org/apache/logging/log4j/util/SortedArrayStringMap <clinit> ()V 0 <bsm> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000029e900f8c00
instanceKlass java/lang/invoke/MethodHandle$1
instanceKlass java/net/Inet6Address$Inet6AddressHolder
instanceKlass  @bci java/net/InetAddress loadResolver ()Ljava/net/spi/InetAddressResolver; 8 <appendix> argL0 ; # java/net/InetAddress$$Lambda+0x0000029e9007ec00
instanceKlass java/net/spi/InetAddressResolverProvider
instanceKlass java/net/InetAddress$NameServiceAddresses
instanceKlass java/net/InetAddress$Addresses
instanceKlass java/util/concurrent/ConcurrentSkipListMap$Iter
instanceKlass java/net/InetAddress$PlatformResolver
instanceKlass java/net/spi/InetAddressResolver
instanceKlass java/net/spi/InetAddressResolver$LookupPolicy
instanceKlass java/net/Inet4AddressImpl
instanceKlass java/net/Inet6AddressImpl
instanceKlass java/net/InetAddressImpl
instanceKlass java/util/concurrent/ConcurrentSkipListMap$Node
instanceKlass java/util/concurrent/ConcurrentSkipListMap$Index
instanceKlass java/util/concurrent/ConcurrentNavigableMap
instanceKlass java/net/InetAddress$InetAddressHolder
instanceKlass java/net/InetAddress$1
instanceKlass jdk/internal/access/JavaNetInetAddressAccess
instanceKlass  @bci org/apache/logging/log4j/core/util/NetUtils getLocalHostname ()Ljava/lang/String; 0 <appendix> argL0 ; # org/apache/logging/log4j/core/util/NetUtils$$Lambda+0x0000029e900f7db0
instanceKlass org/apache/logging/log4j/core/util/NetUtils
instanceKlass  @bci org/apache/logging/log4j/core/LoggerContext setConfiguration (Lorg/apache/logging/log4j/core/config/Configuration;)Lorg/apache/logging/log4j/core/config/Configuration; 62 <appendix> argL0 ; # org/apache/logging/log4j/core/LoggerContext$$Lambda+0x0000029e900f7978
instanceKlass org/apache/logging/log4j/core/config/status/StatusConfiguration
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e900f8800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e900f8400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e900f8000
instanceKlass org/apache/logging/log4j/core/config/builder/impl/DefaultComponentBuilder
instanceKlass org/apache/logging/log4j/core/config/builder/api/Component
instanceKlass org/apache/logging/log4j/core/config/builder/api/ScriptComponentBuilder
instanceKlass org/apache/logging/log4j/core/config/builder/api/LayoutComponentBuilder
instanceKlass org/apache/logging/log4j/core/config/builder/api/PropertyComponentBuilder
instanceKlass org/apache/logging/log4j/core/config/builder/api/ScriptFileComponentBuilder
instanceKlass org/apache/logging/log4j/core/config/builder/api/AppenderRefComponentBuilder
instanceKlass org/apache/logging/log4j/core/config/builder/api/KeyValuePairComponentBuilder
instanceKlass org/apache/logging/log4j/core/config/builder/api/AppenderComponentBuilder
instanceKlass org/apache/logging/log4j/core/config/builder/api/CustomLevelComponentBuilder
instanceKlass org/apache/logging/log4j/core/config/builder/api/RootLoggerComponentBuilder
instanceKlass javax/xml/transform/Source
instanceKlass javax/xml/transform/Result
instanceKlass org/apache/logging/log4j/core/config/builder/api/LoggerComponentBuilder
instanceKlass org/apache/logging/log4j/core/config/builder/api/FilterComponentBuilder
instanceKlass org/apache/logging/log4j/core/config/builder/impl/DefaultConfigurationBuilder
instanceKlass org/apache/logging/log4j/core/config/Reconfigurable
instanceKlass org/apache/logging/log4j/core/config/builder/api/LoggableComponentBuilder
instanceKlass org/apache/logging/log4j/core/config/builder/api/FilterableComponentBuilder
instanceKlass org/apache/logging/log4j/core/config/builder/api/ComponentBuilder
instanceKlass org/apache/logging/log4j/core/util/Source
instanceKlass org/apache/logging/log4j/core/net/UrlConnectionFactory
instanceKlass org/apache/logging/log4j/core/util/FileUtils
instanceKlass  @bci org/apache/logging/log4j/core/util/BasicAuthorizationProvider <init> (Lorg/apache/logging/log4j/util/PropertiesUtil;)V 48 <appendix> member <vmtarget> ; # org/apache/logging/log4j/core/util/BasicAuthorizationProvider$$Lambda+0x0000029e900f1e78
instanceKlass  @bci org/apache/logging/log4j/core/util/BasicAuthorizationProvider <init> (Lorg/apache/logging/log4j/util/PropertiesUtil;)V 32 <appendix> member <vmtarget> ; # org/apache/logging/log4j/core/util/BasicAuthorizationProvider$$Lambda+0x0000029e900f1c40
instanceKlass  @bci org/apache/logging/log4j/core/util/BasicAuthorizationProvider <init> (Lorg/apache/logging/log4j/util/PropertiesUtil;)V 16 <appendix> member <vmtarget> ; # org/apache/logging/log4j/core/util/BasicAuthorizationProvider$$Lambda+0x0000029e900f1a08
instanceKlass org/apache/logging/log4j/core/util/BasicAuthorizationProvider
instanceKlass com/fasterxml/jackson/core/FormatFeature
instanceKlass com/fasterxml/jackson/core/io/CharTypes
instanceKlass com/fasterxml/jackson/core/io/JsonStringEncoder
instanceKlass com/fasterxml/jackson/core/io/SerializedString
instanceKlass com/fasterxml/jackson/dataformat/yaml/util/StringQuotingChecker
instanceKlass com/fasterxml/jackson/core/async/ByteBufferFeeder
instanceKlass com/fasterxml/jackson/core/async/ByteArrayFeeder
instanceKlass com/fasterxml/jackson/core/async/NonBlockingInputFeeder
instanceKlass com/fasterxml/jackson/core/TSFBuilder
instanceKlass com/fasterxml/jackson/core/SerializableString
instanceKlass com/fasterxml/jackson/core/util/JacksonFeatureSet
instanceKlass com/fasterxml/jackson/core/util/JacksonFeature
instanceKlass com/fasterxml/jackson/databind/cfg/DefaultCacheProvider
instanceKlass com/fasterxml/jackson/databind/cfg/CacheProvider
instanceKlass com/fasterxml/jackson/databind/introspect/AccessorNamingStrategy
instanceKlass com/fasterxml/jackson/core/Base64Variant
instanceKlass com/fasterxml/jackson/core/Base64Variants
instanceKlass java/text/DigitList
instanceKlass java/text/FieldPosition
instanceKlass java/lang/StringUTF16$CharsSpliterator
instanceKlass java/util/stream/Sink$ChainedInt
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfInt <clinit> ()V 43 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfInt$$Lambda+0x800000049
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfInt <clinit> ()V 38 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfInt$$Lambda+0x80000004b
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfInt <clinit> ()V 16 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfInt$$Lambda+0x80000004a
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfInt <clinit> ()V 11 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfInt$$Lambda+0x80000004c
instanceKlass java/util/OptionalInt
instanceKlass  @bci java/text/DecimalFormatSymbols findNonFormatChar (Ljava/lang/String;C)C 4 <appendix> argL0 ; # java/text/DecimalFormatSymbols$$Lambda+0x80000000d
instanceKlass java/util/function/IntPredicate
instanceKlass java/lang/StringLatin1$CharsSpliterator
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getDecimalFormatSymbolsProvider ()Ljava/text/spi/DecimalFormatSymbolsProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x80000006b
instanceKlass java/text/DecimalFormatSymbols
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getNumberFormatProvider ()Ljava/text/spi/NumberFormatProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x80000006c
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getDateFormatSymbolsProvider ()Ljava/text/spi/DateFormatSymbolsProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x80000006a
instanceKlass java/text/DateFormatSymbols
instanceKlass java/text/AttributedCharacterIterator$Attribute
instanceKlass com/fasterxml/jackson/databind/type/TypeBindings
instanceKlass com/fasterxml/jackson/databind/type/TypeParser
instanceKlass com/fasterxml/jackson/databind/type/TypeFactory
instanceKlass com/fasterxml/jackson/databind/cfg/BaseSettings
instanceKlass java/util/concurrent/atomic/AtomicReferenceArray
instanceKlass java/util/concurrent/atomic/AtomicLongArray
instanceKlass com/fasterxml/jackson/databind/util/internal/Linked
instanceKlass com/fasterxml/jackson/databind/util/internal/PrivateMaxEntriesMap$Builder
instanceKlass com/fasterxml/jackson/databind/util/LRUMap
instanceKlass com/fasterxml/jackson/databind/util/LookupCache
instanceKlass java/beans/ConstructorProperties
instanceKlass java/beans/Transient
instanceKlass com/fasterxml/jackson/databind/util/ClassUtil$Ctor
instanceKlass com/fasterxml/jackson/databind/util/ClassUtil
instanceKlass com/fasterxml/jackson/databind/ext/Java7Support
instanceKlass com/fasterxml/jackson/annotation/JsonMerge
instanceKlass com/fasterxml/jackson/databind/annotation/JsonDeserialize
instanceKlass com/fasterxml/jackson/annotation/JsonManagedReference
instanceKlass com/fasterxml/jackson/annotation/JsonBackReference
instanceKlass com/fasterxml/jackson/annotation/JsonUnwrapped
instanceKlass com/fasterxml/jackson/annotation/JsonRawValue
instanceKlass com/fasterxml/jackson/annotation/JsonTypeInfo
instanceKlass com/fasterxml/jackson/annotation/JsonFormat
instanceKlass com/fasterxml/jackson/annotation/JsonView
instanceKlass com/fasterxml/jackson/databind/annotation/JsonSerialize
instanceKlass com/fasterxml/jackson/databind/introspect/ConcreteBeanPropertyBase
instanceKlass com/fasterxml/jackson/databind/BeanProperty
instanceKlass com/fasterxml/jackson/databind/introspect/BeanPropertyDefinition
instanceKlass com/fasterxml/jackson/databind/util/Named
instanceKlass com/fasterxml/jackson/databind/introspect/TypeResolutionContext
instanceKlass com/fasterxml/jackson/databind/introspect/Annotated
instanceKlass com/fasterxml/jackson/databind/jsontype/TypeResolverBuilder
instanceKlass com/fasterxml/jackson/databind/introspect/VisibilityChecker
instanceKlass com/fasterxml/jackson/databind/introspect/ClassIntrospector
instanceKlass com/fasterxml/jackson/core/JsonParser
instanceKlass com/fasterxml/jackson/databind/JsonSerializable$Base
instanceKlass com/fasterxml/jackson/core/JsonGenerator
instanceKlass com/fasterxml/jackson/databind/JsonSerializable
instanceKlass com/fasterxml/jackson/core/type/ResolvedType
instanceKlass com/fasterxml/jackson/core/TreeNode
instanceKlass com/fasterxml/jackson/core/util/BufferRecycler$Gettable
instanceKlass com/fasterxml/jackson/databind/Module$SetupContext
instanceKlass com/fasterxml/jackson/databind/jsontype/PolymorphicTypeValidator
instanceKlass com/fasterxml/jackson/databind/introspect/AccessorNamingStrategy$Provider
instanceKlass com/fasterxml/jackson/databind/AnnotationIntrospector
instanceKlass com/fasterxml/jackson/databind/ser/SerializerFactory
instanceKlass com/fasterxml/jackson/databind/deser/DeserializerFactory
instanceKlass com/fasterxml/jackson/databind/DatabindContext
instanceKlass com/fasterxml/jackson/databind/jsontype/SubtypeResolver
instanceKlass com/fasterxml/jackson/core/TokenStreamFactory
instanceKlass com/fasterxml/jackson/core/TreeCodec
instanceKlass com/fasterxml/jackson/core/Versioned
instanceKlass org/apache/logging/log4j/core/config/Order
instanceKlass org/apache/logging/log4j/core/config/OrderComparator
instanceKlass org/apache/logging/log4j/core/util/AuthorizationProvider
instanceKlass org/apache/logging/log4j/core/config/builder/api/ConfigurationBuilder
instanceKlass  @bci org/apache/logging/log4j/core/selector/ClassLoaderContextSelector locateContext (Ljava/lang/ClassLoader;Ljava/util/Map$Entry;Ljava/net/URI;)Lorg/apache/logging/log4j/core/LoggerContext; 160 <appendix> member <vmtarget> ; # org/apache/logging/log4j/core/selector/ClassLoaderContextSelector$$Lambda+0x0000029e900c92d0
instanceKlass org/apache/logging/log4j/core/appender/DefaultErrorHandler
instanceKlass org/apache/logging/log4j/core/appender/ConsoleAppender$FactoryData
instanceKlass org/apache/logging/log4j/core/appender/ConsoleAppender$ConsoleManagerFactory
instanceKlass org/apache/logging/log4j/core/layout/ByteBufferDestination
instanceKlass org/apache/logging/log4j/core/ErrorHandler
instanceKlass org/apache/logging/log4j/core/layout/PatternLayout$PatternFormatterPatternSerializer
instanceKlass org/apache/logging/log4j/core/pattern/PlainTextRenderer
instanceKlass org/apache/logging/log4j/core/impl/ThrowableFormatOptions
instanceKlass org/apache/logging/log4j/core/pattern/PatternFormatter
instanceKlass  @bci jdk/internal/reflect/DirectConstructorHandleAccessor invokeImpl ([Ljava/lang/Object;)Ljava/lang/Object; 88 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000029e900c1c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e900c1800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e900c1400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e900c1000
instanceKlass org/apache/logging/log4j/core/pattern/TextRenderer
instanceKlass org/apache/logging/log4j/core/util/Integers
instanceKlass org/apache/logging/log4j/core/pattern/NameAbbreviator
instanceKlass org/apache/logging/log4j/core/util/OptionConverter
instanceKlass sun/util/calendar/CalendarUtils
instanceKlass sun/util/calendar/CalendarDate
instanceKlass sun/util/resources/Bundles$CacheKeyReference
instanceKlass  @bci java/util/ResourceBundle$ResourceBundleProviderHelper newResourceBundle (Ljava/lang/Class;)Ljava/util/ResourceBundle; 22 <appendix> member <vmtarget> ; # java/util/ResourceBundle$ResourceBundleProviderHelper$$Lambda+0x800000012
instanceKlass java/util/ResourceBundle$ResourceBundleProviderHelper
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter applyAliases (Ljava/util/Locale;)Ljava/util/Locale; 4 <appendix> argL0 ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x800000064
instanceKlass sun/util/resources/Bundles$CacheKey
instanceKlass java/util/ResourceBundle$1
instanceKlass jdk/internal/access/JavaUtilResourceBundleAccess
instanceKlass sun/util/resources/Bundles
instanceKlass sun/util/resources/LocaleData$LocaleDataStrategy
instanceKlass sun/util/resources/Bundles$Strategy
instanceKlass sun/util/resources/LocaleData$1
instanceKlass sun/util/resources/LocaleData
instanceKlass sun/util/locale/provider/LocaleResources
instanceKlass  @bci sun/util/locale/provider/LocaleProviderAdapter toLocaleArray (Ljava/util/Set;)[Ljava/util/Locale; 16 <appendix> argL0 ; # sun/util/locale/provider/LocaleProviderAdapter$$Lambda+0x80000006e
instanceKlass  @bci sun/util/locale/provider/LocaleProviderAdapter toLocaleArray (Ljava/util/Set;)[Ljava/util/Locale; 6 <appendix> argL0 ; # sun/util/locale/provider/LocaleProviderAdapter$$Lambda+0x80000006d
instanceKlass java/util/Spliterators$IteratorSpliterator
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter getCalendarDataProvider ()Ljava/util/spi/CalendarDataProvider; 8 <appendix> member <vmtarget> ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x800000067
instanceKlass java/util/ResourceBundle
instanceKlass java/util/ResourceBundle$Control
instanceKlass sun/util/locale/provider/CalendarDataUtility$CalendarWeekParameterGetter
instanceKlass sun/util/locale/provider/LocaleServiceProviderPool$LocalizedObjectGetter
instanceKlass sun/util/locale/provider/LocaleServiceProviderPool
instanceKlass java/util/Locale$Builder
instanceKlass sun/util/locale/provider/CalendarDataUtility
instanceKlass sun/util/calendar/CalendarSystem$GregorianHolder
instanceKlass sun/util/calendar/CalendarSystem
instanceKlass java/util/Calendar$Builder
instanceKlass sun/util/locale/provider/AvailableLanguageTags
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getCalendarProvider ()Lsun/util/spi/CalendarProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x800000068
instanceKlass sun/util/resources/cldr/provider/CLDRLocaleDataMetaInfo
instanceKlass jdk/internal/module/ModulePatcher$PatchedModuleReader
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter <init> ()V 4 <appendix> argL0 ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x800000066
instanceKlass sun/util/locale/LocaleObjectCache
instanceKlass sun/util/locale/BaseLocale$Key
instanceKlass sun/util/locale/InternalLocaleBuilder$CaseInsensitiveChar
instanceKlass sun/util/locale/InternalLocaleBuilder
instanceKlass sun/util/locale/StringTokenIterator
instanceKlass sun/util/locale/ParseStatus
instanceKlass sun/util/locale/LanguageTag
instanceKlass sun/util/cldr/CLDRBaseLocaleDataMetaInfo
instanceKlass sun/util/locale/provider/LocaleDataMetaInfo
instanceKlass sun/util/locale/provider/ResourceBundleBasedAdapter
instanceKlass sun/util/locale/provider/LocaleProviderAdapter
instanceKlass java/util/spi/LocaleServiceProvider
instanceKlass java/util/Calendar
instanceKlass sun/util/calendar/ZoneInfoFile$ZoneOffsetTransitionRule
instanceKlass sun/util/calendar/ZoneInfoFile$1
instanceKlass sun/util/calendar/ZoneInfoFile
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e900c0800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e900c0400
instanceKlass org/apache/logging/log4j/core/util/datetime/FixedDateFormat
instanceKlass org/apache/logging/log4j/core/pattern/DatePatternConverter$CachedTime
instanceKlass org/apache/logging/log4j/core/time/MutableInstant
instanceKlass java/util/TimeZone
instanceKlass java/util/Date
instanceKlass org/apache/logging/log4j/core/pattern/DatePatternConverter$Formatter
instanceKlass org/apache/logging/log4j/core/time/Instant
instanceKlass org/apache/logging/log4j/core/pattern/PatternParser$1
instanceKlass org/apache/logging/log4j/core/pattern/FormattingInfo
instanceKlass java/lang/Class$AnnotationData
instanceKlass java/lang/annotation/Target
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e900c0000
instanceKlass java/lang/reflect/Proxy$ProxyBuilder$1
instanceKlass  @bci java/lang/reflect/ProxyGenerator addProxyMethod (Ljava/lang/reflect/Method;Ljava/lang/Class;)V 23 <appendix> argL0 ; # java/lang/reflect/ProxyGenerator$$Lambda+0x0000029e900764e8
instanceKlass  @bci java/lang/reflect/ProxyGenerator addProxyMethod (Ljava/lang/reflect/ProxyGenerator$ProxyMethod;)V 10 <appendix> argL0 ; # java/lang/reflect/ProxyGenerator$$Lambda+0x0000029e900762b0
instanceKlass java/util/StringJoiner
instanceKlass java/lang/reflect/ProxyGenerator$ProxyMethod
instanceKlass  @bci java/lang/reflect/Proxy getLoader (Ljava/lang/Module;)Ljava/lang/ClassLoader; 6 <appendix> member <vmtarget> ; # java/lang/reflect/Proxy$$Lambda+0x0000029e90075b70
instanceKlass  @bci java/lang/WeakPairMap computeIfAbsent (Ljava/lang/Object;Ljava/lang/Object;Ljava/util/function/BiFunction;)Ljava/lang/Object; 18 <appendix> member <vmtarget> ; # java/lang/WeakPairMap$$Lambda+0x0000029e90075728
instanceKlass  @bci java/lang/Module implAddExportsOrOpens (Ljava/lang/String;Ljava/lang/Module;ZZ)V 145 <appendix> argL0 ; # java/lang/Module$$Lambda+0x0000029e90075500
instanceKlass  @bci java/lang/module/ModuleDescriptor$Builder packages (Ljava/util/Set;)Ljava/lang/module/ModuleDescriptor$Builder; 17 <appendix> argL0 ; # java/lang/module/ModuleDescriptor$Builder$$Lambda+0x800000002
instanceKlass java/lang/WeakPairMap$Pair$Lookup
instanceKlass java/lang/WeakPairMap$Pair
instanceKlass java/lang/WeakPairMap
instanceKlass java/lang/Module$ReflectionData
instanceKlass jdk/internal/module/Checks
instanceKlass java/lang/module/ModuleDescriptor$Builder
instanceKlass  @bci java/lang/reflect/Proxy$ProxyBuilder getDynamicModule (Ljava/lang/ClassLoader;)Ljava/lang/Module; 4 <appendix> argL0 ; # java/lang/reflect/Proxy$ProxyBuilder$$Lambda+0x0000029e90074bc8
instanceKlass java/lang/PublicMethods
instanceKlass java/lang/reflect/Proxy$ProxyBuilder
instanceKlass  @bci java/lang/reflect/Proxy getProxyConstructor (Ljava/lang/Class;Ljava/lang/ClassLoader;[Ljava/lang/Class;)Ljava/lang/reflect/Constructor; 35 <appendix> argL0 ; # java/lang/reflect/Proxy$$Lambda+0x0000029e90074590
instanceKlass java/lang/reflect/Proxy
instanceKlass sun/reflect/annotation/AnnotationInvocationHandler
instanceKlass java/lang/reflect/InvocationHandler
instanceKlass sun/reflect/annotation/AnnotationParser$1
instanceKlass java/lang/annotation/Documented
instanceKlass java/lang/annotation/Inherited
instanceKlass java/lang/annotation/Retention
instanceKlass sun/reflect/annotation/ExceptionProxy
instanceKlass sun/reflect/annotation/AnnotationType$1
instanceKlass sun/reflect/annotation/AnnotationType
instanceKlass java/lang/reflect/GenericArrayType
instanceKlass org/apache/logging/log4j/core/config/plugins/Plugin
instanceKlass sun/reflect/generics/visitor/Reifier
instanceKlass sun/reflect/generics/visitor/TypeTreeVisitor
instanceKlass sun/reflect/generics/factory/CoreReflectionFactory
instanceKlass sun/reflect/generics/factory/GenericsFactory
instanceKlass sun/reflect/generics/scope/AbstractScope
instanceKlass sun/reflect/generics/scope/Scope
instanceKlass sun/reflect/generics/tree/ClassTypeSignature
instanceKlass sun/reflect/generics/tree/SimpleClassTypeSignature
instanceKlass sun/reflect/generics/tree/FieldTypeSignature
instanceKlass sun/reflect/generics/tree/BaseType
instanceKlass sun/reflect/generics/tree/TypeSignature
instanceKlass sun/reflect/generics/tree/ReturnType
instanceKlass sun/reflect/generics/tree/TypeArgument
instanceKlass sun/reflect/generics/tree/TypeTree
instanceKlass sun/reflect/generics/tree/Tree
instanceKlass sun/reflect/generics/parser/SignatureParser
instanceKlass org/apache/logging/log4j/core/pattern/ConverterKeys
instanceKlass org/apache/logging/log4j/core/pattern/PatternParser
instanceKlass org/apache/logging/log4j/core/layout/StringBuilderEncoder
instanceKlass org/apache/logging/log4j/core/layout/PatternLayout$PatternSerializer
instanceKlass org/apache/logging/log4j/core/layout/AbstractStringLayout$Serializer
instanceKlass org/apache/logging/log4j/core/layout/PatternLayout$SerializerBuilder
instanceKlass org/apache/logging/log4j/core/layout/PatternLayout$Builder
instanceKlass org/apache/logging/log4j/core/layout/AbstractStringLayout$Serializer2
instanceKlass org/apache/logging/log4j/core/util/internal/InternalLoggerRegistry
instanceKlass org/apache/logging/log4j/core/config/Node
instanceKlass org/apache/logging/log4j/core/util/DummyNanoClock
instanceKlass org/apache/logging/log4j/core/util/WatchEventService
instanceKlass java/util/UUID
instanceKlass org/apache/logging/log4j/core/util/WatchManager$LocalUUID
instanceKlass java/util/concurrent/Future
instanceKlass java/util/concurrent/ScheduledExecutorService
instanceKlass java/util/concurrent/ExecutorService
instanceKlass java/util/concurrent/Executor
instanceKlass org/apache/logging/log4j/core/config/DefaultReliabilityStrategy
instanceKlass org/apache/logging/log4j/core/config/LocationAwareReliabilityStrategy
instanceKlass sun/reflect/misc/ReflectUtil
instanceKlass java/util/concurrent/atomic/AtomicReferenceFieldUpdater$AtomicReferenceFieldUpdaterImpl$1
instanceKlass java/util/concurrent/atomic/AtomicReferenceFieldUpdater
instanceKlass org/apache/logging/log4j/core/config/AppenderControlArraySet
instanceKlass org/apache/logging/log4j/core/util/SystemClock
instanceKlass org/apache/logging/log4j/core/time/PreciseClock
instanceKlass org/apache/logging/log4j/core/util/Clock
instanceKlass org/apache/logging/log4j/core/util/ClockFactory
instanceKlass org/apache/logging/log4j/core/impl/ReusableLogEventFactory
instanceKlass  @bci org/apache/logging/log4j/core/config/LoggerConfig <clinit> ()V 9 <appendix> argL0 ; # org/apache/logging/log4j/core/config/LoggerConfig$$Lambda+0x0000029e900b8e18
instanceKlass org/apache/logging/log4j/core/LogEvent
instanceKlass org/apache/logging/log4j/core/impl/LogEventFactory
instanceKlass org/apache/logging/log4j/core/impl/LocationAwareLogEventFactory
instanceKlass org/apache/logging/log4j/core/config/ReliabilityStrategy
instanceKlass java/util/DualPivotQuicksort
instanceKlass org/apache/logging/log4j/core/lookup/StrMatcher
instanceKlass sun/management/Util
instanceKlass  @bci java/util/stream/Collectors toList ()Ljava/util/stream/Collector; 14 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x0000029e9006db10
instanceKlass  @bci java/util/stream/Collectors toList ()Ljava/util/stream/Collector; 9 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x0000029e9006d448
instanceKlass  @bci java/util/stream/Collectors toList ()Ljava/util/stream/Collector; 4 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x0000029e9006d230
instanceKlass  @bci sun/management/spi/PlatformMBeanProvider$PlatformComponent getMBeans (Ljava/lang/Class;)Ljava/util/List; 63 <appendix> member <vmtarget> ; # sun/management/spi/PlatformMBeanProvider$PlatformComponent$$Lambda+0x0000029e9006cff0
instanceKlass  @bci sun/management/spi/PlatformMBeanProvider$PlatformComponent getMBeans (Ljava/lang/Class;)Ljava/util/List; 47 <appendix> member <vmtarget> ; # sun/management/spi/PlatformMBeanProvider$PlatformComponent$$Lambda+0x0000029e9006cda0
instanceKlass java/util/Collections$2
instanceKlass sun/management/RuntimeImpl
instanceKlass  @bci java/lang/management/ManagementFactory$PlatformMBeanFinder findSingleton (Ljava/lang/Class;)Lsun/management/spi/PlatformMBeanProvider$PlatformComponent; 30 <appendix> member <vmtarget> ; # java/lang/management/ManagementFactory$PlatformMBeanFinder$$Lambda+0x0000029e9006c570
instanceKlass  @bci java/lang/management/ManagementFactory$PlatformMBeanFinder findSingleton (Ljava/lang/Class;)Lsun/management/spi/PlatformMBeanProvider$PlatformComponent; 19 <appendix> member <vmtarget> ; # java/lang/management/ManagementFactory$PlatformMBeanFinder$$Lambda+0x0000029e9006c320
instanceKlass java/util/Collections$UnmodifiableCollection$1
instanceKlass jdk/management/jfr/internal/FlightRecorderMXBeanProvider$SingleMBeanComponent
instanceKlass jdk/management/jfr/FlightRecorderMXBean
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$11
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$10
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$9
instanceKlass java/util/logging/LogManager
instanceKlass sun/management/ManagementFactoryHelper$LoggingMXBeanAccess$1
instanceKlass sun/management/ManagementFactoryHelper$LoggingMXBeanAccess
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$8
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$7
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$6
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$5
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$4
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$3
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$2
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$1
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$5
instanceKlass sun/management/VMManagementImpl
instanceKlass sun/management/VMManagement
instanceKlass sun/management/ManagementFactoryHelper
instanceKlass sun/management/NotificationEmitterSupport
instanceKlass javax/management/NotificationEmitter
instanceKlass javax/management/NotificationBroadcaster
instanceKlass com/sun/management/DiagnosticCommandMBean
instanceKlass javax/management/DynamicMBean
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$4
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$3
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$2
instanceKlass  @bci java/util/stream/Collectors toSet ()Ljava/util/stream/Collector; 14 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000041
instanceKlass  @bci java/util/stream/Collectors toSet ()Ljava/util/stream/Collector; 9 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x80000003a
instanceKlass  @bci java/util/stream/Collectors toSet ()Ljava/util/stream/Collector; 4 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000046
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$1
instanceKlass sun/management/spi/PlatformMBeanProvider$PlatformComponent
instanceKlass  @bci com/sun/management/internal/PlatformMBeanProviderImpl <clinit> ()V 8 <appendix> argL0 ; # com/sun/management/internal/PlatformMBeanProviderImpl$$Lambda+0x0000029e90066f18
instanceKlass java/util/concurrent/Callable
instanceKlass sun/management/spi/PlatformMBeanProvider
instanceKlass java/lang/management/ManagementFactory$PlatformMBeanFinder$1
instanceKlass java/lang/management/ManagementFactory$PlatformMBeanFinder
instanceKlass java/lang/management/RuntimeMXBean
instanceKlass java/lang/management/PlatformManagedObject
instanceKlass  @bci java/lang/management/ManagementFactory loadNativeLib ()V 0 <appendix> argL0 ; # java/lang/management/ManagementFactory$$Lambda+0x0000029e900658f0
instanceKlass java/lang/management/ManagementFactory
instanceKlass org/apache/logging/log4j/core/net/JndiManager$JndiManagerFactory
instanceKlass javax/naming/Context
instanceKlass org/apache/logging/log4j/core/appender/ManagerFactory
instanceKlass org/apache/logging/log4j/core/appender/AbstractManager
instanceKlass java/text/Format
instanceKlass org/apache/logging/log4j/core/impl/ThreadContextDataProvider
instanceKlass  @bci org/apache/logging/log4j/core/impl/ThreadContextDataInjector getServiceProviders ()Ljava/util/List; 32 <appendix> member <vmtarget> ; # org/apache/logging/log4j/core/impl/ThreadContextDataInjector$$Lambda+0x0000029e900b7950
instanceKlass org/apache/logging/log4j/core/util/ContextDataProvider
instanceKlass java/util/concurrent/ConcurrentLinkedDeque$Node
instanceKlass org/apache/logging/log4j/core/impl/ThreadContextDataInjector
instanceKlass org/apache/logging/log4j/util/StringMap
instanceKlass org/apache/logging/log4j/core/impl/ThreadContextDataInjector$AbstractContextDataInjector
instanceKlass org/apache/logging/log4j/spi/ReadOnlyThreadContextMap
instanceKlass org/apache/logging/log4j/spi/DefaultThreadContextMap
instanceKlass org/apache/logging/log4j/util/ReadOnlyStringMap
instanceKlass org/apache/logging/log4j/spi/ThreadContextMapFactory
instanceKlass org/apache/logging/log4j/spi/DefaultThreadContextStack
instanceKlass java/util/Arrays$ArrayItr
instanceKlass org/apache/logging/log4j/spi/ThreadContextStack
instanceKlass org/apache/logging/log4j/ThreadContext$ContextStack
instanceKlass org/apache/logging/log4j/ThreadContext
instanceKlass  @bci org/apache/logging/log4j/core/impl/ContextDataInjectorFactory createInjector ()Lorg/apache/logging/log4j/core/ContextDataInjector; 4 <appendix> argL0 ; # org/apache/logging/log4j/core/impl/ContextDataInjectorFactory$$Lambda+0x0000029e900b2fa8
instanceKlass org/apache/logging/log4j/core/ContextDataInjector
instanceKlass org/apache/logging/log4j/core/impl/ContextDataInjectorFactory
instanceKlass org/apache/logging/log4j/core/util/ReflectionUtil
instanceKlass java/util/concurrent/CopyOnWriteArrayList$COWIterator
instanceKlass  @bci org/apache/logging/log4j/core/config/plugins/util/PluginRegistry decodeCacheFiles (Ljava/lang/ClassLoader;)Ljava/util/Map; 325 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000029e900b4c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e900b4800
instanceKlass  @bci org/apache/logging/log4j/core/config/plugins/util/PluginRegistry decodeCacheFiles (Ljava/lang/ClassLoader;)Ljava/util/Map; 325 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000029e900b4400
instanceKlass  @bci org/apache/logging/log4j/core/config/plugins/util/PluginRegistry decodeCacheFiles (Ljava/lang/ClassLoader;)Ljava/util/Map; 325 <appendix> member <vmtarget> ; # org/apache/logging/log4j/core/config/plugins/util/PluginRegistry$$Lambda+0x0000029e900b2778
instanceKlass  @cpi org/apache/logging/log4j/core/config/plugins/util/PluginRegistry 467 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000029e900b4000
instanceKlass org/apache/logging/log4j/util/Supplier
instanceKlass org/apache/logging/log4j/core/util/AbstractWatcher
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$UuidConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$UrlConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$UriConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$StringConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$ShortConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$SecurityProviderConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$PatternConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$PathConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$LongConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$LevelConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$IntegerConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$InetAddressConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$FloatConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$FileConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$DurationConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$DoubleConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$CronExpressionConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$ClassConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$CharsetConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$CharArrayConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$CharacterConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$ByteArrayConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$ByteConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$BooleanConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$BigIntegerConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverters$BigDecimalConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/convert/TypeConverter
instanceKlass org/apache/logging/log4j/core/lookup/UpperLookup
instanceKlass org/apache/logging/log4j/core/lookup/MapLookup
instanceKlass org/apache/logging/log4j/core/lookup/DateLookup
instanceKlass org/apache/logging/log4j/core/lookup/ContextMapLookup
instanceKlass org/apache/logging/log4j/core/pattern/FileDatePatternConverter
instanceKlass org/apache/logging/log4j/core/config/arbiters/SystemPropertyArbiter
instanceKlass org/apache/logging/log4j/core/net/ssl/SslConfiguration
instanceKlass org/apache/logging/log4j/core/appender/rolling/action/PathSortByModificationTime
instanceKlass org/apache/logging/log4j/core/appender/rolling/action/PathSorter
instanceKlass org/apache/logging/log4j/core/net/SocketPerformancePreferences
instanceKlass org/apache/logging/log4j/core/net/SocketOptions
instanceKlass org/apache/logging/log4j/core/util/Builder
instanceKlass org/apache/logging/log4j/core/net/SocketAddress
instanceKlass org/apache/logging/log4j/core/config/arbiters/SelectArbiter
instanceKlass org/apache/logging/log4j/core/config/ScriptsPlugin
instanceKlass org/apache/logging/log4j/core/layout/ScriptPatternSelector
instanceKlass org/apache/logging/log4j/core/appender/rolling/action/ScriptCondition
instanceKlass org/apache/logging/log4j/core/config/arbiters/ScriptArbiter
instanceKlass org/apache/logging/log4j/core/script/AbstractScript
instanceKlass org/apache/logging/log4j/core/appender/routing/Routes
instanceKlass org/apache/logging/log4j/core/appender/routing/Route
instanceKlass org/apache/logging/log4j/core/pattern/RegexReplacement
instanceKlass org/apache/logging/log4j/core/appender/rewrite/PropertiesRewritePolicy
instanceKlass org/apache/logging/log4j/core/config/PropertiesPlugin
instanceKlass org/apache/logging/log4j/core/layout/PatternMatch
instanceKlass org/apache/logging/log4j/core/net/MulticastDnsAdvertiser
instanceKlass org/apache/logging/log4j/core/layout/MarkerPatternSelector
instanceKlass org/apache/logging/log4j/core/appender/rewrite/MapRewritePolicy
instanceKlass org/apache/logging/log4j/core/config/LoggersPlugin
instanceKlass org/apache/logging/log4j/core/appender/rewrite/LoggerNameLevelRewritePolicy
instanceKlass org/apache/logging/log4j/core/appender/rewrite/RewritePolicy
instanceKlass org/apache/logging/log4j/core/layout/LoggerFields
instanceKlass org/apache/logging/log4j/core/async/LinkedTransferQueueFactory
instanceKlass org/apache/logging/log4j/core/layout/LevelPatternSelector
instanceKlass org/apache/logging/log4j/core/layout/PatternSelector
instanceKlass org/apache/logging/log4j/core/util/KeyValuePair
instanceKlass org/apache/logging/log4j/core/net/ssl/StoreConfiguration
instanceKlass org/apache/logging/log4j/core/async/JCToolsBlockingQueueFactory
instanceKlass org/apache/logging/log4j/core/appender/rolling/action/IfNot
instanceKlass org/apache/logging/log4j/core/appender/rolling/action/IfLastModified
instanceKlass org/apache/logging/log4j/core/appender/rolling/action/IfFileName
instanceKlass org/apache/logging/log4j/core/appender/rolling/action/IfAny
instanceKlass org/apache/logging/log4j/core/appender/rolling/action/IfAll
instanceKlass org/apache/logging/log4j/core/appender/rolling/action/IfAccumulatedFileSize
instanceKlass org/apache/logging/log4j/core/appender/rolling/action/IfAccumulatedFileCount
instanceKlass org/apache/logging/log4j/core/appender/rolling/action/PathCondition
instanceKlass org/apache/logging/log4j/core/appender/routing/PurgePolicy
instanceKlass org/apache/logging/log4j/core/appender/FailoversPlugin
instanceKlass org/apache/logging/log4j/core/config/arbiters/EnvironmentArbiter
instanceKlass org/apache/logging/log4j/core/async/DisruptorBlockingQueueFactory
instanceKlass org/apache/logging/log4j/core/appender/rolling/DirectFileRolloverStrategy
instanceKlass org/apache/logging/log4j/core/appender/rolling/action/AbstractAction
instanceKlass org/apache/logging/log4j/core/appender/rolling/action/Action
instanceKlass org/apache/logging/log4j/core/appender/rolling/AbstractRolloverStrategy
instanceKlass org/apache/logging/log4j/core/appender/rolling/RolloverStrategy
instanceKlass org/apache/logging/log4j/core/config/arbiters/DefaultArbiter
instanceKlass org/apache/logging/log4j/core/config/CustomLevels
instanceKlass org/apache/logging/log4j/core/config/CustomLevelConfig
instanceKlass org/apache/logging/log4j/core/layout/AbstractLayout
instanceKlass org/apache/logging/log4j/core/StringLayout
instanceKlass org/apache/logging/log4j/core/appender/rolling/TriggeringPolicy
instanceKlass org/apache/logging/log4j/core/appender/db/jdbc/ConnectionSource
instanceKlass org/apache/logging/log4j/core/appender/db/ColumnMapping
instanceKlass org/apache/logging/log4j/core/appender/db/jdbc/ColumnConfig
instanceKlass org/apache/logging/log4j/core/config/arbiters/ClassArbiter
instanceKlass org/apache/logging/log4j/core/config/arbiters/Arbiter
instanceKlass org/apache/logging/log4j/core/async/AsyncWaitStrategyFactoryConfig
instanceKlass org/apache/logging/log4j/core/async/ArrayBlockingQueueFactory
instanceKlass org/apache/logging/log4j/core/async/BlockingQueueFactory
instanceKlass org/apache/logging/log4j/core/appender/AppenderSet
instanceKlass org/apache/logging/log4j/core/config/AppendersPlugin
instanceKlass org/apache/logging/log4j/core/config/AppenderRef
instanceKlass org/apache/logging/log4j/core/pattern/AnsiConverter
instanceKlass org/apache/logging/log4j/core/pattern/ArrayPatternConverter
instanceKlass org/apache/logging/log4j/core/impl/LocationAware
instanceKlass org/apache/logging/log4j/core/pattern/AbstractPatternConverter
instanceKlass org/apache/logging/log4j/core/pattern/PatternConverter
instanceKlass org/apache/logging/log4j/core/config/plugins/util/PluginType
instanceKlass org/apache/logging/log4j/core/config/builder/api/ConfigurationBuilderFactory
instanceKlass  @bci org/apache/logging/log4j/core/config/plugins/processor/PluginCache loadCacheFiles (Ljava/util/Enumeration;)V 137 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000029e90088800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90088400
instanceKlass  @bci org/apache/logging/log4j/core/config/plugins/processor/PluginCache loadCacheFiles (Ljava/util/Enumeration;)V 137 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000029e90088000
instanceKlass  @bci org/apache/logging/log4j/core/config/plugins/processor/PluginCache loadCacheFiles (Ljava/util/Enumeration;)V 137 <appendix> member <vmtarget> ; # org/apache/logging/log4j/core/config/plugins/processor/PluginCache$$Lambda+0x0000029e900840d0
instanceKlass  @cpi org/apache/logging/log4j/core/config/plugins/processor/PluginCache 249 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000029e9003cc00
instanceKlass org/apache/logging/log4j/core/config/plugins/processor/PluginEntry
instanceKlass  @bci org/apache/logging/log4j/core/config/plugins/processor/PluginCache getCategory (Ljava/lang/String;)Ljava/util/Map; 10 <appendix> argL0 ; # org/apache/logging/log4j/core/config/plugins/processor/PluginCache$$Lambda+0x0000029e90083c30
instanceKlass jdk/internal/util/ByteArray
instanceKlass org/apache/logging/log4j/core/config/plugins/processor/PluginCache
instanceKlass org/apache/logging/log4j/core/config/plugins/util/ResolverUtil$Test
instanceKlass org/apache/logging/log4j/core/config/plugins/util/PluginRegistry
instanceKlass org/apache/logging/log4j/core/config/plugins/util/PluginManager
instanceKlass org/apache/logging/log4j/core/lookup/PropertiesLookup
instanceKlass org/apache/logging/log4j/core/lookup/LookupResult
instanceKlass org/apache/logging/log4j/core/lookup/AbstractLookup
instanceKlass org/apache/logging/log4j/core/config/LoggerContextAware
instanceKlass org/apache/logging/log4j/core/config/DefaultAdvertiser
instanceKlass org/apache/logging/log4j/core/config/Property
instanceKlass org/apache/logging/log4j/core/config/ConfigurationSource
instanceKlass org/apache/logging/log4j/core/util/Watcher
instanceKlass org/apache/logging/log4j/core/Appender
instanceKlass org/apache/logging/log4j/core/Layout
instanceKlass org/apache/logging/log4j/core/layout/Encoder
instanceKlass org/apache/logging/log4j/core/async/AsyncLoggerConfigDelegate
instanceKlass org/apache/logging/log4j/core/util/NanoClock
instanceKlass org/apache/logging/log4j/core/lookup/StrLookup
instanceKlass org/apache/logging/log4j/core/net/Advertiser
instanceKlass org/apache/logging/log4j/core/lookup/StrSubstitutor
instanceKlass org/apache/logging/log4j/core/config/ConfigurationAware
instanceKlass org/apache/logging/log4j/core/Filter
instanceKlass  @bci org/apache/logging/log4j/core/LoggerContext createInstanceFromFactoryProperty (Ljava/lang/Class;Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object; 3 <appendix> member <vmtarget> ; # org/apache/logging/log4j/core/LoggerContext$$Lambda+0x0000029e9003fac0
instanceKlass org/apache/logging/log4j/message/ReusableMessageFactory
instanceKlass java/util/stream/WhileOps$DropWhileSink
instanceKlass  @bci org/apache/logging/log4j/util/StackLocator lambda$getCallerClass$2 (Ljava/lang/Class;Ljava/util/function/Predicate;Ljava/util/stream/Stream;)Ljava/lang/Class; 24 <appendix> member <vmtarget> ; # org/apache/logging/log4j/util/StackLocator$$Lambda+0x0000029e9003f3e8
instanceKlass java/util/stream/WhileOps$DropWhileOp
instanceKlass java/util/stream/WhileOps
instanceKlass  @bci org/apache/logging/log4j/util/StackLocator lambda$getCallerClass$2 (Ljava/lang/Class;Ljava/util/function/Predicate;Ljava/util/stream/Stream;)Ljava/lang/Class; 12 <appendix> member <vmtarget> ; # org/apache/logging/log4j/util/StackLocator$$Lambda+0x0000029e9003f198
instanceKlass  @bci org/apache/logging/log4j/util/StackLocator lambda$getCallerClass$2 (Ljava/lang/Class;Ljava/util/function/Predicate;Ljava/util/stream/Stream;)Ljava/lang/Class; 1 <appendix> argL0 ; # org/apache/logging/log4j/util/StackLocator$$Lambda+0x0000029e9003ef60
instanceKlass  @bci org/apache/logging/log4j/util/StackLocator getCallerClass (Ljava/lang/Class;Ljava/util/function/Predicate;)Ljava/lang/Class; 33 <appendix> member <vmtarget> ; # org/apache/logging/log4j/util/StackLocator$$Lambda+0x0000029e9003ed20
instanceKlass org/apache/logging/log4j/internal/LogManagerStatus
instanceKlass java/lang/Shutdown$Lock
instanceKlass java/lang/Shutdown
instanceKlass java/lang/ApplicationShutdownHooks$1
instanceKlass java/lang/ApplicationShutdownHooks
instanceKlass java/lang/invoke/VarHandle$AccessDescriptor
instanceKlass java/util/concurrent/atomic/AtomicReference
instanceKlass java/util/concurrent/Executors$DefaultThreadFactory
instanceKlass java/util/concurrent/Executors
instanceKlass org/apache/logging/log4j/core/util/Cancellable
instanceKlass org/apache/logging/log4j/core/util/DefaultShutdownCallbackRegistry
instanceKlass  @bci org/apache/logging/log4j/core/selector/ClassLoaderContextSelector <init> ()V 6 <appendix> member <vmtarget> ; # org/apache/logging/log4j/core/selector/ClassLoaderContextSelector$$Lambda+0x0000029e9003e000
instanceKlass org/apache/logging/log4j/core/AbstractLifeCycle
instanceKlass org/apache/logging/log4j/core/LifeCycle2
instanceKlass org/apache/logging/log4j/spi/LoggerContextShutdownEnabled
instanceKlass org/apache/logging/log4j/core/config/ConfigurationListener
instanceKlass org/apache/logging/log4j/spi/Terminable
instanceKlass org/apache/logging/log4j/core/selector/ClassLoaderContextSelector
instanceKlass  @bci org/apache/logging/log4j/util/LoaderUtil newCheckedInstanceOfProperty (Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Object; 2 <appendix> argL0 ; # org/apache/logging/log4j/util/LoaderUtil$$Lambda+0x0000029e9003a940
instanceKlass org/apache/logging/log4j/core/util/Loader
instanceKlass org/apache/logging/log4j/util/Constants
instanceKlass org/apache/logging/log4j/core/util/Constants
instanceKlass  @bci org/apache/logging/log4j/util/ProviderUtil selectProvider (Lorg/apache/logging/log4j/util/PropertiesUtil;Ljava/util/Collection;Lorg/apache/logging/log4j/Logger;)Lorg/apache/logging/log4j/spi/Provider; 376 <appendix> argL0 ; # org/apache/logging/log4j/util/ProviderUtil$$Lambda+0x0000029e9003a128
instanceKlass java/util/stream/ReduceOps$2ReducingSink
instanceKlass  @bci java/util/function/BinaryOperator maxBy (Ljava/util/Comparator;)Ljava/util/function/BinaryOperator; 6 <appendix> member <vmtarget> ; # java/util/function/BinaryOperator$$Lambda+0x0000029e900621b8
instanceKlass java/util/HashMap$HashMapSpliterator
instanceKlass  @bci java/util/Comparator comparing (Ljava/util/function/Function;)Ljava/util/Comparator; 6 <appendix> member <vmtarget> ; # java/util/Comparator$$Lambda+0x0000029e90061f20
instanceKlass  @bci java/lang/invoke/BootstrapMethodInvoker invoke (Ljava/lang/Class;Ljava/lang/invoke/MethodHandle;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object; 462 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000029e9003c800
instanceKlass  @cpi org/apache/logging/log4j/util/StackLocator 334 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000029e9003c400
instanceKlass  @bci org/apache/logging/log4j/util/ProviderUtil selectProvider (Lorg/apache/logging/log4j/util/PropertiesUtil;Ljava/util/Collection;Lorg/apache/logging/log4j/Logger;)Lorg/apache/logging/log4j/spi/Provider; 256 <appendix> argL0 ; # org/apache/logging/log4j/util/ProviderUtil$$Lambda+0x0000029e90039ef0
instanceKlass  @bci org/apache/logging/log4j/util/PropertiesUtil$Environment get (Ljava/lang/String;)Ljava/lang/String; 35 <appendix> argL0 ; # org/apache/logging/log4j/util/PropertiesUtil$Environment$$Lambda+0x0000029e90039ca8
instanceKlass  @bci org/apache/logging/log4j/util/PropertiesUtil$Environment get (Ljava/lang/String;)Ljava/lang/String; 25 <appendix> member <vmtarget> ; # org/apache/logging/log4j/util/PropertiesUtil$Environment$$Lambda+0x0000029e90039a68
instanceKlass java/util/stream/SortedOps
instanceKlass org/apache/logging/log4j/util/PropertySource$Comparator
instanceKlass  @bci java/util/regex/Pattern union (Ljava/util/regex/Pattern$CharPredicate;Ljava/util/regex/Pattern$CharPredicate;Z)Ljava/util/regex/Pattern$CharPredicate; 6 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x800000033
instanceKlass  @bci java/util/regex/Pattern Range (II)Ljava/util/regex/Pattern$CharPredicate; 23 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x80000002b
instanceKlass org/apache/logging/log4j/util/PropertySource$Util
instanceKlass org/apache/logging/log4j/util/Cast
instanceKlass org/apache/logging/log4j/util/LazyUtil$1
instanceKlass org/apache/logging/log4j/util/LazyUtil
instanceKlass org/apache/logging/log4j/util/SystemPropertiesPropertySource
instanceKlass org/apache/logging/log4j/util/EnvironmentPropertySource
instanceKlass  @bci org/apache/logging/log4j/util/PropertiesUtil$Environment <init> (Lorg/apache/logging/log4j/util/PropertySource;)V 99 <appendix> member <vmtarget> ; # org/apache/logging/log4j/util/PropertiesUtil$Environment$$Lambda+0x0000029e900388b8
instanceKlass java/util/Collections$SynchronizedCollection
instanceKlass java/util/Properties$EntrySet
instanceKlass  @bci org/apache/logging/log4j/util/PropertiesUtil$Environment <init> (Lorg/apache/logging/log4j/util/PropertySource;)V 34 <appendix> argL0 ; # org/apache/logging/log4j/util/PropertiesUtil$Environment$$Lambda+0x0000029e90038678
instanceKlass  @cpi org/eclipse/lsp4j/jsonrpc/services/ServiceEndpoints 155 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000029e9003c000
instanceKlass org/apache/logging/log4j/util/BiConsumer
instanceKlass org/apache/logging/log4j/util/PropertiesUtil$Environment
instanceKlass org/apache/logging/log4j/util/PropertiesPropertySource
instanceKlass  @bci java/util/regex/CharPredicates ASCII_WORD ()Ljava/util/regex/Pattern$BmpCharPredicate; 0 <appendix> argL0 ; # java/util/regex/CharPredicates$$Lambda+0x0000029e90060bb0
instanceKlass  @bci java/util/regex/CharPredicates ASCII_SPACE ()Ljava/util/regex/Pattern$BmpCharPredicate; 0 <appendix> argL0 ; # java/util/regex/CharPredicates$$Lambda+0x800000027
instanceKlass  @bci java/util/regex/CharPredicates ASCII_DIGIT ()Ljava/util/regex/Pattern$BmpCharPredicate; 0 <appendix> argL0 ; # java/util/regex/CharPredicates$$Lambda+0x800000026
instanceKlass  @bci org/apache/logging/log4j/util/PropertiesUtil <clinit> ()V 6 <appendix> argL0 ; # org/apache/logging/log4j/util/PropertiesUtil$$Lambda+0x0000029e900319f8
instanceKlass org/apache/logging/log4j/util/PropertySource
instanceKlass org/apache/logging/log4j/util/PropertiesUtil
instanceKlass org/apache/logging/log4j/util/LoaderUtil$ThreadContextClassLoaderGetter
instanceKlass  @bci org/apache/logging/log4j/util/LoaderUtil <clinit> ()V 23 <appendix> argL0 ; # org/apache/logging/log4j/util/LoaderUtil$$Lambda+0x0000029e90033920
instanceKlass  @cpi org/apache/logging/log4j/util/LoaderUtil 455 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000029e90031400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90031000
instanceKlass org/apache/logging/log4j/util/LazyBoolean
instanceKlass java/util/function/BooleanSupplier
instanceKlass org/apache/logging/log4j/util/LoaderUtil
instanceKlass  @bci org/apache/logging/log4j/core/impl/Log4jProvider <init> ()V 27 <appendix> member <vmtarget> ; # org/apache/logging/log4j/core/impl/Log4jProvider$$Lambda+0x0000029e90033070
instanceKlass org/apache/logging/log4j/util/LazyUtil$SafeLazy
instanceKlass org/apache/logging/log4j/util/Lazy
instanceKlass  @bci org/apache/logging/log4j/core/impl/Log4jProvider <init> ()V 14 <appendix> argL0 ; # org/apache/logging/log4j/core/impl/Log4jProvider$$Lambda+0x0000029e900329d8
instanceKlass org/apache/logging/log4j/core/selector/ContextSelector
instanceKlass org/apache/logging/log4j/core/config/Configuration
instanceKlass org/apache/logging/log4j/core/filter/Filterable
instanceKlass org/apache/logging/log4j/core/LifeCycle
instanceKlass org/apache/logging/log4j/spi/LoggerContext
instanceKlass org/apache/logging/log4j/core/impl/Log4jContextFactory
instanceKlass org/apache/logging/log4j/core/util/ShutdownCallbackRegistry
instanceKlass java/net/InetAddress
instanceKlass java/util/ServiceLoader$ProviderImpl
instanceKlass java/util/ServiceLoader$Provider
instanceKlass org/apache/logging/log4j/spi/ThreadContextMap
instanceKlass org/apache/logging/log4j/spi/LoggerContextFactory
instanceKlass java/util/ServiceLoader$1
instanceKlass  @bci org/springframework/boot/loader/jar/NestedJarFileResources releaseInflators (Ljava/io/IOException;)Ljava/io/IOException; 14 <appendix> argL0 ; # org/springframework/boot/loader/jar/NestedJarFileResources$$Lambda+0x0000029e9001f560
instanceKlass  @cpi org/springframework/boot/loader/jar/NestedJarFileResources 226 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000029e90030c00
instanceKlass java/nio/charset/CoderResult
instanceKlass java/io/Reader
instanceKlass java/lang/Readable
instanceKlass java/util/stream/ForEachOps$ForEachOp
instanceKlass java/util/stream/ForEachOps
instanceKlass  @bci org/apache/logging/log4j/util/ProviderUtil lazyInit ()V 48 <appendix> argL0 ; # org/apache/logging/log4j/util/ProviderUtil$$Lambda+0x0000029e90037180
instanceKlass  @bci org/apache/logging/log4j/util/ProviderUtil lazyInit ()V 38 <appendix> argL0 ; # org/apache/logging/log4j/util/ProviderUtil$$Lambda+0x0000029e90036f38
instanceKlass  @bci org/apache/logging/log4j/util/ServiceLoaderUtil safeStream (Ljava/lang/Class;Ljava/util/ServiceLoader;Lorg/apache/logging/log4j/Logger;)Ljava/util/stream/Stream; 71 <appendix> member <vmtarget> ; # org/apache/logging/log4j/util/ServiceLoaderUtil$$Lambda+0x0000029e90036ce8
instanceKlass org/apache/logging/log4j/util/OsgiServiceLocator
instanceKlass  @bci org/apache/logging/log4j/util/StackLocator getCallerClass (I)Ljava/lang/Class; 15 <appendix> argL0 ; # org/apache/logging/log4j/util/StackLocator$$Lambda+0x0000029e900368b0
instanceKlass  @cpi org/apache/logging/log4j/util/StackLocator 271 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000029e90030800
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 43 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x80000004d
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 38 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x80000004f
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 16 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x80000004e
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 11 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x800000050
instanceKlass java/util/stream/FindOps$FindOp
instanceKlass java/util/stream/FindOps$FindSink
instanceKlass java/util/stream/FindOps
instanceKlass java/util/stream/SliceOps
instanceKlass java/lang/StackStreamFactory$FrameBuffer
instanceKlass java/lang/StackStreamFactory
instanceKlass  @bci org/apache/logging/log4j/util/StackLocator getCallerClass (I)Ljava/lang/Class; 4 <appendix> member <vmtarget> ; # org/apache/logging/log4j/util/StackLocator$$Lambda+0x0000029e90036678
instanceKlass  @cpi org/apache/logging/log4j/util/StackLocator 277 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000029e90030400
instanceKlass org/apache/logging/log4j/util/StackLocator
instanceKlass org/apache/logging/log4j/util/StackLocatorUtil
instanceKlass org/apache/logging/log4j/util/ServiceLoaderUtil
instanceKlass org/apache/logging/log4j/spi/Provider
instanceKlass org/apache/logging/log4j/util/ProviderUtil
instanceKlass org/apache/logging/log4j/LogManager
instanceKlass org/slf4j/Marker
instanceKlass org/apache/logging/slf4j/Log4jMarkerFactory
instanceKlass org/slf4j/IMarkerFactory
instanceKlass org/slf4j/impl/StaticMarkerBinder
instanceKlass org/slf4j/spi/MarkerFactoryBinder
instanceKlass  @bci org/apache/logging/slf4j/Log4jLoggerFactory <clinit> ()V 6 <appendix> argL0 ; # org/apache/logging/slf4j/Log4jLoggerFactory$$Lambda+0x0000029e90034870
instanceKlass java/lang/invoke/VarForm
instanceKlass java/lang/invoke/VarHandleGuards
instanceKlass java/lang/invoke/VarHandles
instanceKlass java/util/concurrent/ConcurrentLinkedQueue$Node
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$WriteLock
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$ReadLock
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock
instanceKlass org/apache/logging/log4j/message/ExitMessage
instanceKlass org/apache/logging/log4j/message/EntryMessage
instanceKlass org/apache/logging/log4j/message/FlowMessage
instanceKlass org/apache/logging/log4j/status/StatusConsoleListener
instanceKlass  @bci java/util/regex/Pattern negate (Ljava/util/regex/Pattern$CharPredicate;)Ljava/util/regex/Pattern$CharPredicate; 1 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x800000032
instanceKlass  @bci java/util/regex/CharPredicates forUnicodeBlock (Ljava/lang/String;)Ljava/util/regex/Pattern$CharPredicate; 6 <appendix> member <vmtarget> ; # java/util/regex/CharPredicates$$Lambda+0x0000029e90059af8
instanceKlass java/lang/Character$Subset
instanceKlass java/util/regex/CharPredicates
instanceKlass java/util/regex/IntHashSet
instanceKlass java/util/regex/Matcher
instanceKlass java/util/regex/MatchResult
instanceKlass  @bci java/util/regex/Pattern DOT ()Ljava/util/regex/Pattern$CharPredicate; 0 <appendix> argL0 ; # java/util/regex/Pattern$$Lambda+0x0000029e90058ec0
instanceKlass  @bci org/apache/logging/log4j/status/StatusLogger$PropertiesUtilsDouble normalizeProperties ([Ljava/util/Properties;)Ljava/util/Map; 31 <appendix> member <vmtarget> ; # org/apache/logging/log4j/status/StatusLogger$PropertiesUtilsDouble$$Lambda+0x0000029e9002fd28
instanceKlass  @cpi org/eclipse/lsp4j/jsonrpc/services/ServiceEndpoints 160 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000029e90030000
instanceKlass java/net/URLClassLoader$2
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$UnmodifiableEntry
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$1
instanceKlass java/lang/ProcessEnvironment$CheckedEntry
instanceKlass java/lang/ProcessEnvironment$CheckedEntrySet$1
instanceKlass java/lang/ProcessEnvironment$EntryComparator
instanceKlass java/lang/ProcessEnvironment$NameComparator
instanceKlass org/apache/logging/log4j/status/StatusLogger$PropertiesUtilsDouble
instanceKlass org/apache/logging/log4j/status/StatusLogger$Config
instanceKlass org/apache/logging/log4j/status/StatusLogger$InstanceHolder
instanceKlass java/util/RegularEnumSet$EnumSetIterator
instanceKlass  @bci org/apache/logging/log4j/util/Strings <clinit> ()V 0 <appendix> argL0 ; # org/apache/logging/log4j/util/Strings$$Lambda+0x0000029e9002f208
instanceKlass org/apache/logging/log4j/util/Strings
instanceKlass org/apache/logging/log4j/Level
instanceKlass  @bci org/apache/logging/log4j/spi/AbstractLogger <clinit> ()V 142 <appendix> argL0 ; # org/apache/logging/log4j/spi/AbstractLogger$$Lambda+0x0000029e9002e5b0
instanceKlass org/apache/logging/log4j/internal/DefaultLogBuilder
instanceKlass org/apache/logging/log4j/BridgeAware
instanceKlass org/apache/logging/log4j/message/DefaultFlowMessageFactory
instanceKlass org/apache/logging/log4j/message/FlowMessageFactory
instanceKlass org/apache/logging/log4j/message/AbstractMessageFactory
instanceKlass  @bci org/apache/logging/log4j/MarkerManager getMarker (Ljava/lang/String;)Lorg/apache/logging/log4j/Marker; 4 <appendix> argL0 ; # org/apache/logging/log4j/MarkerManager$$Lambda+0x0000029e90027bf0
instanceKlass org/apache/logging/log4j/MarkerManager$Log4jMarker
instanceKlass org/apache/logging/log4j/util/StringBuilderFormattable
instanceKlass org/apache/logging/log4j/Marker
instanceKlass org/apache/logging/log4j/MarkerManager
instanceKlass org/apache/logging/log4j/status/StatusListener
instanceKlass java/util/EventListener
instanceKlass org/apache/logging/log4j/LogBuilder
instanceKlass org/apache/logging/log4j/message/Message
instanceKlass org/apache/logging/log4j/message/MessageFactory2
instanceKlass org/apache/logging/log4j/message/MessageFactory
instanceKlass org/apache/logging/log4j/spi/AbstractLogger
instanceKlass org/apache/logging/log4j/spi/LocationAwareLogger
instanceKlass org/apache/logging/log4j/spi/ExtendedLogger
instanceKlass org/apache/logging/log4j/Logger
instanceKlass java/util/concurrent/locks/ReadWriteLock
instanceKlass org/apache/logging/log4j/spi/AbstractLoggerAdapter
instanceKlass org/apache/logging/log4j/spi/LoggerContextShutdownAware
instanceKlass org/apache/logging/log4j/spi/LoggerAdapter
instanceKlass org/slf4j/impl/StaticLoggerBinder
instanceKlass org/slf4j/spi/LoggerFactoryBinder
instanceKlass java/lang/foreign/MemorySegment
instanceKlass java/lang/invoke/MethodHandleImpl$CountingWrapper$1
instanceKlass java/net/URLClassLoader$3$1
instanceKlass java/net/URLClassLoader$3
instanceKlass org/springframework/boot/loader/net/protocol/jar/JarUrlClassLoader$OptimizedEnumeration
instanceKlass org/slf4j/helpers/Util
instanceKlass org/slf4j/helpers/NOPLoggerFactory
instanceKlass java/util/concurrent/LinkedBlockingQueue$Node
instanceKlass java/util/concurrent/BlockingQueue
instanceKlass org/slf4j/Logger
instanceKlass org/slf4j/helpers/SubstituteLoggerFactory
instanceKlass org/slf4j/ILoggerFactory
instanceKlass org/slf4j/event/LoggingEvent
instanceKlass org/slf4j/LoggerFactory
instanceKlass org/eclipse/lsp4j/services/TextDocumentService
instanceKlass org/eclipse/lsp4j/services/WorkspaceService
instanceKlass com/github/cameltooling/lsp/internal/AbstractLanguageServer
instanceKlass org/eclipse/lsp4j/services/LanguageClientAware
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90023800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90023400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90023000
instanceKlass  @cpi org/apache/logging/log4j/util/ProviderUtil 449 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000029e90022c00
instanceKlass org/eclipse/lsp4j/services/LanguageServer
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint lambda$recursiveFindRpcMethods$2 (Ljava/lang/Object;Ljava/util/Set;Ljava/util/Set;Ljava/lang/reflect/Method;)V 33 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000029e90022800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90022400
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint lambda$recursiveFindRpcMethods$2 (Ljava/lang/Object;Ljava/util/Set;Ljava/util/Set;Ljava/lang/reflect/Method;)V 33 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000029e90022000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90021c00
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint lambda$recursiveFindRpcMethods$2 (Ljava/lang/Object;Ljava/util/Set;Ljava/util/Set;Ljava/lang/reflect/Method;)V 33 <appendix> argL2 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000029e90021800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90021400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90021000
instanceKlass java/util/TreeMap$TreeMapSpliterator
instanceKlass java/util/NavigableSet
instanceKlass java/util/SortedSet
instanceKlass  @bci org/springframework/boot/loader/jar/MetaInfVersionsInfo get (Lorg/springframework/boot/loader/zip/ZipContent;)Lorg/springframework/boot/loader/jar/MetaInfVersionsInfo; 10 <appendix> member <vmtarget> ; # org/springframework/boot/loader/jar/MetaInfVersionsInfo$$Lambda+0x0000029e9001f0f0
instanceKlass java/util/stream/Nodes$ArrayNode
instanceKlass  @bci org/springframework/boot/loader/jar/MetaInfVersionsInfo <init> (Ljava/util/Set;)V 46 <appendix> argL0 ; # org/springframework/boot/loader/jar/MetaInfVersionsInfo$$Lambda+0x0000029e9001eed8
instanceKlass  @cpi org/springframework/boot/loader/jar/MetaInfVersionsInfo 211 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000029e90020400
instanceKlass  @bci org/springframework/boot/loader/jar/MetaInfVersionsInfo <init> (Ljava/util/Set;)V 36 <appendix> argL0 ; # org/springframework/boot/loader/jar/MetaInfVersionsInfo$$Lambda+0x0000029e9001eca0
instanceKlass java/util/stream/Nodes$IntArrayNode
instanceKlass java/util/stream/Node$Builder$OfInt
instanceKlass java/util/stream/Sink$OfInt
instanceKlass java/util/function/IntConsumer
instanceKlass java/util/stream/Node$Builder
instanceKlass java/util/stream/Node$OfDouble
instanceKlass java/util/stream/Node$OfLong
instanceKlass java/util/stream/Node$OfInt
instanceKlass java/util/stream/Node$OfPrimitive
instanceKlass java/util/stream/Nodes$EmptyNode
instanceKlass java/util/stream/Node
instanceKlass java/util/stream/Nodes
instanceKlass  @bci java/util/stream/IntPipeline toArray ()[I 1 <appendix> argL0 ; # java/util/stream/IntPipeline$$Lambda+0x0000029e900524c0
instanceKlass java/util/stream/IntStream
instanceKlass  @bci org/springframework/boot/loader/jar/MetaInfVersionsInfo <init> (Ljava/util/Set;)V 11 <appendix> argL0 ; # org/springframework/boot/loader/jar/MetaInfVersionsInfo$$Lambda+0x0000029e9001ea88
instanceKlass java/util/function/ToIntFunction
instanceKlass  @bci org/springframework/boot/loader/jar/NestedJarFile getMetaInfVersionsInfo ()Lorg/springframework/boot/loader/jar/MetaInfVersionsInfo; 29 <appendix> argL0 ; # org/springframework/boot/loader/jar/NestedJarFile$$Lambda+0x0000029e9001e850
instanceKlass org/springframework/boot/loader/jar/MetaInfVersionsInfo
instanceKlass com/github/cameltooling/lsp/internal/Runner
instanceKlass  @bci java/io/FilePermissionCollection add (Ljava/security/Permission;)V 68 <appendix> argL0 ; # java/io/FilePermissionCollection$$Lambda+0x0000029e90051d98
instanceKlass java/security/Security$1
instanceKlass jdk/internal/access/JavaSecurityPropertiesAccess
instanceKlass java/util/concurrent/ConcurrentHashMap$MapEntry
instanceKlass java/io/FileInputStream$1
instanceKlass java/util/Properties$LineReader
instanceKlass  @bci java/security/Security <clinit> ()V 9 <appendix> argL0 ; # java/security/Security$$Lambda+0x80000000b
instanceKlass java/security/Security
instanceKlass sun/security/util/SecurityProperties
instanceKlass sun/security/util/FilePermCompat
instanceKlass java/io/FilePermission$1
instanceKlass jdk/internal/access/JavaIOFilePermissionAccess
instanceKlass  @bci org/springframework/boot/loader/net/protocol/jar/JarFileUrlKey get (Ljava/net/URL;)Ljava/lang/String; 45 <appendix> argL0 ; # org/springframework/boot/loader/net/protocol/jar/JarFileUrlKey$$Lambda+0x0000029e9001e410
instanceKlass org/springframework/boot/loader/net/protocol/jar/JarFileUrlKey
instanceKlass org/springframework/boot/loader/net/protocol/jar/Canonicalizer
instanceKlass java/net/URLClassLoader$1
instanceKlass  @bci org/springframework/boot/loader/net/protocol/jar/UrlJarManifest get ()Ljava/util/jar/Manifest; 42 <appendix> member <vmtarget> ; # org/springframework/boot/loader/net/protocol/jar/UrlJarManifest$$Lambda+0x0000029e9001dde0
instanceKlass  @cpi org/springframework/boot/loader/net/protocol/jar/UrlJarManifest 117 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000029e90020000
instanceKlass  @bci org/springframework/boot/loader/jar/NestedJarFileResources createInflatorCleanupAction (Ljava/util/zip/Inflater;)Ljava/lang/Runnable; 2 <appendix> member <vmtarget> ; # org/springframework/boot/loader/jar/NestedJarFileResources$$Lambda+0x0000029e9001dbc0
instanceKlass  @bci org/springframework/boot/loader/jar/NestedJarFile getManifest ()Ljava/util/jar/Manifest; 10 <appendix> member <vmtarget> ; # org/springframework/boot/loader/jar/NestedJarFile$$Lambda+0x0000029e9001d780
instanceKlass  @bci org/springframework/boot/loader/zip/ZipContent getInfo (Ljava/lang/Class;Ljava/util/function/Function;)Ljava/lang/Object; 51 <appendix> member <vmtarget> ; # org/springframework/boot/loader/zip/ZipContent$$Lambda+0x0000029e9001d540
instanceKlass  @bci org/springframework/boot/loader/jar/NestedJarFile getManifestInfo ()Lorg/springframework/boot/loader/jar/ManifestInfo; 29 <appendix> member <vmtarget> ; # org/springframework/boot/loader/jar/NestedJarFile$$Lambda+0x0000029e9001d300
instanceKlass org/springframework/boot/loader/jar/ManifestInfo
instanceKlass  @bci org/springframework/boot/loader/net/protocol/jar/UrlNestedJarFile <init> (Ljava/io/File;Ljava/lang/String;Ljava/lang/Runtime$Version;Ljava/util/function/Consumer;)V 13 <appendix> member <vmtarget> ; # org/springframework/boot/loader/net/protocol/jar/UrlNestedJarFile$$Lambda+0x0000029e9001cec8
instanceKlass org/springframework/boot/loader/net/protocol/jar/UrlJarManifest$ManifestSupplier
instanceKlass org/springframework/boot/loader/net/protocol/jar/UrlJarManifest
instanceKlass  @bci org/springframework/boot/loader/zip/ZipContent$Source equals (Ljava/lang/Object;)Z 2 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000029e9001bc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e9001b800
instanceKlass  @bci org/springframework/boot/loader/zip/ZipContent$Source equals (Ljava/lang/Object;)Z 2 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000029e9001b400
instanceKlass jdk/internal/org/objectweb/asm/Edge
instanceKlass  @bci org/springframework/boot/loader/zip/ZipContent$Source equals (Ljava/lang/Object;)Z 2 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000029e9001b000
instanceKlass  @bci org/springframework/boot/loader/zip/ZipContent$Source equals (Ljava/lang/Object;)Z 2 <appendix> form names 9 function resolvedHandle form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000029e9001ac00
instanceKlass  @bci org/springframework/boot/loader/zip/ZipContent$Source equals (Ljava/lang/Object;)Z 2 <appendix> form names 9 function resolvedHandle form names 5 function resolvedHandle form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000029e9001a800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e9001a400
instanceKlass java/lang/invoke/MethodHandleImpl$LoopClauses
instanceKlass java/lang/invoke/MethodHandleImpl$CasesHolder
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e9001a000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90019c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90019800
instanceKlass  @bci org/springframework/boot/loader/zip/ZipContent$Source equals (Ljava/lang/Object;)Z 2 <appendix> form names 7 function resolvedHandle form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000029e90019400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90019000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90018c00
instanceKlass java/util/BitSet
instanceKlass org/springframework/boot/loader/zip/ZipContent$Entry
instanceKlass jdk/internal/foreign/MemorySessionImpl
instanceKlass java/lang/foreign/MemorySegment$Scope
instanceKlass org/springframework/boot/loader/zip/ZipString
instanceKlass org/springframework/boot/loader/zip/NameOffsetLookups
instanceKlass sun/nio/ch/IOStatus
instanceKlass sun/nio/ch/NativeThread
instanceKlass java/nio/channels/spi/AbstractInterruptibleChannel$1
instanceKlass sun/nio/ch/Interruptible
instanceKlass  @bci org/springframework/boot/loader/zip/FileDataBlock read (Ljava/nio/ByteBuffer;J)I 17 <appendix> argL0 ; # org/springframework/boot/loader/zip/FileDataBlock$$Lambda+0x0000029e9000f5d8
instanceKlass java/nio/DirectByteBuffer$Deallocator
instanceKlass sun/nio/ch/FileChannelImpl$Closer
instanceKlass sun/nio/ch/NativeThreadSet
instanceKlass sun/nio/ch/IOUtil
instanceKlass sun/nio/ch/NativeDispatcher
instanceKlass sun/nio/fs/WindowsChannelFactory$2
instanceKlass sun/nio/fs/WindowsChannelFactory$Flags
instanceKlass sun/nio/fs/WindowsChannelFactory$1
instanceKlass sun/nio/fs/WindowsChannelFactory
instanceKlass sun/nio/fs/WindowsSecurityDescriptor
instanceKlass java/nio/channels/spi/AbstractInterruptibleChannel
instanceKlass java/nio/channels/InterruptibleChannel
instanceKlass java/nio/channels/ScatteringByteChannel
instanceKlass java/nio/channels/GatheringByteChannel
instanceKlass java/nio/channels/SeekableByteChannel
instanceKlass java/nio/channels/ByteChannel
instanceKlass java/nio/channels/WritableByteChannel
instanceKlass java/nio/channels/ReadableByteChannel
instanceKlass java/nio/channels/Channel
instanceKlass org/springframework/boot/loader/zip/FileDataBlock$FileAccess
instanceKlass org/springframework/boot/loader/zip/FileDataBlock$Tracker$1
instanceKlass org/springframework/boot/loader/zip/FileDataBlock$Tracker
instanceKlass org/springframework/boot/loader/zip/FileDataBlock
instanceKlass org/springframework/boot/loader/zip/ZipContent$Loader
instanceKlass  @bci org/springframework/boot/loader/zip/ZipContent$Source hashCode ()I 1 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000029e90018800
instanceKlass  @bci org/springframework/boot/loader/zip/ZipContent$Source hashCode ()I 1 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000029e90018400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90018000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90017c00
instanceKlass  @bci org/springframework/boot/loader/zip/ZipContent$Source hashCode ()I 1 <appendix> form names 7 function resolvedHandle form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000029e90017800
instanceKlass  @bci org/springframework/boot/loader/zip/ZipContent$Source hashCode ()I 1 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000029e90017400
instanceKlass  @bci org/springframework/boot/loader/zip/ZipContent$Source hashCode ()I 1 <appendix> argL2 argL2 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000029e90017000
instanceKlass  @bci java/lang/runtime/ObjectMethods bootstrap (Ljava/lang/invoke/MethodHandles$Lookup;Ljava/lang/String;Ljava/lang/invoke/TypeDescriptor;Ljava/lang/Class;Ljava/lang/String;[Ljava/lang/invoke/MethodHandle;)Ljava/lang/Object; 37 <appendix> argL0 ; # java/lang/runtime/ObjectMethods$$Lambda+0x0000029e9004b600
instanceKlass java/lang/invoke/DirectMethodHandle$1
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90016c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90016800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90016400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90016000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90015c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90015800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90015400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90015000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90014c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90014800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90014400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90014000
instanceKlass java/lang/runtime/ObjectMethods$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90013c00
instanceKlass sun/invoke/util/ValueConversions$WrapperCache
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90013800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90013400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90013000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90012c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90012800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90012400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90012000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90011c00
instanceKlass java/lang/invoke/MethodHandleImpl$ArrayAccessor
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90011800
instanceKlass  @bci org/springframework/boot/loader/jar/NestedJarFile <init> (Ljava/io/File;Ljava/lang/String;Ljava/lang/Runtime$Version;ZLorg/springframework/boot/loader/ref/Cleaner;)V 87 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000029e90011400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90011000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90010c00
instanceKlass java/lang/ClassValue$Version
instanceKlass java/lang/ClassValue$Identity
instanceKlass java/lang/ClassValue
instanceKlass java/lang/invoke/MethodHandleImpl$Makers$2
instanceKlass java/lang/invoke/MethodHandleImpl$Makers$1
instanceKlass java/lang/invoke/MethodHandleImpl$Makers
instanceKlass  @bci java/lang/invoke/BootstrapMethodInvoker invoke (Ljava/lang/Class;Ljava/lang/invoke/MethodHandle;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object; 862 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000029e90010800
instanceKlass  @bci org/springframework/boot/loader/zip/ZipContent$Source hashCode ()I 1 <appendix> argL1 argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000029e90010400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000029e90010000
instanceKlass java/lang/runtime/ObjectMethods
instanceKlass org/springframework/boot/loader/zip/CloseableDataBlock
instanceKlass org/springframework/boot/loader/zip/DataBlock
instanceKlass org/springframework/boot/loader/zip/ZipContent
instanceKlass org/springframework/boot/loader/jar/NestedJarFileResources
instanceKlass org/springframework/boot/loader/log/DebugLogger
instanceKlass  @bci org/springframework/boot/loader/net/protocol/jar/UrlJarFiles getOrCreate (ZLjava/net/URL;)Ljava/util/jar/JarFile; 22 <appendix> member <vmtarget> ; # org/springframework/boot/loader/net/protocol/jar/UrlJarFiles$$Lambda+0x0000029e90007c38
instanceKlass  @cpi org/eclipse/lsp4j/jsonrpc/services/EndpointProxy 172 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000029e9000bc00
instanceKlass org/springframework/boot/loader/net/protocol/nested/NestedUrlConnectionResources
instanceKlass org/springframework/boot/loader/net/util/UrlDecoder
instanceKlass jdk/internal/ref/CleanerImpl$InnocuousThreadFactory
instanceKlass org/springframework/boot/loader/ref/DefaultCleaner
instanceKlass org/springframework/boot/loader/ref/Cleaner
instanceKlass java/time/LocalDateTime
instanceKlass java/time/chrono/ChronoLocalDateTime
instanceKlass java/time/temporal/Temporal
instanceKlass java/time/zone/ZoneOffsetTransitionRule
instanceKlass java/time/zone/ZoneRules
instanceKlass  @bci java/time/ZoneOffset ofTotalSeconds (I)Ljava/time/ZoneOffset; 37 <appendix> argL0 ; # java/time/ZoneOffset$$Lambda+0x80000000e
instanceKlass java/time/temporal/TemporalAdjuster
instanceKlass  @bci java/time/format/DateTimeFormatter <clinit> ()V 1075 <appendix> argL0 ; # java/time/format/DateTimeFormatter$$Lambda+0x800000010
instanceKlass  @bci java/time/format/DateTimeFormatter <clinit> ()V 1067 <appendix> argL0 ; # java/time/format/DateTimeFormatter$$Lambda+0x80000000f
instanceKlass java/time/Period
instanceKlass java/time/chrono/ChronoPeriod
instanceKlass java/time/format/DateTimeFormatterBuilder$TextPrinterParser
instanceKlass java/util/TimSort
instanceKlass java/util/Arrays$LegacyMergeSort
instanceKlass java/time/format/DateTimeTextProvider$1
instanceKlass java/time/format/DateTimeTextProvider
instanceKlass java/util/LinkedHashMap$LinkedHashIterator
instanceKlass java/util/Collections$1
instanceKlass java/util/AbstractMap$SimpleImmutableEntry
instanceKlass java/time/format/DateTimeTextProvider$LocaleStore
instanceKlass java/time/format/DateTimeFormatterBuilder$InstantPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$StringLiteralPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$ZoneIdPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$OffsetIdPrinterParser
instanceKlass java/time/format/DecimalStyle
instanceKlass java/time/format/DateTimeFormatterBuilder$CompositePrinterParser
instanceKlass java/time/chrono/AbstractChronology
instanceKlass java/time/chrono/Chronology
instanceKlass java/time/format/DateTimeFormatterBuilder$CharLiteralPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$NumberPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$DateTimePrinterParser
instanceKlass java/time/temporal/JulianFields
instanceKlass java/time/temporal/IsoFields
instanceKlass java/time/temporal/ValueRange
instanceKlass java/time/Duration
instanceKlass java/time/temporal/TemporalAmount
instanceKlass java/time/temporal/TemporalUnit
instanceKlass java/time/temporal/TemporalField
instanceKlass java/util/regex/Pattern$TreeInfo
instanceKlass java/util/regex/Pattern$BitClass
instanceKlass java/util/regex/ASCII
instanceKlass  @bci java/util/regex/Pattern Single (I)Ljava/util/regex/Pattern$BmpCharPredicate; 1 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x80000002a
instanceKlass java/util/regex/Pattern$BmpCharPredicate
instanceKlass java/util/regex/Pattern$CharPredicate
instanceKlass java/util/regex/Pattern$Node
instanceKlass java/util/regex/Pattern
instanceKlass  @bci java/time/format/DateTimeFormatterBuilder <clinit> ()V 0 <appendix> argL0 ; # java/time/format/DateTimeFormatterBuilder$$Lambda+0x800000011
instanceKlass java/time/ZoneId
instanceKlass java/time/temporal/TemporalQuery
instanceKlass java/time/format/DateTimeFormatterBuilder
instanceKlass java/time/format/DateTimeFormatter
instanceKlass java/time/temporal/TemporalAccessor
instanceKlass  @bci org/springframework/boot/loader/net/protocol/jar/JarUrlConnection <clinit> ()V 65 <appendix> argL0 ; # org/springframework/boot/loader/net/protocol/jar/JarUrlConnection$$Lambda+0x0000029e90006f00
instanceKlass org/springframework/boot/loader/net/protocol/jar/UrlJarFiles$Cache
instanceKlass org/springframework/boot/loader/net/protocol/jar/UrlJarFileFactory
instanceKlass org/springframework/boot/loader/net/protocol/jar/UrlJarFiles
instanceKlass  @bci java/util/logging/LogManager$LoggerContext$1 run ()Ljava/lang/Void; 22 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000029e9000b800
instanceKlass org/springframework/boot/loader/net/protocol/jar/Optimizations
instanceKlass  @bci java/util/stream/MatchOps makeRef (Ljava/util/function/Predicate;Ljava/util/stream/MatchOps$MatchKind;)Ljava/util/stream/TerminalOp; 20 <appendix> member <vmtarget> ; # java/util/stream/MatchOps$$Lambda+0x0000029e90045e88
instanceKlass java/util/stream/MatchOps$BooleanTerminalSink
instanceKlass java/util/stream/MatchOps$MatchOp
instanceKlass java/util/stream/MatchOps
instanceKlass  @bci org/springframework/boot/loader/net/protocol/jar/JarUrlClassLoader <init> ([Ljava/net/URL;Ljava/lang/ClassLoader;)V 35 <appendix> member <vmtarget> ; # org/springframework/boot/loader/net/protocol/jar/JarUrlClassLoader$$Lambda+0x0000029e90005430
instanceKlass  @cpi sun/management/spi/PlatformMBeanProvider$PlatformComponent 113 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000029e9000b400
instanceKlass jdk/internal/loader/URLClassPath$1
instanceKlass java/lang/CompoundEnumeration
instanceKlass jdk/internal/loader/BuiltinClassLoader$1
instanceKlass java/util/Collections$EmptyEnumeration
instanceKlass java/util/Collections$EmptyIterator
instanceKlass jdk/internal/jimage/ImageLocation
instanceKlass jdk/internal/jimage/decompressor/Decompressor
instanceKlass jdk/internal/jimage/ImageStringsReader
instanceKlass jdk/internal/jimage/ImageStrings
instanceKlass jdk/internal/jimage/ImageHeader
instanceKlass jdk/internal/jimage/NativeImageBuffer$1
instanceKlass jdk/internal/jimage/NativeImageBuffer
instanceKlass jdk/internal/jimage/BasicImageReader$1
instanceKlass jdk/internal/jimage/BasicImageReader
instanceKlass jdk/internal/jimage/ImageReader
instanceKlass jdk/internal/jimage/ImageReaderFactory$1
instanceKlass jdk/internal/jimage/ImageReaderFactory
instanceKlass jdk/internal/module/SystemModuleFinders$SystemImage
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleReader
instanceKlass java/lang/module/ModuleReader
instanceKlass jdk/internal/loader/BuiltinClassLoader$5
instanceKlass jdk/internal/loader/BuiltinClassLoader$2
instanceKlass jdk/internal/module/Resources
instanceKlass java/util/ServiceLoader$3
instanceKlass java/util/ServiceLoader$2
instanceKlass java/util/ServiceLoader$LazyClassPathLookupIterator
instanceKlass java/util/Spliterators$1Adapter
instanceKlass java/util/Spliterators$ArraySpliterator
instanceKlass java/util/Spliterator$OfDouble
instanceKlass java/util/Spliterator$OfLong
instanceKlass java/util/Spliterator$OfInt
instanceKlass java/util/Spliterator$OfPrimitive
instanceKlass java/util/Spliterators$EmptySpliterator
instanceKlass java/util/Spliterators
instanceKlass java/util/ServiceLoader$ModuleServicesLookupIterator
instanceKlass java/util/ServiceLoader
instanceKlass java/net/spi/URLStreamHandlerProvider
instanceKlass java/net/URL$1
instanceKlass java/net/URL$2
instanceKlass java/net/URL$ThreadTrackHolder
instanceKlass  @bci org/springframework/boot/loader/net/protocol/nested/NestedLocation asPath (Ljava/lang/String;)Ljava/nio/file/Path; 5 <appendix> member <vmtarget> ; # org/springframework/boot/loader/net/protocol/nested/NestedLocation$$Lambda+0x0000029e900051f0
instanceKlass  @bci org/springframework/boot/loader/net/protocol/nested/NestedLocation parse (Ljava/lang/String;)Lorg/springframework/boot/loader/net/protocol/nested/NestedLocation; 26 <appendix> member <vmtarget> ; # org/springframework/boot/loader/net/protocol/nested/NestedLocation$$Lambda+0x0000029e90004fb0
instanceKlass java/net/URLConnection
instanceKlass  @bci io/fabric8/kubernetes/api/model/HasMetadata getFullResourceName (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String; 23 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000029e9000b000
instanceKlass java/util/TreeMap$PrivateEntryIterator
instanceKlass java/util/TreeMap$Entry
instanceKlass java/lang/invoke/LambdaFormEditor$1
instanceKlass java/util/NavigableMap
instanceKlass java/util/SortedMap
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e9000ac00
instanceKlass  @bci io/fabric8/kubernetes/api/model/HasMetadata getFullResourceName (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String; 23 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000029e9000a800
instanceKlass java/lang/invoke/MethodHandles$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e9000a400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e9000a000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90009c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90009800
instanceKlass  @bci io/fabric8/kubernetes/api/model/HasMetadata getFullResourceName (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String; 23 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000029e90009400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90009000
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint lambda$recursiveFindRpcMethods$2 (Ljava/lang/Object;Ljava/util/Set;Ljava/util/Set;Ljava/lang/reflect/Method;)V 33 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000029e90008c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90008800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90008400
instanceKlass java/lang/Long$LongCache
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90008000
instanceKlass org/springframework/boot/loader/net/protocol/jar/JarUrl
instanceKlass java/util/stream/Sink$ChainedReference
instanceKlass java/util/stream/ReduceOps$AccumulatingSink
instanceKlass java/util/stream/TerminalSink
instanceKlass java/util/stream/Sink
instanceKlass java/util/function/Consumer
instanceKlass java/util/stream/ReduceOps$Box
instanceKlass java/util/stream/ReduceOps$ReduceOp
instanceKlass java/util/stream/TerminalOp
instanceKlass java/util/stream/ReduceOps
instanceKlass  @bci java/util/stream/Collectors castingIdentity ()Ljava/util/function/Function; 0 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000043
instanceKlass  @bci java/util/stream/Collectors toCollection (Ljava/util/function/Supplier;)Ljava/util/stream/Collector; 10 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x0000029e90043b00
instanceKlass java/util/function/BinaryOperator
instanceKlass  @bci java/util/stream/Collectors toCollection (Ljava/util/function/Supplier;)Ljava/util/stream/Collector; 5 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x0000029e900438d8
instanceKlass java/util/function/BiConsumer
instanceKlass java/util/stream/Collector
instanceKlass java/util/Collections$UnmodifiableCollection
instanceKlass java/util/stream/Collectors
instanceKlass  @bci org/springframework/boot/loader/launch/JarFileArchive getClassPathUrls (Ljava/util/function/Predicate;Ljava/util/function/Predicate;)Ljava/util/Set; 34 <appendix> argL0 ; # org/springframework/boot/loader/launch/JarFileArchive$$Lambda+0x0000029e90003d40
instanceKlass java/util/SequencedSet
instanceKlass  @bci org/springframework/boot/loader/launch/JarFileArchive getClassPathUrls (Ljava/util/function/Predicate;Ljava/util/function/Predicate;)Ljava/util/Set; 24 <appendix> member <vmtarget> ; # org/springframework/boot/loader/launch/JarFileArchive$$Lambda+0x0000029e90003b00
instanceKlass  @bci org/springframework/boot/loader/launch/JarFileArchive getClassPathUrls (Ljava/util/function/Predicate;Ljava/util/function/Predicate;)Ljava/util/Set; 7 <appendix> argL0 ; # org/springframework/boot/loader/launch/JarFileArchive$$Lambda+0x0000029e900038c8
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000029e90002c00
instanceKlass jdk/internal/reflect/MethodHandleAccessorFactory$LazyStaticHolder
instanceKlass sun/reflect/annotation/AnnotationParser
instanceKlass java/lang/Class$3
instanceKlass java/util/EnumMap$1
instanceKlass java/util/stream/StreamOpFlag$MaskBuilder
instanceKlass java/util/stream/Stream
instanceKlass java/util/stream/BaseStream
instanceKlass java/util/stream/PipelineHelper
instanceKlass java/util/stream/StreamSupport
instanceKlass  @bci java/util/zip/ZipFile jarStream ()Ljava/util/stream/Stream; 25 <appendix> member <vmtarget> ; # java/util/zip/ZipFile$$Lambda+0x800000051
instanceKlass java/util/function/IntFunction
instanceKlass java/util/Spliterators$AbstractSpliterator
instanceKlass java/util/Spliterator
instanceKlass  @bci org/springframework/boot/loader/launch/ExecutableArchiveLauncher getClassPathUrls ()Ljava/util/Set; 11 <appendix> member <vmtarget> ; # org/springframework/boot/loader/launch/ExecutableArchiveLauncher$$Lambda+0x0000029e90003450
instanceKlass  @bci org/springframework/boot/loader/launch/ExecutableArchiveLauncher getClassPathUrls ()Ljava/util/Set; 5 <appendix> member <vmtarget> ; # org/springframework/boot/loader/launch/ExecutableArchiveLauncher$$Lambda+0x0000029e90003200
instanceKlass  @cpi org/springframework/boot/loader/launch/ExecutableArchiveLauncher 159 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000029e90002800
instanceKlass org/springframework/boot/loader/net/protocol/Handlers
instanceKlass java/nio/file/Paths
instanceKlass java/nio/file/attribute/PosixFilePermissions$1
instanceKlass java/nio/file/attribute/PosixFilePermissions
instanceKlass java/nio/file/attribute/FileAttribute
instanceKlass org/springframework/boot/loader/launch/JarFileArchive
instanceKlass sun/nio/fs/WindowsUriSupport
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder$1
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder
instanceKlass java/nio/file/FileSystems
instanceKlass java/net/URI$Parser
instanceKlass  @bci org/apache/logging/log4j/util/SortedArrayStringMap <clinit> ()V 0 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000029e90002400
instanceKlass java/lang/invoke/LambdaFormBuffer
instanceKlass java/lang/invoke/LambdaFormEditor$TransformKey
instanceKlass java/lang/invoke/LambdaFormEditor
instanceKlass java/lang/invoke/DelegatingMethodHandle$Holder
instanceKlass java/lang/invoke/DirectMethodHandle$2
instanceKlass java/lang/invoke/ClassSpecializer$Factory
instanceKlass java/lang/invoke/ClassSpecializer$SpeciesData
instanceKlass java/lang/invoke/ClassSpecializer$1
instanceKlass java/lang/invoke/ClassSpecializer
instanceKlass  @bci org/springframework/boot/loader/launch/Archive <clinit> ()V 0 <appendix> argL0 ; # org/springframework/boot/loader/launch/Archive$$Lambda+0x0000029e90001940
instanceKlass java/lang/invoke/LambdaProxyClassArchive
instanceKlass java/lang/invoke/InfoFromMemberName
instanceKlass java/lang/invoke/MethodHandleInfo
instanceKlass jdk/internal/org/objectweb/asm/ConstantDynamic
instanceKlass jdk/internal/org/objectweb/asm/Handle
instanceKlass sun/security/action/GetBooleanAction
instanceKlass java/lang/invoke/AbstractValidatingLambdaMetafactory
instanceKlass java/lang/invoke/MethodHandleImpl$1
instanceKlass jdk/internal/access/JavaLangInvokeAccess
instanceKlass java/lang/invoke/Invokers$Holder
instanceKlass java/lang/invoke/BootstrapMethodInvoker
instanceKlass  @cpi org/apache/logging/slf4j/Log4jLoggerFactory 168 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000029e90002000
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassDefiner
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassFile
instanceKlass jdk/internal/org/objectweb/asm/Handler
instanceKlass jdk/internal/org/objectweb/asm/Attribute
instanceKlass jdk/internal/org/objectweb/asm/FieldVisitor
instanceKlass sun/invoke/empty/Empty
instanceKlass sun/invoke/util/VerifyType
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$ClassData
instanceKlass jdk/internal/org/objectweb/asm/AnnotationVisitor
instanceKlass jdk/internal/org/objectweb/asm/Frame
instanceKlass jdk/internal/org/objectweb/asm/Label
instanceKlass jdk/internal/org/objectweb/asm/Type
instanceKlass jdk/internal/org/objectweb/asm/MethodVisitor
instanceKlass sun/invoke/util/BytecodeDescriptor
instanceKlass jdk/internal/org/objectweb/asm/ByteVector
instanceKlass jdk/internal/org/objectweb/asm/Symbol
instanceKlass jdk/internal/org/objectweb/asm/SymbolTable
instanceKlass jdk/internal/org/objectweb/asm/ClassVisitor
instanceKlass org/springframework/boot/loader/launch/Archive$Entry
instanceKlass java/util/function/Predicate
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$1
instanceKlass java/lang/invoke/InvokerBytecodeGenerator
instanceKlass java/lang/invoke/LambdaForm$Holder
instanceKlass java/lang/invoke/LambdaForm$Name
instanceKlass java/lang/invoke/Invokers
instanceKlass java/lang/invoke/MethodHandleImpl
instanceKlass sun/invoke/util/ValueConversions
instanceKlass java/lang/invoke/DirectMethodHandle$Holder
instanceKlass java/lang/invoke/LambdaForm$NamedFunction
instanceKlass sun/invoke/util/Wrapper$Format
instanceKlass java/lang/invoke/MethodTypeForm
instanceKlass java/lang/invoke/MethodType$ConcurrentWeakInternSet
instanceKlass java/lang/invoke/LambdaMetafactory
instanceKlass org/springframework/boot/loader/launch/Archive
instanceKlass org/springframework/boot/loader/launch/JarModeRunner
instanceKlass java/lang/Void
instanceKlass java/lang/PublicMethods$Key
instanceKlass java/lang/PublicMethods$MethodList
instanceKlass jdk/internal/misc/PreviewFeatures
instanceKlass jdk/internal/misc/MainMethodFinder
instanceKlass org/springframework/boot/loader/launch/Launcher
instanceKlass java/security/SecureClassLoader$DebugHolder
instanceKlass java/security/Permission
instanceKlass java/security/Guard
instanceKlass java/security/PermissionCollection
instanceKlass java/security/SecureClassLoader$1
instanceKlass java/util/zip/Checksum$1
instanceKlass java/util/zip/CRC32
instanceKlass java/util/zip/Checksum
instanceKlass sun/nio/ByteBuffered
instanceKlass java/lang/Package$VersionInfo
instanceKlass java/lang/NamedPackage
instanceKlass sun/security/util/ManifestEntryVerifier
instanceKlass java/util/ArrayList$Itr
instanceKlass jdk/internal/misc/ThreadTracker
instanceKlass java/util/jar/JarFile$ThreadTrackHolder
instanceKlass jdk/internal/loader/Resource
instanceKlass jdk/internal/loader/FileURLMapper
instanceKlass jdk/internal/loader/URLClassPath$JarLoader$1
instanceKlass jdk/internal/loader/URLClassPath$Loader
instanceKlass jdk/internal/loader/URLClassPath$3
instanceKlass java/security/PrivilegedExceptionAction
instanceKlass sun/net/util/URLUtil
instanceKlass java/io/RandomAccessFile$1
instanceKlass java/lang/reflect/Array
instanceKlass java/util/jar/Attributes$Name
instanceKlass java/util/SequencedMap
instanceKlass java/util/jar/Attributes
instanceKlass java/util/jar/JarVerifier
instanceKlass sun/security/action/GetIntegerAction
instanceKlass sun/security/util/Debug
instanceKlass sun/security/util/SignatureFileVerifier
instanceKlass java/util/zip/ZipFile$InflaterCleanupAction
instanceKlass java/util/zip/Inflater$InflaterZStreamRef
instanceKlass java/util/zip/Inflater
instanceKlass java/util/zip/ZipEntry
instanceKlass java/util/zip/ZipFile$2
instanceKlass java/nio/Bits$1
instanceKlass jdk/internal/misc/VM$BufferPool
instanceKlass java/nio/Bits
instanceKlass sun/nio/ch/DirectBuffer
instanceKlass jdk/internal/perf/PerfCounter$CoreCounters
instanceKlass jdk/internal/perf/Perf
instanceKlass jdk/internal/perf/Perf$GetPerfAction
instanceKlass jdk/internal/perf/PerfCounter
instanceKlass sun/util/locale/LocaleUtils
instanceKlass sun/util/locale/BaseLocale
instanceKlass java/util/Locale
instanceKlass java/nio/file/attribute/FileTime
instanceKlass java/util/zip/ZipUtils
instanceKlass java/util/zip/ZipFile$Source$End
instanceKlass java/io/RandomAccessFile$2
instanceKlass jdk/internal/access/JavaIORandomAccessFileAccess
instanceKlass java/io/RandomAccessFile
instanceKlass java/io/DataInput
instanceKlass java/io/DataOutput
instanceKlass sun/nio/fs/WindowsNativeDispatcher$CompletionStatus
instanceKlass sun/nio/fs/WindowsNativeDispatcher$AclInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$Account
instanceKlass sun/nio/fs/WindowsNativeDispatcher$DiskFreeSpace
instanceKlass sun/nio/fs/WindowsNativeDispatcher$VolumeInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstStream
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstFile
instanceKlass java/util/Enumeration
instanceKlass java/util/concurrent/ConcurrentHashMap$Traverser
instanceKlass jdk/internal/loader/NativeLibraries$3
instanceKlass jdk/internal/loader/NativeLibrary
instanceKlass java/util/ArrayDeque$DeqIterator
instanceKlass jdk/internal/loader/NativeLibraries$NativeLibraryContext$1
instanceKlass jdk/internal/loader/NativeLibraries$NativeLibraryContext
instanceKlass jdk/internal/loader/NativeLibraries$2
instanceKlass jdk/internal/loader/NativeLibraries$1
instanceKlass jdk/internal/loader/NativeLibraries$LibraryPaths
instanceKlass sun/nio/fs/WindowsNativeDispatcher
instanceKlass sun/nio/fs/NativeBuffer$Deallocator
instanceKlass sun/nio/fs/NativeBuffer
instanceKlass java/lang/ThreadLocal$ThreadLocalMap
instanceKlass java/lang/ThreadLocal
instanceKlass sun/nio/fs/NativeBuffers
instanceKlass sun/nio/fs/WindowsFileAttributes
instanceKlass java/nio/file/attribute/DosFileAttributes
instanceKlass sun/nio/fs/AbstractBasicFileAttributeView
instanceKlass sun/nio/fs/DynamicFileAttributeView
instanceKlass sun/nio/fs/WindowsFileAttributeViews
instanceKlass sun/nio/fs/Util
instanceKlass java/nio/file/attribute/BasicFileAttributeView
instanceKlass java/nio/file/attribute/FileAttributeView
instanceKlass java/nio/file/attribute/AttributeView
instanceKlass java/nio/file/Files
instanceKlass java/nio/file/CopyOption
instanceKlass java/nio/file/attribute/BasicFileAttributes
instanceKlass java/util/concurrent/ForkJoinPool$ManagedBlocker
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$Node
instanceKlass sun/nio/fs/WindowsPath
instanceKlass java/nio/file/Path
instanceKlass java/nio/file/Watchable
instanceKlass java/util/zip/ZipFile$Source$Key
instanceKlass sun/nio/fs/WindowsPathParser$Result
instanceKlass sun/nio/fs/WindowsPathParser
instanceKlass java/nio/file/FileSystem
instanceKlass java/nio/file/OpenOption
instanceKlass java/nio/file/spi/FileSystemProvider
instanceKlass sun/nio/fs/DefaultFileSystemProvider
instanceKlass java/util/zip/ZipFile$Source
instanceKlass java/lang/ref/Cleaner$Cleanable
instanceKlass jdk/internal/ref/CleanerImpl
instanceKlass java/lang/ref/Cleaner$1
instanceKlass java/lang/ref/Cleaner
instanceKlass jdk/internal/ref/CleanerFactory$1
instanceKlass java/util/concurrent/ThreadFactory
instanceKlass jdk/internal/ref/CleanerFactory
instanceKlass java/util/zip/ZipCoder
instanceKlass java/util/zip/ZipFile$CleanableResource
instanceKlass java/lang/Runtime$Version
instanceKlass java/util/jar/JavaUtilJarAccessImpl
instanceKlass jdk/internal/access/JavaUtilJarAccess
instanceKlass java/util/zip/ZipFile$1
instanceKlass jdk/internal/access/JavaUtilZipFileAccess
instanceKlass java/util/zip/ZipFile
instanceKlass java/util/zip/ZipConstants
instanceKlass java/lang/StringCoding
instanceKlass sun/nio/cs/ArrayDecoder
instanceKlass java/nio/charset/CharsetDecoder
instanceKlass sun/launcher/LauncherHelper
instanceKlass jdk/internal/vm/PostVMInitHook$1
instanceKlass jdk/internal/util/EnvUtils
instanceKlass jdk/internal/vm/PostVMInitHook$2
instanceKlass jdk/internal/vm/PostVMInitHook
instanceKlass java/lang/invoke/StringConcatFactory
instanceKlass java/lang/ModuleLayer$Controller
instanceKlass java/util/concurrent/CopyOnWriteArrayList
instanceKlass jdk/internal/module/ServicesCatalog$ServiceProvider
instanceKlass jdk/internal/loader/AbstractClassLoaderValue$Memoizer
instanceKlass java/util/ImmutableCollections$ListItr
instanceKlass java/util/ListIterator
instanceKlass java/lang/ModuleLayer
instanceKlass jdk/internal/module/ModuleLoaderMap$Modules
instanceKlass jdk/internal/module/ModuleLoaderMap$Mapper
instanceKlass jdk/internal/module/ModuleLoaderMap
instanceKlass java/util/AbstractMap$1$1
instanceKlass java/lang/module/ResolvedModule
instanceKlass java/lang/module/Configuration
instanceKlass jdk/internal/loader/BuiltinClassLoader$LoadedModule
instanceKlass jdk/internal/loader/AbstractClassLoaderValue
instanceKlass jdk/internal/module/ServicesCatalog
instanceKlass java/util/Deque
instanceKlass java/util/Queue
instanceKlass sun/net/util/IPAddressUtil$MASKS
instanceKlass sun/net/util/IPAddressUtil
instanceKlass java/net/URLStreamHandler
instanceKlass sun/net/www/ParseUtil
instanceKlass java/net/URL$3
instanceKlass jdk/internal/access/JavaNetURLAccess
instanceKlass java/net/URL$DefaultFactory
instanceKlass java/net/URLStreamHandlerFactory
instanceKlass jdk/internal/loader/URLClassPath
instanceKlass java/security/Principal
instanceKlass java/security/ProtectionDomain$Key
instanceKlass java/security/ProtectionDomain$JavaSecurityAccessImpl
instanceKlass jdk/internal/access/JavaSecurityAccess
instanceKlass java/lang/ClassLoader$ParallelLoaders
instanceKlass java/security/cert/Certificate
instanceKlass jdk/internal/loader/ArchivedClassLoaders
instanceKlass java/util/concurrent/ConcurrentHashMap$CollectionView
instanceKlass jdk/internal/loader/ClassLoaderHelper
instanceKlass jdk/internal/loader/NativeLibraries
instanceKlass java/lang/Module$EnableNativeAccess
instanceKlass jdk/internal/loader/BootLoader
instanceKlass java/util/Optional
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleFinder
instanceKlass java/lang/module/ModuleFinder
instanceKlass jdk/internal/module/SystemModuleFinders$3
instanceKlass jdk/internal/module/ModuleHashes$HashSupplier
instanceKlass jdk/internal/module/SystemModuleFinders$2
instanceKlass java/util/function/Supplier
instanceKlass java/lang/module/ModuleReference
instanceKlass jdk/internal/module/ModuleResolution
instanceKlass java/util/Collections$UnmodifiableMap
instanceKlass jdk/internal/module/ModuleHashes$Builder
instanceKlass jdk/internal/module/ModuleHashes
instanceKlass jdk/internal/module/ModuleTarget
instanceKlass java/util/ImmutableCollections$Set12$1
instanceKlass java/lang/reflect/AccessFlag$18
instanceKlass java/lang/reflect/AccessFlag$17
instanceKlass java/lang/reflect/AccessFlag$16
instanceKlass java/lang/reflect/AccessFlag$15
instanceKlass java/lang/reflect/AccessFlag$14
instanceKlass java/lang/reflect/AccessFlag$13
instanceKlass java/lang/reflect/AccessFlag$12
instanceKlass java/lang/reflect/AccessFlag$11
instanceKlass java/lang/reflect/AccessFlag$10
instanceKlass java/lang/reflect/AccessFlag$9
instanceKlass java/lang/reflect/AccessFlag$8
instanceKlass java/lang/reflect/AccessFlag$7
instanceKlass java/lang/reflect/AccessFlag$6
instanceKlass java/lang/reflect/AccessFlag$5
instanceKlass java/lang/reflect/AccessFlag$4
instanceKlass java/lang/reflect/AccessFlag$3
instanceKlass java/lang/reflect/AccessFlag$2
instanceKlass java/lang/reflect/AccessFlag$1
instanceKlass java/lang/module/ModuleDescriptor$Version
instanceKlass java/lang/module/ModuleDescriptor$Provides
instanceKlass java/lang/module/ModuleDescriptor$Opens
instanceKlass java/util/ImmutableCollections$SetN$SetNIterator
instanceKlass java/lang/module/ModuleDescriptor$Exports
instanceKlass java/lang/module/ModuleDescriptor$Requires
instanceKlass jdk/internal/module/Builder
instanceKlass jdk/internal/module/SystemModules$default
instanceKlass jdk/internal/module/SystemModules
instanceKlass jdk/internal/module/SystemModulesMap
instanceKlass java/net/URI$1
instanceKlass jdk/internal/access/JavaNetUriAccess
instanceKlass java/net/URI
instanceKlass jdk/internal/module/SystemModuleFinders
instanceKlass jdk/internal/module/ArchivedModuleGraph
instanceKlass jdk/internal/module/ArchivedBootLayer
instanceKlass jdk/internal/module/ModuleBootstrap$Counters
instanceKlass jdk/internal/module/ModulePatcher
instanceKlass java/io/FileSystem
instanceKlass java/io/DefaultFileSystem
instanceKlass java/io/File
instanceKlass java/lang/module/ModuleDescriptor$1
instanceKlass jdk/internal/access/JavaLangModuleAccess
instanceKlass sun/invoke/util/VerifyAccess
instanceKlass java/util/KeyValueHolder
instanceKlass java/util/ImmutableCollections$MapN$MapNIterator
instanceKlass java/lang/StrictMath
instanceKlass java/lang/invoke/MethodHandles$Lookup
instanceKlass java/lang/invoke/MemberName$Factory
instanceKlass java/lang/invoke/MethodHandles
instanceKlass java/lang/module/ModuleDescriptor
instanceKlass jdk/internal/module/ModuleBootstrap
instanceKlass java/lang/Character$CharacterCache
instanceKlass java/util/HexFormat
instanceKlass jdk/internal/util/ClassFileDumper
instanceKlass sun/security/action/GetPropertyAction
instanceKlass java/lang/invoke/MethodHandleStatics
instanceKlass jdk/internal/misc/Blocker
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject
instanceKlass java/util/concurrent/locks/Condition
instanceKlass java/util/Collections
instanceKlass java/lang/Thread$ThreadIdentifiers
instanceKlass sun/io/Win32ErrorMode
instanceKlass jdk/internal/misc/OSEnvironment
instanceKlass java/lang/Integer$IntegerCache
instanceKlass jdk/internal/misc/Signal$NativeHandler
instanceKlass java/util/Hashtable$Entry
instanceKlass jdk/internal/misc/Signal
instanceKlass java/lang/Terminator$1
instanceKlass jdk/internal/misc/Signal$Handler
instanceKlass java/lang/Terminator
instanceKlass java/nio/ByteOrder
instanceKlass java/nio/Buffer$2
instanceKlass jdk/internal/access/JavaNioAccess
instanceKlass java/nio/Buffer$1
instanceKlass jdk/internal/misc/ScopedMemoryAccess
instanceKlass java/nio/charset/CodingErrorAction
instanceKlass sun/nio/cs/SingleByte
instanceKlass java/lang/StringUTF16
instanceKlass sun/nio/cs/MS1252$Holder
instanceKlass sun/nio/cs/ArrayEncoder
instanceKlass java/nio/charset/CharsetEncoder
instanceKlass java/io/Writer
instanceKlass java/io/PrintStream$1
instanceKlass jdk/internal/access/JavaIOPrintStreamAccess
instanceKlass jdk/internal/misc/InternalLock
instanceKlass java/io/OutputStream
instanceKlass java/io/Flushable
instanceKlass java/io/FileDescriptor$1
instanceKlass jdk/internal/access/JavaIOFileDescriptorAccess
instanceKlass java/io/FileDescriptor
instanceKlass jdk/internal/util/StaticProperty
instanceKlass jdk/internal/reflect/MethodHandleAccessorFactory
instanceKlass java/lang/reflect/Modifier
instanceKlass java/lang/Class$1
instanceKlass java/lang/Class$Atomic
instanceKlass java/lang/Class$ReflectionData
instanceKlass java/nio/charset/StandardCharsets
instanceKlass sun/nio/cs/HistoricallyNamedCharset
instanceKlass java/nio/charset/spi/CharsetProvider
instanceKlass java/nio/charset/Charset
instanceKlass java/util/HashMap$HashIterator
instanceKlass java/util/concurrent/locks/LockSupport
instanceKlass java/util/concurrent/ConcurrentHashMap$Node
instanceKlass java/util/concurrent/ConcurrentHashMap$CounterCell
instanceKlass java/util/concurrent/locks/ReentrantLock
instanceKlass java/util/concurrent/locks/Lock
instanceKlass java/lang/CharacterData
instanceKlass java/util/Arrays
instanceKlass jdk/internal/util/Preconditions$3
instanceKlass jdk/internal/util/Preconditions$2
instanceKlass jdk/internal/util/Preconditions$4
instanceKlass java/util/function/BiFunction
instanceKlass jdk/internal/util/Preconditions$1
instanceKlass java/util/function/Function
instanceKlass jdk/internal/util/Preconditions
instanceKlass java/lang/Runtime
instanceKlass java/lang/VersionProps
instanceKlass java/lang/StringConcatHelper
instanceKlass java/util/HashMap$Node
instanceKlass java/util/Map$Entry
instanceKlass jdk/internal/util/ArraysSupport
instanceKlass jdk/internal/util/SystemProps$Raw
instanceKlass jdk/internal/util/SystemProps
instanceKlass java/lang/System$2
instanceKlass jdk/internal/access/JavaLangAccess
instanceKlass java/lang/ref/NativeReferenceQueue$Lock
instanceKlass java/lang/ref/ReferenceQueue
instanceKlass java/lang/ref/Reference$1
instanceKlass jdk/internal/access/JavaLangRefAccess
instanceKlass jdk/internal/reflect/ReflectionFactory
instanceKlass java/lang/Math
instanceKlass java/lang/StringLatin1
instanceKlass jdk/internal/reflect/Reflection
instanceKlass jdk/internal/reflect/ReflectionFactory$GetReflectionFactoryAction
instanceKlass java/security/PrivilegedAction
instanceKlass jdk/internal/access/SharedSecrets
instanceKlass java/lang/reflect/ReflectAccess
instanceKlass jdk/internal/access/JavaLangReflectAccess
instanceKlass java/util/ImmutableCollections
instanceKlass java/util/Objects
instanceKlass java/util/Set
instanceKlass jdk/internal/misc/CDS
instanceKlass java/lang/Module$ArchivedData
instanceKlass jdk/internal/misc/VM
instanceKlass java/lang/String$CaseInsensitiveComparator
instanceKlass java/util/Comparator
instanceKlass java/io/ObjectStreamField
instanceKlass jdk/internal/vm/FillerObject
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload
instanceKlass jdk/internal/vm/vector/VectorSupport
instanceKlass java/lang/reflect/RecordComponent
instanceKlass java/util/Iterator
instanceKlass java/lang/Number
instanceKlass java/lang/Character
instanceKlass java/lang/Boolean
instanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer
instanceKlass java/lang/LiveStackFrame
instanceKlass java/lang/StackFrameInfo
instanceKlass java/lang/StackWalker$StackFrame
instanceKlass java/lang/StackStreamFactory$AbstractStackWalker
instanceKlass java/lang/StackWalker
instanceKlass java/nio/Buffer
instanceKlass java/lang/StackTraceElement
instanceKlass java/util/RandomAccess
instanceKlass java/util/List
instanceKlass java/util/SequencedCollection
instanceKlass java/util/AbstractCollection
instanceKlass java/util/Collection
instanceKlass java/lang/Iterable
instanceKlass java/util/concurrent/ConcurrentMap
instanceKlass java/util/AbstractMap
instanceKlass java/security/CodeSource
instanceKlass jdk/internal/loader/ClassLoaders
instanceKlass java/util/jar/Manifest
instanceKlass java/lang/Enum
instanceKlass java/net/URL
instanceKlass java/io/InputStream
instanceKlass java/io/Closeable
instanceKlass java/lang/AutoCloseable
instanceKlass jdk/internal/module/Modules
instanceKlass jdk/internal/misc/Unsafe
instanceKlass jdk/internal/misc/UnsafeConstants
instanceKlass java/lang/AbstractStringBuilder
instanceKlass java/lang/Appendable
instanceKlass java/lang/AssertionStatusDirectives
instanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext
instanceKlass jdk/internal/foreign/abi/ABIDescriptor
instanceKlass jdk/internal/foreign/abi/NativeEntryPoint
instanceKlass java/lang/invoke/CallSite
instanceKlass java/lang/invoke/MethodType
instanceKlass java/lang/invoke/TypeDescriptor$OfMethod
instanceKlass java/lang/invoke/LambdaForm
instanceKlass java/lang/invoke/MethodHandleNatives
instanceKlass java/lang/invoke/ResolvedMethodName
instanceKlass java/lang/invoke/MemberName
instanceKlass java/lang/invoke/VarHandle
instanceKlass java/lang/invoke/MethodHandle
instanceKlass jdk/internal/reflect/CallerSensitive
instanceKlass java/lang/annotation/Annotation
instanceKlass jdk/internal/reflect/FieldAccessor
instanceKlass jdk/internal/reflect/ConstantPool
instanceKlass jdk/internal/reflect/ConstructorAccessor
instanceKlass jdk/internal/reflect/MethodAccessor
instanceKlass jdk/internal/reflect/MagicAccessorImpl
instanceKlass jdk/internal/vm/StackChunk
instanceKlass jdk/internal/vm/Continuation
instanceKlass jdk/internal/vm/ContinuationScope
instanceKlass java/lang/reflect/Parameter
instanceKlass java/lang/reflect/Member
instanceKlass java/lang/reflect/AccessibleObject
instanceKlass java/lang/Module
instanceKlass java/util/Map
instanceKlass java/util/Dictionary
instanceKlass java/lang/ThreadGroup
instanceKlass java/lang/Thread$UncaughtExceptionHandler
instanceKlass java/lang/Thread$Constants
instanceKlass java/lang/Thread$FieldHolder
instanceKlass java/lang/Thread
instanceKlass java/lang/Runnable
instanceKlass java/lang/ref/Reference
instanceKlass java/lang/Record
instanceKlass java/security/AccessController
instanceKlass java/security/AccessControlContext
instanceKlass java/security/ProtectionDomain
instanceKlass java/lang/SecurityManager
instanceKlass java/lang/Throwable
instanceKlass java/lang/System
instanceKlass java/lang/ClassLoader
instanceKlass java/lang/Cloneable
instanceKlass java/lang/Class
instanceKlass java/lang/invoke/TypeDescriptor$OfField
instanceKlass java/lang/invoke/TypeDescriptor
instanceKlass java/lang/reflect/Type
instanceKlass java/lang/reflect/GenericDeclaration
instanceKlass java/lang/reflect/AnnotatedElement
instanceKlass java/lang/String
instanceKlass java/lang/constant/ConstantDesc
instanceKlass java/lang/constant/Constable
instanceKlass java/lang/CharSequence
instanceKlass java/lang/Comparable
instanceKlass java/io/Serializable
ciInstanceKlass java/lang/Object 1 1 124 7 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 3 8 1 7 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 3 1 1
ciInstanceKlass java/io/Serializable 1 0 7 100 1 100 1 1 1
ciInstanceKlass java/lang/System 1 1 834 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 100 12 1 1 1 18 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 8 1 10 12 1 7 1 10 12 1 8 1 10 12 1 10 12 1 1 100 1 10 12 10 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 100 1 100 1 8 1 10 12 1 10 12 1 1 7 1 10 12 1 100 1 8 1 10 10 12 1 100 1 8 1 10 8 1 10 7 12 1 1 8 1 10 12 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 100 1 18 12 1 100 1 9 100 12 1 1 1 10 12 1 100 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 10 12 1 9 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 7 12 1 1 1 7 1 8 1 10 9 12 1 9 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 8 1 11 12 1 10 12 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 1 7 1 11 12 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 11 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 8 1 9 12 1 8 1 10 7 12 1 1 8 1 7 1 9 7 12 1 1 1 10 12 1 7 1 9 12 10 9 12 7 1 10 12 9 12 1 1 8 1 10 12 1 1 8 1 10 7 12 1 1 10 12 1 10 12 1 1 11 7 12 1 1 10 12 10 7 12 1 1 1 9 12 1 1 7 1 8 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 8 1 8 1 10 8 1 8 1 8 1 8 1 10 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 7 1 8 1 10 10 10 12 1 1 10 12 1 1 8 1 10 12 1 8 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 7 1 10 10 12 1 10 12 1 9 12 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 7 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 1 1 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/System in Ljava/io/InputStream; java/io/BufferedInputStream
staticfield java/lang/System out Ljava/io/PrintStream; java/io/PrintStream
staticfield java/lang/System err Ljava/io/PrintStream; java/io/PrintStream
ciInstanceKlass java/lang/SecurityManager 0 0 576 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 1 10 100 1 10 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 100 1 8 1 10 9 12 1 1 9 12 1 8 1 9 12 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 10 12 1 1 100 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 7 12 1 1 1 10 12 1 1 8 1 100 1 8 1 10 8 1 8 1 8 1 8 1 8 1 10 100 12 1 1 8 1 100 1 8 1 8 1 10 8 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 100 12 1 1 11 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 18 12 1 1 11 12 1 1 18 18 11 12 1 18 12 1 11 12 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 100 1 10 100 12 1 1 10 12 1 10 12 1 18 12 1 18 10 100 12 1 1 1 18 12 1 10 12 1 18 18 8 1 10 12 1 9 12 1 1 11 7 12 1 1 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 8 1 100 1 10 9 12 1 8 1 10 12 1 8 1 100 1 10 10 100 12 1 1 10 100 1 9 7 12 1 1 1 11 12 1 1 10 12 1 11 12 1 10 12 1 7 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 7 12 1 1 1 16 1 16 15 10 12 16 1 15 10 12 16 15 11 7 1 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 1 16 1 15 11 12 1 15 10 12 16 15 10 16 1 15 10 100 12 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/security/AccessController 1 1 295 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 7 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 1 10 11 7 12 1 1 1 10 7 12 1 1 11 7 1 7 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 8 1 10 100 12 1 1 1 8 1 7 1 10 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 3 1 1 1
staticfield java/security/AccessController $assertionsDisabled Z 1
ciInstanceKlass java/security/ProtectionDomain 1 1 348 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 7 1 9 12 1 9 12 1 1 7 1 9 12 1 1 9 12 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 9 100 12 1 1 10 12 1 1 10 100 1 10 12 1 1 8 1 7 1 8 1 10 12 1 10 11 10 7 12 1 1 1 10 12 1 1 8 1 11 8 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 8 1 10 7 12 1 1 1 9 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 100 1 18 12 1 1 10 7 12 1 1 1 10 7 1 10 12 1 10 12 1 1 11 100 12 1 1 11 12 1 100 1 11 7 12 1 1 1 10 12 1 10 11 12 1 1 11 12 1 1 10 12 1 10 7 12 1 1 10 100 12 1 1 11 12 1 10 12 10 12 1 8 1 8 1 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 7 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 100 1 1 16 15 10 12 16 15 10 100 12 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/security/ProtectionDomain filePermCompatInPD Z 0
ciInstanceKlass java/security/CodeSource 1 1 398 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 100 12 1 1 10 100 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 7 1 10 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 8 1 8 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 8 1 10 12 1 8 1 8 1 8 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 100 1 10 12 1 10 12 10 12 1 1 10 100 12 1 1 10 12 1 7 1 10 12 10 100 12 1 1 1 10 8 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 7 1 8 1 8 1 10 10 12 1 1 10 100 12 1 1 1 7 1 10 12 10 12 1 1 11 7 12 1 1 10 10 12 1 11 10 12 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 11 12 1 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Boolean 1 1 152 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 8 1 10 7 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 9 100 12 1 1 9 12 10 100 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1
staticfield java/lang/Boolean TRUE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean FALSE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Comparable 1 0 12 100 1 100 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/constant/Constable 1 0 11 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/util/Map 1 1 263 11 7 12 1 1 1 11 12 1 1 10 7 12 1 1 11 12 1 1 11 7 12 1 1 1 11 100 12 1 1 1 11 12 1 1 7 1 11 12 1 11 12 1 100 1 100 1 10 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 11 12 1 10 12 1 1 11 12 1 11 7 12 1 9 7 12 1 1 1 100 1 10 12 7 1 7 1 10 12 1 7 1 10 7 1 11 12 1 11 12 1 1 11 12 1 1 7 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Class 1 1 1698 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 7 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 8 1 8 1 8 1 10 100 12 1 1 1 11 12 1 1 8 1 10 12 1 10 11 100 12 1 1 1 11 7 12 1 1 1 11 8 1 18 8 1 10 12 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 10 7 12 1 10 12 1 1 10 7 1 7 1 10 12 1 1 9 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 7 1 7 1 10 10 12 1 1 10 12 1 1 100 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 10 7 1 10 12 1 10 12 1 10 12 1 1 10 9 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 1 10 7 12 1 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 9 100 12 1 1 1 9 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 1 10 10 10 12 1 1 10 12 1 1 10 12 10 10 12 1 1 7 1 8 1 10 10 12 1 1 10 12 1 100 1 11 12 1 10 100 12 1 1 10 12 1 10 12 1 10 100 12 1 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 7 1 9 12 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 11 7 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 10 12 1 1 7 1 10 10 12 1 1 10 7 12 1 1 1 100 1 7 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 11 7 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 12 1 9 12 1 1 100 1 10 9 12 1 1 10 12 7 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 10 12 1 1 100 1 10 8 1 10 12 1 11 11 12 1 1 11 7 12 1 1 11 12 1 8 1 10 12 1 10 12 1 1 9 12 1 9 12 1 1 10 7 12 1 1 9 12 1 10 12 1 1 10 10 12 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 9 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 100 1 10 10 12 1 1 7 1 10 12 1 1 100 11 7 1 9 12 1 1 9 12 1 7 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 7 1 10 10 12 1 1 10 10 12 1 10 12 10 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 8 10 7 8 1 18 8 1 8 1 10 12 1 9 12 1 9 12 1 1 10 12 1 7 1 7 1 10 12 1 9 12 1 1 7 1 10 10 12 1 10 7 1 9 12 1 8 1 10 12 1 7 1 10 12 1 10 12 1 1 100 1 7 1 9 12 1 100 1 8 1 10 10 7 12 1 1 1 10 12 11 7 12 1 1 1 10 12 1 10 12 1 1 10 8 1 8 1 10 12 1 1 9 7 12 1 1 11 12 7 1 11 7 12 1 1 9 12 1 10 100 12 1 1 1 10 7 12 1 1 10 12 1 1 9 12 1 9 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 12 1 7 1 11 12 1 10 7 12 1 1 1 10 12 1 11 12 1 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 11 12 1 11 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 100 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 100 1 10 12 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 18 12 1 1 11 12 1 1 18 11 12 1 18 12 1 11 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 8 1 10 12 1 7 1 9 12 1 1 7 1 7 1 7 1 7 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 15 11 12 16 1 16 15 16 15 10 12 16 16 15 10 12 16 15 16 1 15 10 12 16 15 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 100 1 100 1 1 100 1 100 1 1 100 1 100 1 1
staticfield java/lang/Class EMPTY_CLASS_ARRAY [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/Class serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/lang/reflect/AnnotatedElement 1 1 164 11 7 12 1 1 1 11 12 1 1 7 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 11 12 1 1 11 7 12 1 1 10 7 12 1 1 1 10 12 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 18 12 1 18 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 7 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 16 15 16 1 16 1 15 11 12 16 16 1 15 10 100 12 1 1 1 16 1 15 10 100 12 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/invoke/TypeDescriptor 1 0 17 100 1 100 1 1 1 1 1 1 100 1 100 1 1 1 1
ciInstanceKlass java/lang/reflect/GenericDeclaration 1 0 30 7 1 7 1 7 1 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1
ciInstanceKlass java/lang/reflect/Type 1 1 17 11 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/TypeDescriptor$OfField 1 0 21 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StringBuilder 1 1 422 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 100 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 100 1 100 1 8 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 7 1 7 1 7 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/StringBuilder
instanceKlass java/lang/StringBuffer
ciInstanceKlass java/lang/AbstractStringBuilder 1 1 605 7 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 3 3 10 12 1 10 12 1 1 11 7 1 100 1 7 1 10 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 8 1 10 10 12 1 1 100 1 10 12 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 100 1 10 10 7 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 10 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 18 12 1 1 100 1 10 100 12 1 1 1 18 10 12 1 1 10 12 1 10 12 1 1 11 12 1 1 10 12 1 10 12 10 12 1 10 10 10 12 1 10 5 0 10 10 12 1 1 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 100 1 10 12 100 1 10 100 1 10 7 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 7 1 1 16 1 15 10 12 16 15 10 12 15 10 100 12 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/AbstractStringBuilder EMPTYVALUE [B 0
ciInstanceKlass java/lang/Appendable 1 0 14 100 1 100 1 1 1 1 100 1 1 1 1 1
ciInstanceKlass java/lang/CharSequence 1 1 131 11 7 12 1 1 1 18 12 1 1 100 1 10 100 12 1 1 1 18 10 100 12 1 1 1 11 12 1 1 11 7 1 11 12 1 1 10 100 12 1 1 1 11 12 1 1 100 1 10 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 1 15 11 12 16 15 11 12 15 10 100 12 1 1 1 1 1 100 1 100 1 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/AutoCloseable 1 0 12 100 1 100 1 1 1 1 100 1 1 1
ciInstanceKlass java/io/Closeable 1 0 14 100 1 100 1 100 1 1 1 1 100 1 1 1
instanceKlass java/util/concurrent/TimeoutException
instanceKlass java/util/zip/DataFormatException
instanceKlass nonapi/io/github/classgraph/concurrency/SingletonMap$NullSingletonException
instanceKlass nonapi/io/github/classgraph/concurrency/SingletonMap$NewInstanceException
instanceKlass java/security/PrivilegedActionException
instanceKlass java/util/concurrent/ExecutionException
instanceKlass java/security/GeneralSecurityException
instanceKlass sun/nio/fs/WindowsException
instanceKlass javax/xml/transform/TransformerException
instanceKlass javax/xml/stream/XMLStreamException
instanceKlass java/text/ParseException
instanceKlass javax/naming/NamingException
instanceKlass java/net/URISyntaxException
instanceKlass java/lang/CloneNotSupportedException
instanceKlass java/lang/InterruptedException
instanceKlass java/io/IOException
instanceKlass java/lang/ReflectiveOperationException
instanceKlass java/lang/RuntimeException
ciInstanceKlass java/lang/Exception 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/Exception
instanceKlass java/lang/Error
ciInstanceKlass java/lang/Throwable 1 1 404 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 8 1 10 100 12 1 1 10 10 12 1 100 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 8 1 9 7 12 1 1 1 10 12 1 1 100 1 10 12 10 12 1 10 100 12 1 1 1 100 1 10 12 10 12 1 10 12 1 100 1 10 10 7 12 1 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 8 1 8 1 9 12 1 1 10 12 1 1 100 1 10 11 12 1 8 1 8 1 10 7 12 1 1 8 1 10 12 1 8 1 100 1 10 12 1 9 12 1 1 10 12 1 10 100 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 10 12 1 1 7 1 10 100 12 1 1 1 10 12 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 8 1 10 12 1 1 8 1 10 10 9 100 12 1 1 1 8 1 10 12 1 1 11 10 100 1 8 1 10 11 12 1 1 8 1 9 12 1 10 100 12 1 1 11 9 12 1 1 11 12 1 1 100 10 12 1 10 12 1 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Throwable UNASSIGNED_STACK [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
staticfield java/lang/Throwable SUPPRESSED_SENTINEL Ljava/util/List; java/util/Collections$EmptyList
staticfield java/lang/Throwable EMPTY_THROWABLE_ARRAY [Ljava/lang/Throwable; 0 [Ljava/lang/Throwable;
staticfield java/lang/Throwable $assertionsDisabled Z 1
ciMethod java/lang/Throwable getMessage ()Ljava/lang/String; 778 0 389 0 -1
instanceKlass java/security/Provider
ciInstanceKlass java/util/Properties 1 1 690 10 7 12 1 1 1 100 1 10 7 12 1 1 7 1 10 12 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 8 1 10 12 1 7 1 10 12 10 12 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 3 10 10 100 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 10 12 1 1 100 1 9 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 9 12 1 1 7 1 7 1 10 12 1 7 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 11 12 1 10 12 1 1 8 1 10 12 1 10 100 12 1 1 10 12 1 100 1 10 10 12 1 10 12 1 100 1 10 10 12 1 1 10 7 12 1 1 9 100 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 100 1 10 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 1 7 1 10 10 12 1 11 7 12 1 1 10 7 12 1 1 1 8 1 10 100 12 1 1 11 11 7 1 8 1 10 100 1 11 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 10 11 12 1 4 11 10 12 1 1 10 100 12 1 1 11 12 1 10 12 1 1 10 100 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 100 12 1 1 1 100 1 6 0 10 12 1 1 11 100 12 1 1 1 10 12 1 10 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 100 1 1
staticfield java/util/Properties UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
instanceKlass java/util/Hashtable
ciInstanceKlass java/util/Dictionary 1 1 36 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/Properties
ciInstanceKlass java/util/Hashtable 1 1 516 7 1 10 7 12 1 1 1 9 7 12 1 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 8 1 10 12 1 9 12 1 1 7 1 9 12 1 1 4 10 7 12 1 1 1 9 12 1 4 10 12 1 11 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 1 100 1 10 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 10 12 1 3 9 12 1 9 12 1 3 10 12 1 10 12 1 10 12 1 1 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 1 100 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 9 12 1 1 10 100 1 7 1 10 12 1 10 8 1 10 10 12 1 8 1 10 8 1 10 100 12 1 1 1 7 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 100 1 10 100 1 10 10 12 1 1 11 12 1 1 11 12 1 7 1 10 10 10 100 12 1 1 11 100 12 1 1 1 100 1 10 11 100 12 1 1 11 100 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 100 1 8 1 10 4 4 10 12 1 1 10 12 1 8 1 4 10 12 10 100 12 1 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 7 1 7 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/String 1 1 1443 10 7 12 1 1 1 8 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 10 7 12 1 1 1 10 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 10 12 9 7 12 1 1 10 12 1 1 3 10 12 1 1 7 1 11 12 1 1 11 12 1 11 12 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 11 12 1 1 10 12 1 1 10 12 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 100 1 100 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 100 1 11 10 7 12 1 1 11 12 1 11 12 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 3 3 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 7 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 10 12 1 100 1 10 10 12 1 1 10 12 1 1 10 7 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 11 7 1 11 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 1 10 12 10 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 10 10 12 1 10 12 10 10 12 10 10 12 1 10 12 1 10 10 12 10 7 12 1 1 1 10 12 10 10 12 10 12 1 10 12 10 12 10 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 10 7 12 1 1 1 11 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 7 1 8 1 10 10 10 12 1 10 12 1 1 8 1 10 12 1 3 3 10 12 1 10 12 1 1 10 12 7 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 100 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 10 12 10 12 1 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 12 1 1 10 10 12 1 8 1 10 12 1 1 18 12 1 1 11 100 12 1 1 1 7 1 3 18 12 1 18 12 1 8 1 10 100 12 1 1 1 11 12 1 1 10 12 10 10 12 1 10 11 12 1 1 10 12 1 1 11 12 1 18 3 11 10 12 1 11 11 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 11 100 12 1 7 1 100 1 10 12 7 1 10 10 7 12 1 1 1 100 1 10 7 1 10 10 12 1 10 10 12 1 8 1 10 10 12 1 8 1 8 1 10 12 1 10 12 1 10 10 12 10 7 12 1 1 10 7 12 1 1 10 7 12 1 1 8 1 10 12 1 10 12 1 10 9 12 1 10 12 9 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 100 12 1 1 1 10 12 10 12 1 1 10 12 10 10 12 10 12 7 1 9 12 1 1 7 1 10 7 1 7 1 7 1 7 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 15 10 12 15 10 12 15 10 12 15 10 100 12 1 1 1 1 1 1 1 100 1 100 1 1 1
staticfield java/lang/String COMPACT_STRINGS Z 1
staticfield java/lang/String serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/String CASE_INSENSITIVE_ORDER Ljava/util/Comparator; java/lang/String$CaseInsensitiveComparator
ciInstanceKlass java/lang/constant/ConstantDesc 1 0 37 100 1 100 1 1 1 1 100 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/InternalError 0 0 34 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/io/IOError
instanceKlass java/util/ServiceConfigurationError
instanceKlass java/lang/AssertionError
instanceKlass java/lang/VirtualMachineError
instanceKlass java/lang/LinkageError
ciInstanceKlass java/lang/Error 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/StackOverflowError
instanceKlass java/lang/OutOfMemoryError
instanceKlass java/lang/InternalError
ciInstanceKlass java/lang/VirtualMachineError 1 1 34 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Set 1 1 144 100 1 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 12 1 1 10 12 1 7 1 7 1 10 12 1 7 1 7 1 11 7 12 1 1 1 11 12 1 1 7 1 10 12 1 10 12 1 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Iterator 1 1 53 100 1 8 1 10 12 1 1 10 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass nonapi/io/github/classgraph/fileslice/Slice$1
instanceKlass nonapi/io/github/classgraph/fastzipfilereader/NestedJarHandler$7
instanceKlass org/springframework/boot/loader/zip/DataBlockInputStream
instanceKlass org/springframework/boot/loader/jar/NestedJarFile$JarEntryInputStream
instanceKlass org/springframework/boot/loader/net/protocol/jar/LazyDelegatingInputStream
instanceKlass java/util/zip/ZipFile$ZipFileInputStream
instanceKlass java/io/FilterInputStream
instanceKlass java/io/FileInputStream
instanceKlass java/io/ByteArrayInputStream
ciInstanceKlass java/io/InputStream 1 1 195 7 1 10 7 12 1 1 1 100 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 100 1 3 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 1 3 7 1 8 1 10 10 100 12 1 1 1 7 1 10 11 7 12 1 1 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 100 1 10 7 12 1 1 1 5 0 10 12 1 10 12 1 1 100 1 10 8 1 10 8 1 8 1 10 12 1 1 10 100 12 1 1 1 7 1 5 0 10 12 1 100 1 7 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/eclipse/lsp4j/jsonrpc/util/LimitedInputStream
instanceKlass java/io/DataInputStream
instanceKlass org/springframework/boot/loader/jar/NestedJarFile$RawZipDataInputStream
instanceKlass org/springframework/boot/loader/net/protocol/nested/NestedUrlConnection$ConnectionInputStream
instanceKlass java/util/jar/Manifest$FastInputStream
instanceKlass java/util/zip/InflaterInputStream
instanceKlass java/io/BufferedInputStream
ciInstanceKlass java/io/FilterInputStream 1 1 62 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/misc/Unsafe 1 1 1287 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 10 10 12 1 1 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 5 0 5 0 5 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 7 1 8 1 10 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 9 12 1 100 1 10 10 12 1 1 8 1 10 8 1 8 1 10 12 1 1 9 7 12 1 1 1 9 7 1 9 7 1 9 7 1 9 9 7 1 9 7 1 9 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 5 0 5 0 9 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 3 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 100 1 10 9 12 1 5 0 10 12 1 1 5 0 10 12 1 5 0 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 5 0 5 0 5 0 10 12 1 1 10 12 1 10 12 1 10 12 10 7 12 1 1 8 1 7 1 11 12 1 1 8 1 11 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 1 10 12 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 10 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/Unsafe theUnsafe Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_INT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_INT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ADDRESS_SIZE I 8
instanceKlass io/github/classgraph/ClassGraphClassLoader
instanceKlass jdk/internal/reflect/DelegatingClassLoader
instanceKlass java/security/SecureClassLoader
ciInstanceKlass java/lang/ClassLoader 1 1 1108 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 7 12 1 10 7 1 10 7 1 7 1 7 1 10 12 1 10 12 1 9 12 1 1 10 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 10 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 7 1 7 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 10 12 1 1 10 12 1 1 7 1 8 1 10 8 1 10 12 1 10 12 1 100 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 12 1 10 12 1 1 8 1 8 1 10 7 12 1 1 100 1 10 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 10 7 1 7 1 10 12 1 1 10 12 1 10 7 1 10 12 1 100 1 18 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 10 12 1 10 12 1 100 1 10 12 1 8 1 10 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 8 1 100 1 10 10 12 1 9 12 1 10 7 12 1 1 10 12 1 7 1 8 1 10 12 1 10 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 7 1 100 1 10 12 1 1 7 1 7 1 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 7 1 18 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 18 12 1 11 100 12 1 1 1 100 1 10 12 1 1 10 12 1 10 11 12 1 1 10 18 10 12 1 1 11 7 12 1 18 12 1 11 12 1 1 10 12 10 12 1 1 10 12 1 1 100 1 8 1 10 10 12 1 8 1 8 1 10 100 12 1 1 10 12 1 100 1 10 10 12 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 11 7 12 1 1 100 1 10 11 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 9 12 1 1 9 12 9 12 1 9 12 1 9 12 1 8 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 11 12 1 1 10 100 12 1 1 1 100 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 1 15 10 12 16 1 16 15 10 12 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 16 15 10 100 12 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/ClassLoader nocerts [Ljava/security/cert/Certificate; 0 [Ljava/security/cert/Certificate;
staticfield java/lang/ClassLoader $assertionsDisabled Z 1
ciInstanceKlass java/lang/reflect/Constructor 1 1 439 10 7 12 1 1 1 10 7 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 100 1 8 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 7 12 1 1 10 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
instanceKlass java/lang/reflect/Executable
instanceKlass java/lang/reflect/Field
ciInstanceKlass java/lang/reflect/AccessibleObject 1 1 400 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 7 1 10 7 12 1 1 1 11 12 1 7 1 10 12 1 7 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 7 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 100 1 10 12 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 10 7 1 100 1 8 1 10 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 100 1 8 1 10 11 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 100 1 10 12 1 7 1 10 12 1 10 12 1 1 10 7 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 7 12 1 1 8 1 10 7 12 1 1 1 8 1 10 7 12 1 1 1 9 12 1 7 1 10 7 1 10 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 7 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/reflect/AccessibleObject reflectionFactory Ljdk/internal/reflect/ReflectionFactory; jdk/internal/reflect/ReflectionFactory
instanceKlass java/lang/reflect/Constructor
instanceKlass java/lang/reflect/Method
ciInstanceKlass java/lang/reflect/Executable 1 1 577 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 7 12 1 1 1 18 12 1 1 11 7 12 1 1 1 8 1 8 1 8 1 10 7 12 1 1 1 11 12 1 1 7 1 8 1 8 1 10 12 1 7 1 8 1 10 12 1 8 1 11 100 12 1 1 1 7 1 11 7 12 1 1 1 11 12 1 8 1 18 8 1 10 12 1 10 12 1 1 18 8 1 10 12 1 100 1 10 12 1 10 12 1 11 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 7 12 1 1 10 12 1 8 1 10 12 1 10 12 1 3 100 1 8 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 8 1 8 1 8 1 9 12 1 1 9 12 1 10 12 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 7 1 10 12 1 10 12 1 1 100 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 10 10 10 10 100 12 1 1 1 10 12 1 9 12 1 10 12 1 1 9 12 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 16 15 16 1 16 1 15 10 12 16 15 10 7 12 1 1 1 1 1 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/reflect/Member 1 1 37 100 1 10 12 1 1 100 1 100 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/logging/LogManager$Cleaner
instanceKlass org/apache/logging/log4j/core/util/Log4jThread
instanceKlass jdk/internal/misc/InnocuousThread
instanceKlass java/util/concurrent/ForkJoinWorkerThread
instanceKlass java/lang/ref/Finalizer$FinalizerThread
instanceKlass java/lang/ref/Reference$ReferenceHandler
instanceKlass java/lang/BaseVirtualThread
ciInstanceKlass java/lang/Thread 1 1 870 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 10 12 1 10 100 12 1 1 100 1 8 1 10 12 1 1 9 12 1 9 12 1 1 9 12 1 1 7 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 9 12 1 1 10 12 1 7 1 10 12 1 100 1 8 1 10 9 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 3 8 1 7 1 5 0 10 7 12 1 1 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 1 8 1 10 7 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 7 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 7 12 1 1 9 12 1 8 1 9 7 12 1 1 9 12 1 1 5 0 100 1 10 100 1 10 100 1 10 7 1 10 8 1 10 12 1 1 10 100 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 7 1 9 12 1 1 100 1 10 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 1 10 10 12 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 9 12 1 9 12 1 1 9 12 1 1 10 100 12 1 1 10 12 1 1 9 7 12 1 1 1 10 7 12 1 1 10 12 1 10 12 1 100 1 10 10 12 9 12 1 1 10 12 1 11 100 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 10 10 12 1 10 12 1 1 9 12 1 9 12 10 12 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 1 8 1 10 9 12 1 10 12 1 7 1 8 1 10 10 12 1 8 1 10 12 1 1 9 12 10 12 8 1 10 10 12 1 10 12 1 8 1 10 12 1 10 8 1 10 100 12 1 1 10 12 1 1 100 1 8 1 10 9 12 1 9 12 1 1 10 12 1 1 10 10 12 1 10 12 1 100 10 7 12 1 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 7 1 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 8 1 9 12 1 10 12 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 1 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Thread NEW_THREAD_BINDINGS Ljava/lang/Object; java/lang/Class
staticfield java/lang/Thread EMPTY_STACK_TRACE [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
ciInstanceKlass java/lang/Runnable 1 0 11 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/net/URL 1 1 771 10 7 12 1 1 1 10 12 1 10 7 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 7 1 10 10 12 1 1 8 1 10 12 1 1 9 12 1 100 1 8 1 10 12 1 10 12 1 8 1 9 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 9 12 1 8 1 9 12 1 10 12 1 1 8 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 8 1 10 12 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 10 100 1 10 10 12 1 8 1 10 7 12 1 1 1 10 12 1 9 100 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 12 1 10 12 1 10 12 1 1 8 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 100 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 1 1 7 1 8 1 10 10 12 1 9 12 1 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 12 1 1 10 12 1 8 1 8 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 10 12 1 7 1 10 9 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 8 1 7 1 10 10 7 12 1 1 1 10 12 1 8 9 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 11 7 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 100 1 10 8 8 10 12 1 8 8 8 100 1 10 12 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 100 1 8 1 10 10 10 12 1 1 10 12 1 10 12 1 1 8 1 7 1 10 10 7 1 10 12 1 9 7 12 1 1 1 9 12 1 1 7 1 10 10 7 12 1 1 1 7 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/net/URL defaultFactory Ljava/net/URLStreamHandlerFactory; java/net/URL$DefaultFactory
staticfield java/net/URL streamHandlerLock Ljava/lang/Object; java/lang/Object
staticfield java/net/URL serialPersistentFields [Ljava/io/ObjectStreamField; 7 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/lang/Module 1 1 1070 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 9 12 1 1 10 100 12 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 10 12 1 10 7 12 1 1 8 1 8 1 10 8 1 8 1 9 12 1 1 8 1 10 100 12 1 1 1 10 12 1 9 12 1 1 11 12 1 9 7 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 10 12 1 1 9 12 1 9 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 1 10 12 1 1 11 12 1 9 12 1 11 12 10 100 12 1 1 100 1 8 1 10 11 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 11 12 1 1 11 7 12 1 1 11 12 1 1 9 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 10 12 10 7 12 1 1 10 7 1 18 12 1 1 11 100 12 1 1 1 18 12 1 11 12 1 1 10 100 12 1 1 1 11 12 1 1 10 7 12 1 1 7 1 11 12 1 7 1 7 1 10 12 1 10 7 12 1 1 1 10 11 7 12 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 7 1 10 12 1 10 11 12 1 1 10 12 10 12 1 1 9 12 1 1 100 1 10 10 12 1 1 11 7 1 10 12 1 1 11 12 1 10 10 12 1 11 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 18 12 1 11 12 1 18 12 1 10 12 1 10 12 1 10 12 7 1 10 12 1 10 12 1 10 12 1 9 12 1 7 1 10 10 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 18 12 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 7 1 10 12 1 1 7 1 8 1 10 12 1 1 100 1 11 12 1 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 100 1 10 12 1 10 12 1 1 7 1 7 1 10 12 8 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 7 1 10 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 10 7 12 1 1 8 1 18 12 1 1 100 1 100 1 9 12 1 1 9 12 1 9 12 1 11 100 12 1 1 1 100 1 11 12 1 1 100 1 10 12 1 8 1 10 12 1 10 12 10 12 1 8 1 10 10 100 12 1 1 7 1 10 10 12 1 10 7 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 11 12 1 10 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 16 16 15 10 16 1 15 10 12 16 1 15 10 12 16 1 16 15 10 12 16 16 1 15 10 12 16 15 10 7 12 1 1 1 15 10 100 12 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 100 1 1
staticfield java/lang/Module ALL_UNNAMED_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module ALL_UNNAMED_MODULE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module EVERYONE_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module EVERYONE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module $assertionsDisabled Z 1
ciInstanceKlass java/lang/Character 1 1 604 7 1 7 1 100 1 9 12 1 1 8 1 9 12 1 1 7 1 9 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 3 3 3 3 3 10 12 1 1 10 12 1 3 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 3 10 12 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 10 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 12 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 5 0 10 12 1 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 10 12 1 9 12 1 1 100 1 10 10 12 1 10 12 1 1 3 10 100 12 1 1 1 10 12 1 10 100 12 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 9 100 12 1 1 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 10 12 1 1 7 1 8 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 3 1 3 1 3 1 3 1 1 1 1 1 3 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 3 1 1 3 1 1 1 1 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/lang/Character TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Character $assertionsDisabled Z 1
ciInstanceKlass java/lang/OutOfMemoryError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Integer 1 1 453 7 1 7 1 7 1 7 1 10 12 1 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 9 12 1 1 9 12 1 100 1 8 1 10 12 1 7 1 10 12 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 3 10 12 1 1 3 10 12 1 1 10 12 1 1 10 7 12 1 1 1 11 7 1 10 12 1 1 11 10 12 1 1 8 1 10 12 1 1 8 1 7 1 10 12 1 1 10 12 1 1 5 0 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 9 12 1 1 9 12 1 1 10 12 1 10 7 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 5 0 3 3 3 3 10 12 1 10 12 1 3 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 3 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Integer TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Integer digits [C 36
staticfield java/lang/Integer DigitTens [B 100
staticfield java/lang/Integer DigitOnes [B 100
instanceKlass com/google/gson/internal/LazilyParsedNumber
instanceKlass java/math/BigDecimal
instanceKlass java/util/concurrent/atomic/Striped64
instanceKlass java/math/BigInteger
instanceKlass java/util/concurrent/atomic/AtomicLong
instanceKlass java/util/concurrent/atomic/AtomicInteger
instanceKlass java/lang/Long
instanceKlass java/lang/Integer
instanceKlass java/lang/Short
instanceKlass java/lang/Byte
instanceKlass java/lang/Double
instanceKlass java/lang/Float
ciInstanceKlass java/lang/Number 1 1 37 10 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Thread$FieldHolder 1 1 48 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 100 1 1 1
ciInstanceKlass java/lang/Thread$Constants 0 0 59 7 1 10 7 12 1 1 1 100 1 10 10 7 12 1 1 1 7 1 8 1 10 12 1 9 7 12 1 1 1 7 1 7 1 10 12 1 10 12 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ThreadGroup 1 1 411 10 7 12 1 1 1 9 7 12 1 1 1 8 1 9 12 1 1 7 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 18 12 1 1 11 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 11 12 1 1 11 7 12 1 1 11 12 1 1 10 12 1 1 10 12 1 10 12 1 11 12 1 11 12 1 1 100 1 10 10 12 1 100 1 10 18 12 1 1 11 7 12 1 1 1 11 12 1 1 9 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 11 12 10 12 1 1 10 12 1 1 11 7 1 9 12 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 1 8 1 10 8 1 10 12 1 10 12 1 8 1 9 12 1 1 9 12 1 10 7 12 1 1 1 100 9 12 1 1 7 1 9 12 1 10 12 10 12 1 1 7 10 12 9 12 1 10 12 1 100 1 10 11 12 1 1 7 1 10 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 16 15 10 100 12 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/ThreadGroup $assertionsDisabled Z 1
ciInstanceKlass java/lang/Thread$UncaughtExceptionHandler 1 0 16 100 1 100 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/security/AccessControlContext 1 1 374 9 7 12 1 1 1 9 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 7 1 10 12 1 11 7 12 1 1 1 11 12 1 11 12 1 11 12 1 1 7 1 11 12 1 1 10 12 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 11 100 12 1 1 1 10 7 1 100 1 8 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 10 7 12 1 1 1 9 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 7 12 1 1 1 10 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 100 1 10 12 1 10 12 1 1 100 1 10 12 1 8 1 10 12 1 10 12 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 11 10 12 1 10 12 1 1 10 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1
instanceKlass java/lang/ThreadBuilders$BoundVirtualThread
instanceKlass java/lang/VirtualThread
ciInstanceKlass java/lang/BaseVirtualThread 0 0 36 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 1
ciInstanceKlass java/lang/VirtualThread 0 0 895 7 1 9 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 100 1 10 12 1 9 12 1 1 18 12 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 10 12 1 10 12 1 10 12 1 11 100 12 1 1 1 100 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 100 1 10 10 12 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 10 12 1 9 12 1 1 9 12 1 100 1 10 10 12 1 10 12 1 10 100 12 1 1 10 9 10 10 12 1 1 10 12 1 1 10 100 12 1 1 10 100 1 10 9 10 10 12 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 1 10 12 1 10 12 1 9 12 1 1 10 12 1 10 12 1 11 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 10 100 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 18 10 12 1 1 10 12 1 1 7 1 5 0 100 1 10 10 12 1 7 1 9 12 1 1 10 7 12 1 1 10 9 12 1 1 9 100 12 1 1 1 11 100 12 1 1 1 11 100 1 11 12 10 12 1 10 12 1 1 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 11 100 12 1 1 10 12 9 100 12 1 1 1 9 12 1 10 12 1 1 9 12 1 9 12 1 7 1 10 10 12 1 1 10 12 1 10 12 10 12 1 7 1 7 1 8 1 10 10 12 1 1 10 12 1 10 7 12 1 1 8 1 10 12 1 8 1 10 12 1 9 100 12 1 1 1 10 12 1 1 10 12 1 10 10 10 10 12 9 12 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 10 12 1 1 18 12 1 1 18 12 1 10 7 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 1 18 12 1 10 100 12 1 1 1 100 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 8 1 8 1 10 100 12 1 1 8 1 10 12 1 8 1 8 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 18 12 1 1 18 12 1 1 5 0 9 12 1 10 12 1 18 12 1 100 1 10 12 10 7 12 1 1 10 12 1 1 7 1 8 1 10 10 12 1 10 12 1 1 10 12 1 9 12 1 8 10 12 1 1 8 8 9 12 1 8 10 12 1 1 3 1 3 3 1 3 1 3 1 3 1 3 1 3 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 15 16 15 10 12 16 15 10 12 16 16 15 10 12 16 15 10 12 16 15 10 12 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 1 1 7 1 1 100 1 100 1 1
ciInstanceKlass java/lang/ThreadBuilders$BoundVirtualThread 0 0 132 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 9 100 12 1 1 1 10 12 1 1 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/ContinuationScope 0 0 50 10 100 12 1 1 1 10 100 12 1 1 1 100 1 9 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/StackChunk 0 0 34 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Float 1 1 279 7 1 7 1 10 100 12 1 1 1 10 100 12 1 1 1 4 7 1 10 12 1 1 10 12 1 1 8 1 8 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 100 1 4 10 7 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 3 3 100 1 4 4 4 3 10 12 1 1 9 12 1 1 100 1 10 3 3 4 4 10 12 1 3 3 3 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 4 1 4 1 1 1 4 1 1 3 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Float TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Float $assertionsDisabled Z 1
ciInstanceKlass java/lang/Double 1 1 290 7 1 7 1 10 100 12 1 1 1 10 12 1 1 10 7 1 10 12 1 1 10 100 12 1 1 1 6 0 8 1 10 12 1 1 8 1 10 12 1 1 8 1 6 0 10 12 1 1 100 1 5 0 5 0 8 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 1 6 0 10 7 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 5 0 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 6 0 1 6 0 1 6 0 1 1 1 6 0 1 1 3 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Double TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Byte 1 1 213 7 1 100 1 10 7 12 1 1 1 9 12 1 1 8 1 9 12 1 1 7 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 7 12 1 1 1 10 12 1 1 100 1 7 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 8 1 8 1 10 7 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Byte TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Short 1 1 222 7 1 7 1 100 1 10 7 12 1 1 1 10 12 1 1 100 1 7 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 8 1 9 12 1 1 7 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 8 1 8 1 10 7 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 3 3 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Short TYPE Ljava/lang/Class; java/lang/Class
ciMethod java/lang/Short reverseBytes (S)S 0 0 4612 0 -1
ciInstanceKlass java/lang/Integer$IntegerCache 1 1 93 10 7 12 1 1 1 7 1 10 7 12 1 1 1 9 7 12 1 1 1 8 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 3 10 12 1 100 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 9 12 1 100 1 10 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 100 1 1 1 1 1
staticfield java/lang/Integer$IntegerCache high I 127
staticfield java/lang/Integer$IntegerCache cache [Ljava/lang/Integer; 256 [Ljava/lang/Integer;
staticfield java/lang/Integer$IntegerCache $assertionsDisabled Z 1
ciInstanceKlass java/lang/Long 1 1 524 7 1 7 1 7 1 7 1 10 12 1 1 9 12 1 1 9 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 10 12 1 10 12 1 10 12 1 5 0 5 0 7 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 5 0 5 0 9 12 1 1 9 12 1 5 0 100 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 5 0 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 7 1 10 12 1 1 11 10 12 1 1 8 1 10 12 1 1 8 1 7 1 10 12 1 1 10 12 1 8 1 8 1 11 12 1 1 10 12 1 10 12 1 10 12 1 5 0 5 0 9 7 12 1 1 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 7 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 5 0 10 12 1 10 12 1 5 0 5 0 5 0 10 12 1 1 10 12 1 5 0 5 0 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 5 0 1 1 1 1 3 1 3 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Long TYPE Ljava/lang/Class; java/lang/Class
ciMethod java/lang/Long <init> (J)V 512 0 15776 0 -1
ciMethod java/lang/Long valueOf (J)Ljava/lang/Long; 844 0 15595 0 304
ciInstanceKlass java/lang/Long$LongCache 1 1 48 10 100 12 1 1 1 7 1 10 7 12 1 1 1 9 12 1 1 7 1 5 0 10 12 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1
staticfield java/lang/Long$LongCache cache [Ljava/lang/Long; 256 [Ljava/lang/Long;
ciInstanceKlass jdk/internal/vm/vector/VectorSupport 0 0 573 100 1 10 100 12 1 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 100 1 10 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 9 12 1 1 10 100 12 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask
instanceKlass jdk/internal/vm/vector/VectorSupport$Vector
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload 0 0 32 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$Vector 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/FillerObject 0 0 16 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass java/lang/ref/PhantomReference
instanceKlass java/lang/ref/FinalReference
instanceKlass java/lang/ref/WeakReference
instanceKlass java/lang/ref/SoftReference
ciInstanceKlass java/lang/ref/Reference 1 1 190 9 7 12 1 1 1 9 7 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 7 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 7 1 8 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 7 1 100 1 10 12 9 12 1 9 12 1 100 1 10 10 12 1 10 10 7 12 1 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 7 1 1 1
staticfield java/lang/ref/Reference processPendingLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Reference $assertionsDisabled Z 1
ciMethod java/lang/ref/Reference <init> (Ljava/lang/Object;Ljava/lang/ref/ReferenceQueue;)V 770 0 19155 0 0
ciMethod java/lang/ref/Reference reachabilityFence (Ljava/lang/Object;)V 264 0 151305 0 -1
instanceKlass sun/util/locale/provider/LocaleResources$ResourceReference
instanceKlass sun/util/resources/Bundles$BundleReference
instanceKlass sun/util/locale/LocaleObjectCache$CacheEntry
instanceKlass java/lang/invoke/LambdaFormEditor$Transform
ciInstanceKlass java/lang/ref/SoftReference 1 1 47 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1
instanceKlass java/util/logging/LogManager$LoggerWeakRef
instanceKlass java/util/logging/Level$KnownLevel
instanceKlass java/lang/WeakPairMap$WeakRefPeer
instanceKlass java/lang/ClassValue$Entry
instanceKlass java/lang/invoke/MethodType$ConcurrentWeakInternSet$WeakEntry
instanceKlass java/lang/ThreadLocal$ThreadLocalMap$Entry
instanceKlass java/util/WeakHashMap$Entry
ciInstanceKlass java/lang/ref/WeakReference 1 1 31 10 7 12 1 1 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/ref/Finalizer
ciInstanceKlass java/lang/ref/FinalReference 1 1 50 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 7 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1
instanceKlass jdk/internal/ref/PhantomCleanable
instanceKlass jdk/internal/ref/Cleaner
ciInstanceKlass java/lang/ref/PhantomReference 1 1 39 10 100 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/lang/ref/PhantomReference <init> (Ljava/lang/Object;Ljava/lang/ref/ReferenceQueue;)V 512 0 5631 0 0
ciInstanceKlass java/lang/ref/Finalizer 1 1 155 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 10 12 1 7 1 8 1 10 12 1 10 12 1 1 9 12 1 100 1 10 12 1 7 1 11 100 12 1 1 10 12 1 7 1 10 12 1 100 1 10 12 1 10 7 12 1 1 1 10 100 12 1 1 1 100 1 10 10 12 1 7 1 10 12 1 7 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 10 7 1 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ref/Finalizer lock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Finalizer ENABLED Z 1
staticfield java/lang/ref/Finalizer $assertionsDisabled Z 1
instanceKlass org/eclipse/lsp4j/TextDocumentSyncKind
instanceKlass java/time/zone/ZoneOffsetTransitionRule$TimeDefinition
instanceKlass java/time/DayOfWeek
instanceKlass java/time/Month
instanceKlass org/eclipse/lsp4j/DiagnosticTag
instanceKlass org/eclipse/lsp4j/PrepareSupportDefaultBehavior
instanceKlass org/eclipse/lsp4j/CompletionItemKind
instanceKlass org/eclipse/lsp4j/CompletionItemTag
instanceKlass org/eclipse/lsp4j/InsertTextMode
instanceKlass org/eclipse/lsp4j/SymbolTag
instanceKlass org/eclipse/lsp4j/SymbolKind
instanceKlass com/google/gson/ReflectionAccessFilter$FilterResult
instanceKlass com/google/gson/stream/JsonToken
instanceKlass com/google/gson/Strictness
instanceKlass jdk/internal/logger/BootstrapLogger$LoggingBackend
instanceKlass com/google/gson/ToNumberPolicy
instanceKlass com/google/gson/FieldNamingPolicy
instanceKlass com/google/gson/LongSerializationPolicy
instanceKlass org/eclipse/lsp4j/MessageType
instanceKlass java/util/Comparators$NaturalOrderComparator
instanceKlass com/fasterxml/jackson/core/JsonParser$NumberTypeFP
instanceKlass com/fasterxml/jackson/core/StreamReadFeature
instanceKlass org/yaml/snakeyaml/nodes/NodeId
instanceKlass io/fabric8/kubernetes/model/Scope
instanceKlass com/fasterxml/jackson/annotation/JsonCreator$Mode
instanceKlass com/fasterxml/jackson/databind/annotation/JsonSerialize$Inclusion
instanceKlass com/fasterxml/jackson/databind/annotation/JsonSerialize$Typing
instanceKlass com/fasterxml/jackson/annotation/OptBoolean
instanceKlass com/fasterxml/jackson/annotation/JsonFormat$Feature
instanceKlass com/fasterxml/jackson/databind/util/AccessPattern
instanceKlass com/fasterxml/jackson/databind/cfg/ConstructorDetector$SingleArgConstructor
instanceKlass com/fasterxml/jackson/databind/AnnotationIntrospector$ReferenceProperty$Type
instanceKlass com/fasterxml/jackson/annotation/PropertyAccessor
instanceKlass com/fasterxml/jackson/annotation/JsonProperty$Access
instanceKlass com/fasterxml/jackson/core/JsonToken
instanceKlass org/yaml/snakeyaml/DumperOptions$FlowStyle
instanceKlass org/yaml/snakeyaml/DumperOptions$ScalarStyle
instanceKlass org/yaml/snakeyaml/comments/CommentType
instanceKlass org/yaml/snakeyaml/tokens/Token$ID
instanceKlass org/yaml/snakeyaml/events/Event$ID
instanceKlass com/fasterxml/jackson/core/JsonEncoding
instanceKlass nonapi/io/github/classgraph/scanspec/ScanSpec$ScanSpecPathMatch
instanceKlass nonapi/io/github/classgraph/utils/VersionFinder$OperatingSystem
instanceKlass io/github/classgraph/ClassGraph$CircumventEncapsulationMethod
instanceKlass com/fasterxml/jackson/core/JsonParser$NumberType
instanceKlass com/fasterxml/jackson/databind/DeserializationFeature
instanceKlass com/fasterxml/jackson/databind/cfg/JsonNodeFeature
instanceKlass com/fasterxml/jackson/databind/cfg/EnumFeature
instanceKlass com/fasterxml/jackson/databind/SerializationFeature
instanceKlass com/fasterxml/jackson/core/util/Separators$Spacing
instanceKlass com/fasterxml/jackson/databind/MapperFeature
instanceKlass com/fasterxml/jackson/annotation/JsonFormat$Shape
instanceKlass com/fasterxml/jackson/databind/cfg/CoercionInputShape
instanceKlass com/fasterxml/jackson/databind/cfg/CoercionAction
instanceKlass com/fasterxml/jackson/databind/type/LogicalType
instanceKlass com/fasterxml/jackson/annotation/JsonAutoDetect$Visibility
instanceKlass com/fasterxml/jackson/annotation/Nulls
instanceKlass com/fasterxml/jackson/annotation/JsonInclude$Include
instanceKlass org/apache/logging/log4j/core/appender/rolling/RolloverFrequency
instanceKlass org/apache/logging/log4j/core/appender/rolling/FileExtension
instanceKlass java/nio/file/AccessMode
instanceKlass java/nio/file/FileVisitOption
instanceKlass org/apache/logging/log4j/core/config/LoggerConfig$LoggerConfigPredicate
instanceKlass org/apache/logging/log4j/core/config/PropertiesPlugin$NullLookup
instanceKlass java/lang/annotation/ElementType
instanceKlass com/fasterxml/jackson/dataformat/yaml/YAMLGenerator$Feature
instanceKlass com/fasterxml/jackson/dataformat/yaml/YAMLParser$Feature
instanceKlass com/fasterxml/jackson/core/JsonGenerator$Feature
instanceKlass com/fasterxml/jackson/core/JsonParser$Feature
instanceKlass com/fasterxml/jackson/core/JsonFactory$Feature
instanceKlass com/fasterxml/jackson/core/StreamReadCapability
instanceKlass com/fasterxml/jackson/core/Base64Variant$PaddingReadBehaviour
instanceKlass java/math/RoundingMode
instanceKlass com/fasterxml/jackson/databind/util/internal/PrivateMaxEntriesMap$DrainStatus
instanceKlass org/apache/logging/log4j/core/appender/ConsoleAppender$Target
instanceKlass org/apache/logging/log4j/core/pattern/NameAbbreviator$MaxElementAbbreviator$Strategy
instanceKlass sun/util/locale/provider/LocaleProviderAdapter$Type
instanceKlass org/apache/logging/log4j/core/util/datetime/FixedDateFormat$FixedTimeZoneFormat
instanceKlass org/apache/logging/log4j/core/util/datetime/FixedDateFormat$FixedFormat
instanceKlass org/apache/logging/log4j/core/pattern/PatternParser$ParserState
instanceKlass java/lang/reflect/ProxyGenerator$PrimitiveTypeInfo
instanceKlass java/lang/annotation/RetentionPolicy
instanceKlass org/apache/logging/log4j/core/async/ThreadNameCachingStrategy
instanceKlass org/apache/logging/log4j/core/LifeCycle$State
instanceKlass java/lang/StackStreamFactory$WalkerState
instanceKlass java/lang/StackWalker$ExtendedOption
instanceKlass java/lang/StackWalker$Option
instanceKlass org/apache/logging/log4j/spi/StandardLevel
instanceKlass org/springframework/boot/loader/zip/ZipString$CompareType
instanceKlass org/springframework/boot/loader/zip/ZipContent$Kind
instanceKlass java/lang/invoke/MethodHandleImpl$ArrayAccess
instanceKlass java/time/format/TextStyle
instanceKlass java/time/format/DateTimeFormatterBuilder$SettingsParser
instanceKlass java/util/Locale$Category
instanceKlass java/time/format/ResolverStyle
instanceKlass java/time/format/SignStyle
instanceKlass java/time/temporal/JulianFields$Field
instanceKlass java/time/temporal/IsoFields$Unit
instanceKlass java/time/temporal/IsoFields$Field
instanceKlass java/time/temporal/ChronoUnit
instanceKlass java/time/temporal/ChronoField
instanceKlass java/util/regex/Pattern$Qtype
instanceKlass java/util/stream/MatchOps$MatchKind
instanceKlass java/util/stream/Collector$Characteristics
instanceKlass java/util/stream/StreamShape
instanceKlass java/util/stream/StreamOpFlag$Type
instanceKlass java/util/stream/StreamOpFlag
instanceKlass java/nio/file/attribute/PosixFilePermission
instanceKlass java/lang/invoke/VarHandle$AccessType
instanceKlass java/lang/invoke/VarHandle$AccessMode
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassOption
instanceKlass java/lang/invoke/MethodHandleImpl$Intrinsic
instanceKlass java/lang/invoke/LambdaForm$BasicType
instanceKlass java/lang/invoke/LambdaForm$Kind
instanceKlass sun/invoke/util/Wrapper
instanceKlass java/util/zip/ZipCoder$Comparison
instanceKlass java/nio/file/LinkOption
instanceKlass java/util/concurrent/TimeUnit
instanceKlass sun/nio/fs/WindowsPathType
instanceKlass java/nio/file/StandardOpenOption
instanceKlass java/io/File$PathStatus
instanceKlass java/lang/module/ModuleDescriptor$Requires$Modifier
instanceKlass java/lang/reflect/AccessFlag$Location
instanceKlass java/lang/reflect/AccessFlag
instanceKlass java/lang/module/ModuleDescriptor$Modifier
instanceKlass java/lang/reflect/ClassFileFormatVersion
instanceKlass java/lang/Thread$State
ciInstanceKlass java/lang/Enum 1 1 204 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 10 7 12 1 1 1 100 1 10 10 12 1 1 10 12 1 7 1 10 10 7 12 1 1 10 12 1 1 18 12 1 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 1 100 1 8 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 7 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 100 1 7 1 1 100 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 15 10 100 12 1 1 1 1 1 100 1 100 1 1
instanceKlass java/lang/ref/ReferenceQueue$Null
instanceKlass java/lang/ref/NativeReferenceQueue
ciInstanceKlass java/lang/ref/ReferenceQueue 1 1 183 9 7 12 1 1 1 11 7 12 1 1 1 11 12 1 9 7 12 1 1 1 11 12 1 10 7 12 1 1 9 12 1 1 7 1 10 9 12 1 1 10 12 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 1 100 1 10 9 12 1 1 9 12 1 7 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 5 0 10 10 12 10 12 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 11 100 12 1 1 1 10 7 12 1 1 7 1 10 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ref/ReferenceQueue NULL Ljava/lang/ref/ReferenceQueue; java/lang/ref/ReferenceQueue$Null
staticfield java/lang/ref/ReferenceQueue ENQUEUED Ljava/lang/ref/ReferenceQueue; java/lang/ref/ReferenceQueue$Null
staticfield java/lang/ref/ReferenceQueue $assertionsDisabled Z 1
ciInstanceKlass java/lang/reflect/Method 1 1 472 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 8 1 10 12 1 10 12 1 7 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 11 7 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 7 1 100 1 100 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/Field 1 1 457 9 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 7 1 10 7 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 10 12 1 8 1 8 1 10 11 7 1 9 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 7 12 1 1 10 12 1 1 11 7 1 10 12 1 7 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 1 9 100 12 1 1 10 100 12 1 1 1 10 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/reflect/Parameter 1 1 243 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 11 7 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 8 1 8 1 10 7 12 1 1 1 10 12 1 10 12 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 8 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 7 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 100 1 10 11 12 1 1 11 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/RecordComponent 1 1 196 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 10 100 12 1 1 9 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 7 1 9 12 1 9 12 1 1 9 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 9 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/StringBuffer 1 1 483 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 100 12 1 1 1 10 10 12 1 1 9 12 1 1 10 100 12 1 1 10 100 1 8 10 100 12 1 1 1 8 10 12 1 8 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 7 1 10 12 100 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 10 12 1 9 7 12 1 1 1 9 7 1 9 12 1 1 7 1 7 1 7 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/StringBuffer serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
instanceKlass jdk/internal/loader/Loader
instanceKlass jdk/internal/loader/BuiltinClassLoader
instanceKlass java/net/URLClassLoader
ciInstanceKlass java/security/SecureClassLoader 1 1 102 10 7 12 1 1 1 7 1 10 12 1 9 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 7 1 10 12 1 7 1 10 12 1 11 7 12 1 1 1 7 1 11 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
instanceKlass org/springframework/boot/loader/net/protocol/jar/JarUrlClassLoader
ciInstanceKlass java/net/URLClassLoader 1 1 600 10 7 12 1 1 1 7 1 10 12 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 7 1 10 12 1 9 12 1 1 10 12 1 10 10 12 1 10 100 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 100 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 11 7 12 1 1 10 12 1 11 12 1 11 12 1 1 11 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 1 7 1 100 1 10 12 1 1 7 1 10 10 12 1 1 10 7 12 1 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 8 1 10 12 1 1 10 10 12 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 11 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 7 12 1 1 1 8 1 10 12 1 1 7 1 10 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 9 7 12 1 1 1 10 12 1 8 1 100 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 9 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 100 1 8 1 10 100 1 10 12 1 10 7 12 1 100 1 10 12 1 10 12 1 100 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 100 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/util/jar/Manifest 1 1 339 10 7 12 1 1 1 7 1 10 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 11 100 1 10 12 1 10 12 1 1 11 12 1 1 10 12 1 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 11 12 1 1 100 1 10 12 1 8 1 11 12 1 7 1 10 12 1 1 11 12 1 10 12 1 10 12 1 10 7 12 1 1 1 8 1 10 12 1 1 10 9 7 12 1 1 1 10 12 1 1 10 100 12 1 10 12 1 10 12 1 9 100 12 1 1 1 8 1 10 12 1 8 1 8 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 8 1 10 10 12 1 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 11 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 11 10 12 1 11 10 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/io/ByteArrayInputStream 1 1 96 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/nio/CharBuffer
instanceKlass java/nio/IntBuffer
instanceKlass java/nio/LongBuffer
instanceKlass java/nio/ByteBuffer
ciInstanceKlass java/nio/Buffer 1 1 256 100 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 1 100 1 8 1 10 12 1 8 1 8 1 9 12 10 12 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 100 1 10 100 1 10 100 1 10 9 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 10 12 1 10 100 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 7 1 10 10 12 1 1 7 1 10 10 7 12 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1
staticfield java/nio/Buffer UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/nio/Buffer SCOPED_MEMORY_ACCESS Ljdk/internal/misc/ScopedMemoryAccess; jdk/internal/misc/ScopedMemoryAccess
staticfield java/nio/Buffer IOOBE_FORMATTER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$4
staticfield java/nio/Buffer $assertionsDisabled Z 1
ciMethod java/nio/Buffer position (I)Ljava/nio/Buffer; 1024 0 8718 0 176
ciMethod java/nio/Buffer limit (I)Ljava/nio/Buffer; 1024 0 12800 0 200
ciMethod java/nio/Buffer rewind ()Ljava/nio/Buffer; 1024 0 13355 0 0
ciMethod java/nio/Buffer <init> (IIIILjava/lang/foreign/MemorySegment;)V 1024 0 17181 0 0
ciMethod java/nio/Buffer position ()I 430 0 215 0 -1
ciMethod java/nio/Buffer limit ()I 276 0 138 0 -1
ciMethod java/nio/Buffer remaining ()I 1024 0 45787 0 -1
ciMethod java/nio/Buffer createCapacityException (I)Ljava/lang/IllegalArgumentException; 0 0 1 0 -1
ciMethod java/nio/Buffer createPositionException (I)Ljava/lang/IllegalArgumentException; 0 0 1 0 -1
ciMethod java/nio/Buffer createLimitException (I)Ljava/lang/IllegalArgumentException; 0 0 1 0 -1
ciMethod java/nio/Buffer session ()Ljdk/internal/foreign/MemorySessionImpl; 980 0 106983 0 -1
ciMethod java/nio/Buffer hasRemaining ()Z 1024 0 13639 0 0
ciMethod java/nio/Buffer nextGetIndex (I)I 836 0 24410 0 120
ciInstanceKlass java/util/Objects 1 1 184 10 7 12 1 1 1 100 1 8 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 7 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 11 100 12 1 1 1 100 1 10 10 12 1 8 1 10 12 1 8 1 100 1 11 12 1 1 8 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/Objects requireNonNull (Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object; 768 0 30296 0 -1
ciMethod java/util/Objects requireNonNull (Ljava/lang/Object;)Ljava/lang/Object; 814 0 71383 0 -1
ciMethod java/util/Objects checkFromIndexSize (III)I 594 0 50545 0 -1
instanceKlass java/util/AbstractSequentialList
instanceKlass java/util/Collections$SingletonList
instanceKlass java/util/ArrayList$SubList
instanceKlass java/util/Vector
instanceKlass java/util/Collections$CopiesList
instanceKlass java/util/Arrays$ArrayList
instanceKlass java/util/Collections$EmptyList
instanceKlass java/util/ArrayList
ciInstanceKlass java/util/AbstractList 1 1 218 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 1 11 100 12 1 1 1 11 12 1 1 11 12 1 10 7 12 1 1 1 10 12 1 11 12 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 11 7 1 11 100 1 10 12 1 100 1 10 12 1 10 12 1 1 7 1 100 1 10 12 1 100 1 10 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 100 1 8 1 8 1 8 1 10 7 1 11 10 10 12 1 11 12 1 10 12 1 1 8 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Iterable 1 1 62 10 7 12 1 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Collection 1 1 115 11 100 12 1 1 1 100 1 11 7 12 1 1 1 10 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 11 12 1 1 10 100 12 1 1 1 11 12 1 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/SequencedCollection 1 1 109 100 1 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 8 1
ciInstanceKlass java/util/List 1 1 251 10 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 1 11 100 12 1 1 11 12 1 1 11 12 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 1 100 1 10 100 12 1 1 1 11 12 1 1 11 12 1 11 12 1 100 1 10 12 1 11 12 1 1 11 12 1 1 11 12 1 10 100 12 1 1 1 9 7 12 1 1 1 7 1 10 12 10 12 1 7 1 10 12 1 1 10 12 1 10 12 1 1 11 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1
instanceKlass java/util/IdentityHashMap$Values
instanceKlass java/util/WeakHashMap$Values
instanceKlass com/fasterxml/jackson/databind/util/internal/LinkedDeque
instanceKlass java/util/LinkedHashMap$LinkedValues
instanceKlass java/util/concurrent/ConcurrentLinkedDeque
instanceKlass org/apache/logging/log4j/ThreadContext$EmptyThreadContextStack
instanceKlass java/util/AbstractQueue
instanceKlass java/util/HashMap$Values
instanceKlass java/util/ArrayDeque
instanceKlass java/util/AbstractSet
instanceKlass java/util/ImmutableCollections$AbstractImmutableCollection
instanceKlass java/util/AbstractList
ciInstanceKlass java/util/AbstractCollection 1 1 160 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 100 12 1 1 1 100 1 10 11 12 1 11 7 1 10 12 1 10 12 1 10 100 12 1 1 1 11 8 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/AssertionStatusDirectives 0 0 24 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/ConcurrentModificationException
instanceKlass org/eclipse/lsp4j/jsonrpc/ResponseErrorException
instanceKlass org/eclipse/lsp4j/jsonrpc/JsonRpcException
instanceKlass org/eclipse/lsp4j/jsonrpc/MessageIssueException
instanceKlass com/google/gson/JsonParseException
instanceKlass io/fabric8/kubernetes/client/KubernetesClientException
instanceKlass org/yaml/snakeyaml/error/YAMLException
instanceKlass java/nio/BufferUnderflowException
instanceKlass java/lang/IndexOutOfBoundsException
instanceKlass java/lang/LayerInstantiationException
instanceKlass java/nio/file/FileSystemNotFoundException
instanceKlass java/time/DateTimeException
instanceKlass java/lang/reflect/UndeclaredThrowableException
instanceKlass org/apache/logging/log4j/core/config/ConfigurationException
instanceKlass java/util/MissingResourceException
instanceKlass org/apache/logging/log4j/util/InternalException
instanceKlass java/util/NoSuchElementException
instanceKlass org/apache/logging/log4j/LoggingException
instanceKlass java/lang/SecurityException
instanceKlass java/lang/UnsupportedOperationException
instanceKlass java/lang/IllegalStateException
instanceKlass java/io/UncheckedIOException
instanceKlass java/lang/IllegalArgumentException
instanceKlass java/lang/ArithmeticException
instanceKlass java/lang/NullPointerException
instanceKlass java/lang/IllegalMonitorStateException
instanceKlass java/lang/ArrayStoreException
instanceKlass java/lang/ClassCastException
ciInstanceKlass java/lang/RuntimeException 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/reflect/Executable$ParameterData
instanceKlass jdk/internal/module/ModuleReferenceImpl$CachedHash
instanceKlass java/lang/reflect/Proxy$ProxyBuilder$ProxyClassContext
instanceKlass org/springframework/boot/loader/zip/ZipLocalFileHeaderRecord
instanceKlass org/springframework/boot/loader/zip/ZipCentralDirectoryFileHeaderRecord
instanceKlass org/springframework/boot/loader/zip/Zip64EndOfCentralDirectoryRecord
instanceKlass org/springframework/boot/loader/zip/Zip64EndOfCentralDirectoryLocator
instanceKlass org/springframework/boot/loader/zip/ZipEndOfCentralDirectoryRecord$Located
instanceKlass org/springframework/boot/loader/zip/ZipEndOfCentralDirectoryRecord
instanceKlass org/springframework/boot/loader/zip/ZipContent$Source
instanceKlass org/springframework/boot/loader/net/protocol/nested/NestedLocation
instanceKlass java/util/stream/Collectors$CollectorImpl
instanceKlass org/springframework/boot/loader/launch/JarFileArchive$JarArchiveEntry
instanceKlass java/security/SecureClassLoader$CodeSourceKey
instanceKlass jdk/internal/misc/ThreadTracker$ThreadRef
instanceKlass jdk/internal/reflect/ReflectionFactory$Config
instanceKlass jdk/internal/foreign/abi/UpcallLinker$CallRegs
instanceKlass jdk/internal/foreign/abi/VMStorage
ciInstanceKlass java/lang/Record 1 1 22 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/lang/Record <init> ()V 768 0 16508 0 80
ciInstanceKlass java/lang/invoke/MethodType 1 1 780 7 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 7 12 1 1 8 1 10 100 12 1 1 1 9 7 1 9 7 1 10 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 7 1 8 1 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 9 12 1 11 12 1 1 7 10 12 1 1 10 12 1 1 7 1 7 1 10 7 12 1 1 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 10 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 11 12 1 1 10 12 1 1 7 1 10 12 10 12 1 10 12 1 7 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 10 10 12 1 11 12 1 1 11 12 1 10 100 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 9 12 1 1 7 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 11 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 100 1 10 12 1 1 11 100 12 1 1 18 12 1 1 11 12 1 1 18 12 1 11 12 1 100 1 11 100 12 1 1 10 12 1 100 1 10 12 1 10 100 12 1 1 10 12 1 1 9 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 9 12 1 10 100 12 1 1 10 12 1 100 10 12 1 1 10 12 1 10 7 1 7 1 9 12 1 1 7 1 7 1 7 1 1 1 5 0 1 1 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 16 15 10 100 12 1 1 1 1 1 1 1 7 1 1 7 1 1 100 1 100 1 1
staticfield java/lang/invoke/MethodType internTable Ljava/lang/invoke/MethodType$ConcurrentWeakInternSet; java/lang/invoke/MethodType$ConcurrentWeakInternSet
staticfield java/lang/invoke/MethodType NO_PTYPES [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType objectOnlyTypes [Ljava/lang/invoke/MethodType; 20 [Ljava/lang/invoke/MethodType;
staticfield java/lang/invoke/MethodType METHOD_HANDLE_ARRAY [Ljava/lang/Class; 1 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/invoke/MethodType $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/TypeDescriptor$OfMethod 1 0 43 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
instanceKlass java/lang/IllegalAccessException
instanceKlass java/lang/InstantiationException
instanceKlass java/lang/reflect/InvocationTargetException
instanceKlass java/lang/NoSuchFieldException
instanceKlass java/lang/NoSuchMethodException
instanceKlass java/lang/ClassNotFoundException
ciInstanceKlass java/lang/ReflectiveOperationException 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/invoke/DelegatingMethodHandle
instanceKlass java/lang/invoke/BoundMethodHandle
instanceKlass java/lang/invoke/DirectMethodHandle
ciInstanceKlass java/lang/invoke/MethodHandle 1 1 737 100 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 7 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 10 9 7 12 1 1 1 9 7 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 11 12 1 10 12 1 10 12 1 1 10 100 12 1 1 100 1 11 12 1 10 100 1 11 12 1 7 1 10 12 1 11 12 1 9 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 10 12 1 1 9 12 1 11 12 1 9 12 1 9 12 1 9 12 1 11 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 10 7 12 1 1 10 12 1 1 100 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 10 100 12 1 1 1 10 9 7 12 1 1 1 10 12 1 1 10 12 1 1 8 1 8 1 10 7 12 1 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 7 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 11 7 12 1 1 9 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 8 10 12 1 1 8 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 7 1 100 1 1 100 1 1 100 1 1 1 1
staticfield java/lang/invoke/MethodHandle FORM_OFFSET J 20
staticfield java/lang/invoke/MethodHandle UPDATE_OFFSET J 13
staticfield java/lang/invoke/MethodHandle $assertionsDisabled Z 1
instanceKlass com/fasterxml/jackson/core/util/InternCache
ciInstanceKlass java/util/concurrent/ConcurrentHashMap 1 1 1210 7 1 7 1 3 10 12 1 1 3 7 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 100 1 11 12 1 1 11 12 1 11 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 4 10 12 1 9 12 1 10 12 1 1 100 1 10 5 0 10 12 1 10 12 1 1 5 0 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 7 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 7 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 9 10 12 1 1 9 12 1 10 12 1 1 5 0 9 12 1 1 7 1 10 12 1 9 12 1 1 7 1 10 12 1 9 12 1 7 1 10 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 11 100 1 10 12 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 9 10 12 1 9 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 100 1 10 12 11 100 12 1 1 10 11 7 12 1 10 12 1 100 1 10 12 1 100 1 10 10 9 7 12 1 1 1 10 12 3 10 7 12 1 1 9 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 7 12 1 1 9 12 1 9 7 12 1 1 10 12 1 1 10 12 1 3 9 12 1 9 12 1 10 12 1 1 7 1 9 3 9 12 1 7 1 10 12 1 9 12 1 10 12 1 9 12 1 10 12 1 9 12 1 10 100 12 1 1 1 100 10 12 1 7 1 5 0 10 100 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 100 1 10 12 1 10 100 1 100 1 10 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 7 1 10 12 1 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 10 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 10 12 1 9 12 1 1 9 12 1 1 10 12 1 1 8 10 12 1 1 8 8 8 8 7 10 12 1 1 10 12 1 100 1 8 1 10 7 1 7 1 7 1 1 1 5 0 1 1 3 1 3 1 1 1 1 3 1 3 1 3 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/concurrent/ConcurrentHashMap NCPU I 12
staticfield java/util/concurrent/ConcurrentHashMap serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
staticfield java/util/concurrent/ConcurrentHashMap U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/util/concurrent/ConcurrentHashMap SIZECTL J 20
staticfield java/util/concurrent/ConcurrentHashMap TRANSFERINDEX J 32
staticfield java/util/concurrent/ConcurrentHashMap BASECOUNT J 24
staticfield java/util/concurrent/ConcurrentHashMap CELLSBUSY J 36
staticfield java/util/concurrent/ConcurrentHashMap CELLVALUE J 144
staticfield java/util/concurrent/ConcurrentHashMap ABASE I 16
staticfield java/util/concurrent/ConcurrentHashMap ASHIFT I 2
ciInstanceKlass java/util/concurrent/ConcurrentMap 1 1 208 11 7 12 1 1 1 10 100 12 1 1 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 1 1 100 1 11 12 1 11 12 1 100 1 11 100 12 1 1 1 18 12 1 11 12 1 1 11 100 12 1 1 11 12 1 1 11 100 12 1 11 12 1 1 11 12 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 11 12 15 10 100 12 1 1 1 1 1 100 1 100 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1
instanceKlass com/google/gson/internal/LinkedTreeMap
instanceKlass java/util/concurrent/ConcurrentSkipListMap
instanceKlass com/fasterxml/jackson/databind/util/internal/PrivateMaxEntriesMap
instanceKlass java/util/Collections$SingletonMap
instanceKlass java/util/TreeMap
instanceKlass java/util/EnumMap
instanceKlass java/util/IdentityHashMap
instanceKlass java/util/WeakHashMap
instanceKlass java/util/Collections$EmptyMap
instanceKlass sun/util/PreHashedMap
instanceKlass java/util/HashMap
instanceKlass java/util/ImmutableCollections$AbstractImmutableMap
instanceKlass java/util/concurrent/ConcurrentHashMap
ciInstanceKlass java/util/AbstractMap 1 1 196 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 11 12 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 10 12 1 1 11 12 1 100 1 10 11 12 1 11 7 1 10 12 1 1 11 12 1 9 12 1 1 7 1 10 12 1 9 12 1 1 100 1 10 11 11 12 1 1 11 12 1 7 1 100 1 11 12 1 8 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1
ciInstanceKlass jdk/internal/loader/ClassLoaders 1 1 183 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 11 100 12 1 1 1 100 1 11 12 1 1 11 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 100 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 7 1 8 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/loader/ClassLoaders JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/loader/ClassLoaders BOOT_LOADER Ljdk/internal/loader/ClassLoaders$BootClassLoader; jdk/internal/loader/ClassLoaders$BootClassLoader
staticfield jdk/internal/loader/ClassLoaders PLATFORM_LOADER Ljdk/internal/loader/ClassLoaders$PlatformClassLoader; jdk/internal/loader/ClassLoaders$PlatformClassLoader
staticfield jdk/internal/loader/ClassLoaders APP_LOADER Ljdk/internal/loader/ClassLoaders$AppClassLoader; jdk/internal/loader/ClassLoaders$AppClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$BootClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader
ciInstanceKlass jdk/internal/loader/BuiltinClassLoader 1 1 737 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 10 12 1 9 12 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 7 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 7 1 10 12 1 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 8 1 8 1 10 9 12 1 1 10 7 12 1 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 7 12 1 1 7 1 10 7 12 1 1 1 10 12 1 7 1 8 1 10 12 1 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 11 12 1 7 1 10 11 12 1 1 11 10 12 1 1 7 1 10 12 1 10 7 12 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 100 1 10 12 1 1 11 12 1 100 1 100 1 10 12 1 10 12 1 1 100 1 100 1 10 12 1 10 12 1 18 12 1 1 10 12 1 10 12 1 1 18 100 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 11 12 1 7 1 10 12 1 7 1 100 1 10 12 1 10 12 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 10 7 12 1 1 10 12 1 100 1 8 1 8 1 10 10 12 1 8 1 8 1 10 7 12 1 1 1 11 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 1 10 12 1 7 1 10 11 12 1 1 10 12 10 12 1 10 12 1 100 1 10 12 1 10 12 1 10 10 12 1 10 7 12 1 1 8 1 10 7 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 16 15 10 12 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 100 1 1 1 1 1 100 1 100 1 1
staticfield jdk/internal/loader/BuiltinClassLoader packageToModule Ljava/util/Map; java/util/concurrent/ConcurrentHashMap
staticfield jdk/internal/loader/BuiltinClassLoader $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader 1 1 119 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 7 1 8 1 10 12 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1
ciInstanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader 1 1 42 8 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 100 1 1
ciInstanceKlass java/lang/ArithmeticException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ArrayStoreException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassCastException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassNotFoundException 1 1 96 7 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 7 1 10 12 1 9 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ClassNotFoundException serialPersistentFields [Ljava/io/ObjectStreamField; 1 [Ljava/io/ObjectStreamField;
instanceKlass java/nio/file/InvalidPathException
instanceKlass io/github/classgraph/ClassGraphException
instanceKlass java/util/regex/PatternSyntaxException
instanceKlass java/util/IllegalFormatException
instanceKlass java/lang/NumberFormatException
ciInstanceKlass java/lang/IllegalArgumentException 1 1 35 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/lang/IllegalArgumentException <init> (Ljava/lang/String;)V 0 0 1 0 -1
ciInstanceKlass java/lang/IllegalMonitorStateException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/BootstrapMethodError 0 0 45 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass java/lang/ClassFormatError
instanceKlass java/lang/IncompatibleClassChangeError
instanceKlass java/lang/BootstrapMethodError
instanceKlass java/lang/NoClassDefFoundError
ciInstanceKlass java/lang/LinkageError 1 1 31 10 7 12 1 1 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/NullPointerException 1 1 52 10 100 12 1 1 1 10 12 1 9 100 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 1 1 5 0 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
ciMethod java/lang/NullPointerException <init> ()V 0 0 1 0 -1
ciInstanceKlass java/lang/NoClassDefFoundError 0 0 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackOverflowError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackTraceElement 1 1 235 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 8 1 10 100 12 1 1 1 7 1 9 12 1 8 1 9 12 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 8 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 100 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 1 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer
ciInstanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer 1 1 32 10 7 12 1 1 1 9 7 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/Continuation 0 0 549 9 100 12 1 1 1 9 12 1 9 12 1 100 1 7 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 1 11 100 12 1 1 1 10 7 1 9 12 1 1 9 12 1 1 10 8 1 10 12 1 9 12 1 1 10 11 12 1 1 100 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 11 12 1 1 9 12 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 100 1 10 12 1 11 100 12 1 1 1 10 12 1 9 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 1 9 12 1 1 11 12 1 1 9 12 1 1 8 1 10 11 12 1 1 11 12 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 10 10 12 1 8 1 10 12 1 8 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 9 12 1 11 12 1 7 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 11 7 12 1 1 10 7 1 10 12 1 8 1 9 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 8 1 10 7 12 1 1 1 10 12 1 8 1 100 1 8 1 10 9 12 1 1 8 1 10 7 12 1 1 10 100 12 1 1 8 1 8 1 10 12 10 100 12 1 1 1 10 7 1 10 7 12 1 1 1 18 11 100 12 1 1 1 18 12 1 11 12 1 1 7 1 10 7 12 1 1 10 12 1 1 8 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 8 1 10 12 1 7 1 7 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 1 15 10 12 16 15 11 7 12 1 1 1 16 1 16 1 15 10 12 16 15 10 100 12 1 1 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/misc/UnsafeConstants 1 1 34 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/UnsafeConstants ADDRESS_SIZE0 I 8
staticfield jdk/internal/misc/UnsafeConstants PAGE_SIZE I 4096
staticfield jdk/internal/misc/UnsafeConstants BIG_ENDIAN Z 0
staticfield jdk/internal/misc/UnsafeConstants UNALIGNED_ACCESS Z 1
staticfield jdk/internal/misc/UnsafeConstants DATA_CACHE_LINE_FLUSH_SIZE I 0
ciInstanceKlass jdk/internal/misc/ScopedMemoryAccess 1 1 1425 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 1 10 100 12 1 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 11 12 1 1 10 12 1 11 12 1 10 12 1 1 11 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 100 1 1 1 100 1 1 100 1 1 100 1 1 100 1 1 100 1 1 1
staticfield jdk/internal/misc/ScopedMemoryAccess UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/misc/ScopedMemoryAccess theScopedMemoryAccess Ljdk/internal/misc/ScopedMemoryAccess; jdk/internal/misc/ScopedMemoryAccess
ciMethod jdk/internal/misc/ScopedMemoryAccess getShortUnaligned (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)S 824 0 12709 0 -1
ciMethod jdk/internal/misc/ScopedMemoryAccess getIntUnaligned (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)I 1024 0 11789 0 -1
ciMethod jdk/internal/misc/ScopedMemoryAccess getShortUnalignedInternal (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)S 824 0 12709 0 -1
ciMethod jdk/internal/misc/ScopedMemoryAccess getIntUnalignedInternal (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)I 1024 0 11789 0 -1
ciInstanceKlass java/lang/invoke/MethodHandleStatics 1 1 320 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 7 12 1 1 1 7 1 10 8 1 10 12 1 1 10 7 12 1 1 1 8 1 10 7 12 1 1 1 10 12 1 1 8 1 8 1 10 12 1 10 100 12 1 1 1 10 7 12 1 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 10 12 1 1 7 1 10 12 10 12 1 10 12 1 100 1 10 10 12 1 1 100 1 10 10 12 1 7 1 7 1 8 1 8 1 10 12 1 8 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 8 1 10 7 12 1 1 10 7 12 1 1 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 7 12 1 1 1 9 12 1 8 1 8 1 8 1 8 1 9 12 1 8 1 9 12 1 8 1 8 1 9 12 1 8 1 8 1 9 12 1 8 1 9 12 1 8 1 9 12 1 8 1 8 1 9 12 1 8 1 8 1 10 12 1 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/MethodHandleStatics UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/lang/invoke/MethodHandleStatics CLASSFILE_VERSION I 65
staticfield java/lang/invoke/MethodHandleStatics DEBUG_METHOD_HANDLE_NAMES Z 0
staticfield java/lang/invoke/MethodHandleStatics TRACE_INTERPRETER Z 0
staticfield java/lang/invoke/MethodHandleStatics TRACE_METHOD_LINKAGE Z 0
staticfield java/lang/invoke/MethodHandleStatics TRACE_RESOLVE Z 0
staticfield java/lang/invoke/MethodHandleStatics COMPILE_THRESHOLD I 0
staticfield java/lang/invoke/MethodHandleStatics LOG_LF_COMPILATION_FAILURE Z 0
staticfield java/lang/invoke/MethodHandleStatics DONT_INLINE_THRESHOLD I 30
staticfield java/lang/invoke/MethodHandleStatics PROFILE_LEVEL I 0
staticfield java/lang/invoke/MethodHandleStatics PROFILE_GWT Z 1
staticfield java/lang/invoke/MethodHandleStatics CUSTOMIZE_THRESHOLD I 127
staticfield java/lang/invoke/MethodHandleStatics VAR_HANDLE_GUARDS Z 1
staticfield java/lang/invoke/MethodHandleStatics MAX_ARITY I 255
staticfield java/lang/invoke/MethodHandleStatics VAR_HANDLE_IDENTITY_ADAPT Z 0
staticfield java/lang/invoke/MethodHandleStatics DUMP_CLASS_FILES Ljdk/internal/util/ClassFileDumper; jdk/internal/util/ClassFileDumper
ciInstanceKlass java/lang/invoke/LambdaForm 1 1 1059 7 1 100 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 9 12 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 9 7 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 1 7 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 10 12 1 8 1 8 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 9 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 9 12 1 7 1 10 12 1 1 9 12 1 10 12 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 7 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 12 10 12 1 1 10 12 1 1 9 12 1 8 10 12 1 1 100 1 10 12 1 1 10 12 1 9 7 12 1 1 9 7 12 1 1 1 8 1 10 100 12 1 1 10 12 1 1 7 1 7 1 10 10 12 1 1 10 12 1 1 8 1 8 1 7 1 8 1 10 12 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 10 12 1 1 8 1 8 1 8 1 7 1 8 1 7 1 8 1 7 1 8 1 10 12 1 8 1 9 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 100 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 8 1 8 1 7 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 8 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 7 1 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 8 1 10 12 1 9 12 1 1 7 1 10 7 12 1 1 1 8 1 100 1 10 12 1 9 12 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 9 7 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 10 12 1 10 10 12 1 9 12 1 9 9 12 1 7 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 7 1 9 1 1 1 1 3 1 3 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 7 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/LambdaForm DEFAULT_CUSTOMIZED Ljava/lang/invoke/MethodHandle; null
staticfield java/lang/invoke/LambdaForm DEFAULT_KIND Ljava/lang/invoke/LambdaForm$Kind; java/lang/invoke/LambdaForm$Kind
staticfield java/lang/invoke/LambdaForm COMPILE_THRESHOLD I 0
staticfield java/lang/invoke/LambdaForm INTERNED_ARGUMENTS [[Ljava/lang/invoke/LambdaForm$Name; 5 [[Ljava/lang/invoke/LambdaForm$Name;
staticfield java/lang/invoke/LambdaForm IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/LambdaForm LF_identity [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm LF_zero [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm NF_identity [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm NF_zero [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm createFormsLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/invoke/LambdaForm DEBUG_NAME_COUNTERS Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm DEBUG_NAMES Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm TRACE_INTERPRETER Z 0
staticfield java/lang/invoke/LambdaForm $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MemberName 1 1 724 7 1 7 1 100 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 7 12 1 1 10 12 1 7 1 7 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 8 1 10 100 12 1 1 1 7 1 10 10 12 1 1 100 1 100 1 10 12 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 8 1 9 12 1 1 3 10 12 1 10 12 1 10 12 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 8 10 12 1 1 10 12 1 1 8 1 9 7 1 8 9 7 1 10 12 1 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 8 1 8 1 7 1 10 12 1 10 100 12 1 1 1 100 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 3 10 12 1 3 10 12 1 3 3 3 3 3 3 10 12 1 3 9 12 1 10 12 1 1 3 10 12 1 10 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 7 1 10 10 10 12 100 1 10 10 10 12 1 1 10 12 1 1 10 10 12 1 8 10 7 1 10 12 1 10 7 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 1 100 1 8 1 10 7 1 10 12 1 10 12 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 7 12 1 1 1 8 1 8 1 10 12 1 8 1 10 10 10 12 1 10 12 1 8 1 8 1 10 10 12 1 8 1 10 100 12 1 1 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 100 1 10 8 1 8 1 8 1 8 1 10 12 1 7 1 100 1 7 1 10 100 1 10 7 1 10 7 12 1 1 1 9 7 12 1 1 1 7 1 7 1 1 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/MemberName $assertionsDisabled Z 1
instanceKlass java/lang/invoke/VarHandleBooleans$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleInts$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleLongs$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleReferences$Array
instanceKlass java/lang/invoke/VarHandleLongs$Array
instanceKlass java/lang/invoke/VarHandleByteArrayAsDoubles$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsLongs$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsFloats$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsInts$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsChars$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsShorts$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleReferences$FieldInstanceReadOnly
ciInstanceKlass java/lang/invoke/VarHandle 1 1 473 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 100 1 10 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 10 12 1 9 12 1 1 10 100 12 1 1 10 12 1 9 100 12 1 1 1 9 12 1 1 10 12 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 10 12 1 10 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 9 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 100 1 10 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 10 12 1 1 7 1 10 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 100 1 1 1 100 1 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1
staticfield java/lang/invoke/VarHandle VFORM_OFFSET J 16
staticfield java/lang/invoke/VarHandle $assertionsDisabled Z 1
instanceKlass jdk/internal/reflect/FieldAccessorImpl
instanceKlass jdk/internal/reflect/ConstructorAccessorImpl
instanceKlass jdk/internal/reflect/MethodAccessorImpl
ciInstanceKlass jdk/internal/reflect/MagicAccessorImpl 1 1 16 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/DirectMethodHandleAccessor
ciInstanceKlass jdk/internal/reflect/MethodAccessorImpl 1 1 38 10 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/MethodAccessor 1 0 17 100 1 100 1 1 1 1 100 1 100 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/DirectConstructorHandleAccessor$NativeAccessor
instanceKlass jdk/internal/reflect/DirectConstructorHandleAccessor
instanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl
ciInstanceKlass jdk/internal/reflect/ConstructorAccessorImpl 1 1 27 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass jdk/internal/reflect/ConstructorAccessor 1 0 16 100 1 100 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass jdk/internal/reflect/DelegatingClassLoader 0 0 18 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/CallerSensitive 1 0 17 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl 0 0 125 10 7 12 1 1 1 9 7 12 1 1 1 100 1 10 12 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 7 1 10 12 1 1 10 12 1 1 8 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/ConstantPool 1 1 142 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 11 7 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl 0 0 47 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 8 11 100 12 1 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/FieldAccessor 1 0 48 100 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/MethodHandleFieldAccessorImpl
instanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/FieldAccessorImpl 1 1 269 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 100 1 10 12 1 1 10 8 1 10 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 10 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 8 1 10 12 1 8 1 8 1 8 1 8 1 10 7 12 1 1 1 8 1 8 1 8 1 10 12 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl 0 0 62 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 10 12 1 9 12 1 1 10 100 12 1 1 1 9 12 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/invoke/VolatileCallSite
instanceKlass java/lang/invoke/MutableCallSite
instanceKlass java/lang/invoke/ConstantCallSite
ciInstanceKlass java/lang/invoke/CallSite 1 1 307 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 7 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 10 12 1 1 100 1 7 1 10 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 100 12 1 1 10 12 1 1 9 12 1 9 100 12 1 1 1 8 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 1 9 12 1 8 1 100 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 8 10 12 1 1 9 12 1 1 100 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 8 1 10 10 12 10 12 1 1 7 1 7 1 7 1 8 1 10 12 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1
staticfield java/lang/invoke/CallSite $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/ConstantCallSite 1 1 65 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 100 1 10 12 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/ConstantCallSite UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
instanceKlass java/lang/invoke/DirectMethodHandle$StaticAccessor
instanceKlass java/lang/invoke/DirectMethodHandle$Special
instanceKlass java/lang/invoke/DirectMethodHandle$Interface
instanceKlass java/lang/invoke/DirectMethodHandle$Accessor
instanceKlass java/lang/invoke/DirectMethodHandle$Constructor
ciInstanceKlass java/lang/invoke/DirectMethodHandle 1 1 923 7 1 7 1 100 1 7 1 7 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 100 1 10 9 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 7 1 10 12 1 7 1 10 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 10 12 1 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 7 12 1 1 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 10 7 1 9 12 9 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 7 1 9 12 1 1 10 7 12 1 1 1 10 12 10 12 1 7 1 10 12 1 10 12 1 1 8 1 9 12 1 9 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 9 7 1 10 12 1 9 12 1 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 8 1 8 1 8 1 8 1 10 12 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 8 9 12 1 1 10 12 1 1 8 1 8 8 9 12 1 8 1 8 8 8 8 8 1 8 10 12 1 7 1 10 12 1 8 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/DirectMethodHandle IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/DirectMethodHandle FT_UNCHECKED_REF I 8
staticfield java/lang/invoke/DirectMethodHandle ACCESSOR_FORMS [Ljava/lang/invoke/LambdaForm; 132 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/DirectMethodHandle ALL_WRAPPERS [Lsun/invoke/util/Wrapper; 10 [Lsun/invoke/util/Wrapper;
staticfield java/lang/invoke/DirectMethodHandle NFS [Ljava/lang/invoke/LambdaForm$NamedFunction; 12 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/DirectMethodHandle OBJ_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle LONG_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle $assertionsDisabled Z 1
ciMethod java/lang/invoke/DirectMethodHandle allocateInstance (Ljava/lang/Object;)Ljava/lang/Object; 964 0 13555 0 424
ciMethod java/lang/invoke/DirectMethodHandle constructorMethod (Ljava/lang/Object;)Ljava/lang/Object; 964 0 15323 0 0
ciInstanceKlass java/lang/invoke/MutableCallSite 0 0 63 10 100 12 1 1 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/lang/invoke/VolatileCallSite 0 0 37 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/ResolvedMethodName 1 1 16 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives 1 1 690 100 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 7 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 1 9 7 12 1 1 1 8 1 10 100 12 1 1 1 7 1 10 12 100 1 100 1 8 1 7 1 10 10 12 1 7 1 9 7 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 9 12 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 8 1 8 1 8 1 7 1 10 12 1 8 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 10 12 1 1 10 12 1 10 100 12 1 1 1 100 1 8 1 10 100 12 1 1 1 7 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 7 1 7 1 10 12 1 10 12 1 8 1 8 1 10 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 7 1 9 12 1 1 10 7 12 1 1 1 10 10 12 1 9 12 1 10 12 1 9 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 7 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 7 12 1 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 100 1 100 1 10 10 100 1 100 1 10 100 1 10 10 12 1 1 10 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 7 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 1 1
staticfield java/lang/invoke/MethodHandleNatives $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext 1 1 49 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass jdk/internal/foreign/abi/NativeEntryPoint 0 0 194 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 9 12 1 1 18 12 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 18 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 1 15 10 12 16 1 16 15 10 12 15 10 100 12 1 1 1 1 1 100 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/foreign/abi/ABIDescriptor 0 0 55 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/foreign/abi/VMStorage 0 0 91 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 18 12 1 18 12 1 1 18 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 15 15 15 15 15 10 100 12 1 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/foreign/abi/UpcallLinker$CallRegs 0 0 66 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 18 12 1 1 18 12 1 1 18 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 8 1 15 15 15 10 100 12 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/StackWalker 1 1 271 9 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 11 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 11 12 1 1 100 1 8 1 10 10 7 12 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 18 12 1 1 100 1 8 1 10 8 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 7 12 1 1 11 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 16 15 10 12 16 1 15 10 7 12 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/StackWalker DEFAULT_EMPTY_OPTION Ljava/util/EnumSet; java/util/RegularEnumSet
staticfield java/lang/StackWalker DEFAULT_WALKER Ljava/lang/StackWalker; java/lang/StackWalker
ciInstanceKlass java/lang/StackWalker$StackFrame 1 1 41 100 1 10 12 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass java/lang/LiveStackFrameInfo
ciInstanceKlass java/lang/StackFrameInfo 1 1 142 10 7 12 1 1 1 9 7 12 1 1 1 9 7 1 9 12 1 1 11 7 12 1 1 1 9 12 1 1 11 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 11 12 1 11 12 1 1 11 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 11 12 1 1 9 12 1 1 10 7 1 10 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 7 1 1 1 1 1 1
staticfield java/lang/StackFrameInfo JLIA Ljdk/internal/access/JavaLangInvokeAccess; java/lang/invoke/MethodHandleImpl$1
ciInstanceKlass java/lang/LiveStackFrameInfo 0 0 97 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 7 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 10 100 1 10 12 1 100 1 10 12 1 7 1 7 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/LiveStackFrame 0 0 135 100 1 10 100 12 1 1 1 11 7 12 1 1 1 11 12 1 10 7 12 1 1 1 100 1 8 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 12 1 10 12 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1
instanceKlass java/lang/StackStreamFactory$StackFrameTraverser
ciInstanceKlass java/lang/StackStreamFactory$AbstractStackWalker 1 1 375 7 1 7 1 3 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 9 7 12 1 1 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 8 1 10 12 10 7 12 1 1 9 12 1 8 1 5 0 8 1 8 1 9 12 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 9 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 7 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 15 10 100 12 1 1 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/module/Modules 1 1 504 10 7 12 1 1 1 9 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 11 12 1 11 12 1 11 12 1 11 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 18 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 11 12 1 9 12 1 1 11 100 12 1 1 1 10 12 1 1 10 10 12 1 10 9 12 1 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 12 1 1 18 12 1 1 11 100 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 7 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 18 12 1 1 11 12 1 1 18 12 1 1 11 12 1 1 10 12 1 18 18 10 12 1 1 9 12 1 1 11 7 12 1 1 1 100 1 10 11 12 1 11 12 1 1 11 12 1 1 10 100 1 10 12 1 1 10 100 12 1 1 10 12 1 1 11 12 10 12 1 1 7 1 10 18 12 1 10 12 1 1 7 1 8 1 10 12 1 10 100 12 1 1 18 12 1 11 11 12 10 12 1 10 10 100 1 18 12 1 10 10 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 1 16 16 15 10 12 1 16 1 16 1 15 10 12 1 16 1 16 1 15 10 12 16 1 15 10 16 1 15 10 12 16 1 15 10 12 16 15 10 12 16 15 10 12 15 10 100 12 1 1 1 1 1 1 100 1 100 1 1
staticfield jdk/internal/module/Modules JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/module/Modules JLMA Ljdk/internal/access/JavaLangModuleAccess; java/lang/module/ModuleDescriptor$1
staticfield jdk/internal/module/Modules $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/Invokers$Holder 1 1 128 1 100 1 100 1 1 1 1 1 1 1 7 1 7 1 7 1 1 12 10 1 1 12 10 1 1 12 10 1 1 100 1 1 12 9 1 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 1 12 10 1 1 100 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 12 10 12 10 12 10 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 1
ciMethod java/lang/invoke/Invokers$Holder linkToTargetMethod (Ljava/lang/Object;)Ljava/lang/Object; 1024 0 24201 0 -1
ciMethod java/lang/invoke/Invokers$Holder linkToTargetMethod (Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 770 0 10215 0 -1
ciInstanceKlass java/lang/invoke/DirectMethodHandle$Holder 1 1 548 1 100 1 100 1 1 1 1 1 1 1 7 1 1 12 10 1 12 10 1 7 1 7 1 1 12 10 1 1 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 12 10 12 10 1 1 12 10 12 10 1 1 12 10 1 1 12 10 12 10 12 10 12 10 1 1 12 10 12 10 1 1 12 10 12 10 1 1 12 10 1 1 12 10 12 10 12 10 12 10 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 12 10 12 10 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 1 1 12 10 12 10 1 1 12 10 1 12 10 12 10 1 1 12 10 1 12 10 12 10 1 1 1 12 10 1 12 10 1 7 1 1 12 9 1 7 1 12 10 1 12 10 1 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 1 12 10 1 12 10 1 1 1 12 10 1 12 10 1 1 1 1 12 10 1 12 10 1 1 1 12 10 1 12 10 1 1 1 1 1 12 10 1 12 10 1 1 1 12 10 1 12 10 1 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 1
ciMethod java/lang/invoke/DirectMethodHandle$Holder newInvokeSpecial (Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 512 0 2674 0 -1
instanceKlass io/github/classgraph/PotentiallyUnmodifiableList
ciInstanceKlass java/util/ArrayList 1 1 509 10 7 12 1 1 1 7 1 9 7 12 1 1 1 9 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 11 7 12 1 1 1 9 12 1 1 11 12 1 1 7 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 7 1 10 12 1 10 10 7 12 1 1 1 10 7 12 1 1 10 12 1 100 1 10 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 11 12 1 1 11 7 12 1 1 1 11 12 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 11 12 1 7 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 100 1 8 1 10 100 1 10 12 1 7 1 10 12 1 10 12 1 1 7 1 10 12 1 10 12 1 1 11 100 12 1 1 7 1 10 12 1 10 12 1 1 11 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 10 12 1 1 7 1 7 1 7 1 1 1 1 5 0 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/util/ArrayList EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
staticfield java/util/ArrayList DEFAULTCAPACITY_EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
ciInstanceKlass java/util/RandomAccess 1 0 7 100 1 100 1 1 1
instanceKlass java/nio/MappedByteBuffer
instanceKlass java/nio/HeapByteBuffer
ciInstanceKlass java/nio/ByteBuffer 1 1 446 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 7 1 10 12 1 10 12 1 1 7 1 10 12 1 10 12 1 100 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 100 1 10 10 12 1 1 10 12 1 10 12 1 10 12 100 1 5 0 9 12 1 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 100 1 10 10 12 1 1 9 12 1 10 12 1 100 1 10 10 12 1 10 12 1 10 12 10 12 1 9 12 100 1 10 10 12 1 10 12 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 8 1 8 1 10 12 1 8 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 7 1 9 12 1 8 1 10 12 1 8 1 8 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 10 12 1 1 10 12 10 12 10 12 10 12 10 12 10 12 10 12 1 1 10 12 1 9 12 1 1 7 10 7 12 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1
staticfield java/nio/ByteBuffer ARRAY_BASE_OFFSET J 16
staticfield java/nio/ByteBuffer $assertionsDisabled Z 1
ciMethod java/nio/ByteBuffer position (I)Ljava/nio/Buffer; 1024 0 13033 0 208
ciMethod java/nio/ByteBuffer limit (I)Ljava/nio/Buffer; 1024 0 13033 0 256
ciMethod java/nio/ByteBuffer getShort ()S 0 0 1 0 -1
ciMethod java/nio/ByteBuffer getInt ()I 0 0 1 0 -1
ciMethod java/nio/ByteBuffer position (I)Ljava/nio/ByteBuffer; 1024 0 16900 0 176
ciMethod java/nio/ByteBuffer limit (I)Ljava/nio/ByteBuffer; 1024 0 13129 0 0
ciMethod java/nio/ByteBuffer rewind ()Ljava/nio/ByteBuffer; 1024 0 13355 0 96
ciMethod java/nio/ByteBuffer <init> (IIII[BILjava/lang/foreign/MemorySegment;)V 1024 0 17407 0 0
ciMethod java/nio/ByteBuffer allocate (I)Ljava/nio/ByteBuffer; 398 0 13740 0 688
ciMethod java/nio/ByteBuffer allocateDirect (I)Ljava/nio/ByteBuffer; 2 0 85 0 -1
ciMethod java/nio/ByteBuffer order (Ljava/nio/ByteOrder;)Ljava/nio/ByteBuffer; 1024 0 14154 0 0
instanceKlass com/google/gson/internal/LinkedTreeMap$EntrySet
instanceKlass java/util/concurrent/ConcurrentSkipListMap$KeySet
instanceKlass java/util/concurrent/ConcurrentSkipListSet
instanceKlass java/util/LinkedHashMap$LinkedKeySet
instanceKlass java/lang/ProcessEnvironment$CheckedEntrySet
instanceKlass java/util/TreeSet
instanceKlass java/util/LinkedHashMap$LinkedEntrySet
instanceKlass java/util/Collections$SingletonSet
instanceKlass java/util/TreeMap$EntrySet
instanceKlass java/util/EnumSet
instanceKlass java/util/IdentityHashMap$KeySet
instanceKlass java/util/HashMap$KeySet
instanceKlass java/util/AbstractMap$1
instanceKlass java/util/WeakHashMap$KeySet
instanceKlass java/util/Collections$SetFromMap
instanceKlass java/util/HashSet
instanceKlass java/util/ImmutableCollections$MapN$1
instanceKlass java/util/Collections$EmptySet
instanceKlass java/util/HashMap$EntrySet
ciInstanceKlass java/util/AbstractSet 1 1 96 10 7 12 1 1 1 100 1 100 1 11 12 1 1 10 100 1 10 12 1 1 100 1 100 1 10 12 1 1 11 100 12 1 1 1 11 12 1 1 10 100 12 1 1 10 100 12 1 1 1 11 10 12 1 1 11 12 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/function/Function 1 1 77 10 100 12 1 1 1 18 12 1 1 18 18 12 1 11 7 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 11 12 15 11 12 15 11 12 15 10 100 12 1 1 1 1 100 1 100 1 1
ciMethod java/util/function/Function apply (Ljava/lang/Object;)Ljava/lang/Object; 0 0 1 0 -1
ciInstanceKlass java/lang/annotation/Annotation 1 0 17 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/ClassValue$ClassValueMap
ciInstanceKlass java/util/WeakHashMap 1 1 399 7 1 10 7 12 1 1 1 7 1 10 9 7 12 1 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 3 10 7 12 1 1 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 4 10 12 1 11 7 12 1 1 1 6 0 10 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 9 12 1 10 12 1 9 12 1 1 9 12 1 9 12 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 1 7 1 3 10 12 1 1 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 100 1 11 12 1 11 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 9 12 1 1 7 1 10 12 1 9 12 1 1 7 1 10 9 12 100 1 10 10 100 12 1 1 10 12 1 11 100 12 1 1 1 100 1 10 11 100 12 1 1 8 1 10 12 1 10 12 10 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/WeakHashMap NULL_KEY Ljava/lang/Object; java/lang/Object
ciInstanceKlass java/util/IdentityHashMap 1 1 403 9 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 3 3 10 7 12 1 1 7 1 9 12 1 1 11 7 12 1 1 1 6 0 10 12 10 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 3 3 100 1 8 1 10 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 1 1 100 1 11 12 1 11 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 11 12 1 1 10 12 1 10 12 1 9 12 1 10 100 100 1 7 1 10 12 1 9 12 1 7 1 10 12 1 9 12 1 1 7 1 10 100 1 10 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 100 1 8 1 10 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 10 12 1 1 10 10 100 12 1 1 11 100 12 1 1 100 1 10 11 100 12 1 1 10 12 1 10 7 1 7 1 1 1 3 1 3 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1
staticfield java/util/IdentityHashMap NULL_KEY Ljava/lang/Object; java/lang/Object
ciInstanceKlass java/util/function/BiConsumer 1 1 60 10 100 12 1 1 1 18 12 1 1 11 7 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 11 12 15 10 100 12 1 1 1 1 100 1 100 1 1
ciMethod java/util/function/BiConsumer accept (Ljava/lang/Object;Ljava/lang/Object;)V 0 0 1 0 -1
ciInstanceKlass java/util/function/Supplier 1 0 14 100 1 100 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/DirectMethodHandle$Constructor 1 1 91 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 100 1 10 12 1 9 12 1 9 12 1 10 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/DirectMethodHandle$Constructor $assertionsDisabled Z 1
ciInstanceKlass java/lang/ref/Cleaner 1 1 68 10 7 12 1 1 1 7 1 10 9 7 12 1 1 1 10 10 12 1 1 8 1 10 7 12 1 1 1 8 1 8 1 7 1 10 12 1 7 1 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/ref/CleanerImpl 1 1 145 9 7 12 1 1 1 7 1 8 10 12 1 1 11 7 12 1 1 1 10 7 12 1 1 7 1 10 9 12 1 1 7 1 10 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 5 0 10 12 1 1 7 1 11 12 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass jdk/internal/ref/CleanerImpl$PhantomCleanableRef 1 1 51 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 11 7 12 1 1 100 1 8 1 10 12 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass java/io/FileCleanable
instanceKlass jdk/internal/ref/CleanerImpl$CleanerCleanable
instanceKlass jdk/internal/ref/CleanerImpl$PhantomCleanableRef
ciInstanceKlass jdk/internal/ref/PhantomCleanable 1 1 98 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 100 1 8 1 10 12 1 8 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/ref/Cleaner$Cleanable 1 0 14 100 1 100 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/lang/ref/Cleaner$1 1 1 37 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/lang/ref/Cleaner$1 apply (Ljava/lang/ref/Cleaner;)Ljdk/internal/ref/CleanerImpl; 524 0 5384 0 0
ciMethod java/lang/ref/Cleaner$1 apply (Ljava/lang/Object;)Ljava/lang/Object; 524 0 5384 0 136
ciMethod java/lang/ref/Cleaner register (Ljava/lang/Object;Ljava/lang/Runnable;)Ljava/lang/ref/Cleaner$Cleanable; 772 0 5533 0 0
ciInstanceKlass jdk/internal/ref/CleanerFactory 1 1 34 10 100 12 1 1 1 9 7 12 1 1 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/ref/CleanerFactory commonCleaner Ljava/lang/ref/Cleaner; java/lang/ref/Cleaner
ciMethod jdk/internal/ref/CleanerFactory cleaner ()Ljava/lang/ref/Cleaner; 512 0 708 0 0
ciInstanceKlass java/nio/file/Path 1 1 208 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 1 8 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 10 100 1 7 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 10 11 7 12 1 1 11 12 1 1 11 12 1 11 12 1 1 100 1 10 11 12 1 1 11 12 1 7 1 11 10 100 1 8 1 10 100 1 11 12 1 1 100 1 10 12 1 11 12 1 1 7 1 7 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1
instanceKlass org/springframework/boot/loader/jar/NestedJarFile
instanceKlass org/springframework/boot/loader/net/protocol/jar/UrlJarFile
ciInstanceKlass java/util/jar/JarFile 1 1 695 7 1 9 12 1 1 9 12 1 7 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 9 12 1 10 7 12 1 1 1 9 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 10 12 1 1 9 12 9 12 1 10 12 1 1 100 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 9 12 1 9 12 1 1 11 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 9 12 1 1 9 12 1 1 8 1 10 100 12 1 1 7 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 11 12 1 1 11 12 1 1 11 12 1 18 12 1 1 11 100 12 1 1 1 18 12 1 1 11 12 1 1 11 12 1 1 18 18 10 12 7 1 10 12 1 8 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 12 1 8 1 11 12 1 1 7 1 10 12 10 12 1 1 10 12 1 8 1 10 12 10 12 1 1 10 12 1 11 12 1 1 10 12 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 10 12 1 1 100 1 8 1 10 7 1 9 12 1 1 10 12 1 8 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 7 12 1 1 10 12 1 8 1 10 12 1 8 1 10 7 12 1 1 1 9 7 12 1 1 8 1 10 12 1 8 1 10 8 1 8 1 10 3 10 12 1 1 10 12 1 1 100 1 8 1 8 1 10 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 1 1 9 12 1 9 12 1 9 12 1 100 1 10 100 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 7 12 1 1 9 7 12 1 1 1 10 7 12 1 1 10 12 1 7 1 10 12 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 7 1 10 10 7 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 8 1 8 1 10 12 1 10 12 1 8 1 10 12 1 1 8 1 1 1 1 1 1 1 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 1 16 15 16 16 1 15 10 100 12 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/util/jar/JarFile BASE_VERSION Ljava/lang/Runtime$Version; java/lang/Runtime$Version
staticfield java/util/jar/JarFile BASE_VERSION_FEATURE I 8
staticfield java/util/jar/JarFile RUNTIME_VERSION Ljava/lang/Runtime$Version; java/lang/Runtime$Version
staticfield java/util/jar/JarFile MULTI_RELEASE_ENABLED Z 1
staticfield java/util/jar/JarFile MULTI_RELEASE_FORCED Z 0
staticfield java/util/jar/JarFile JUZFA Ljdk/internal/access/JavaUtilZipFileAccess; java/util/zip/ZipFile$1
staticfield java/util/jar/JarFile CLASSPATH_CHARS [B 12
staticfield java/util/jar/JarFile CLASSPATH_LASTOCC [B 65
staticfield java/util/jar/JarFile CLASSPATH_OPTOSFT [B 12
staticfield java/util/jar/JarFile MULTIRELEASE_CHARS [B 19
staticfield java/util/jar/JarFile MULTIRELEASE_LASTOCC [B 65
staticfield java/util/jar/JarFile MULTIRELEASE_OPTOSFT [B 19
ciInstanceKlass java/util/zip/ZipConstants 1 0 78 100 1 100 1 1 1 1 5 0 1 5 0 1 5 0 1 5 0 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 3 1 3 1 1 1 3 1 1 1 1 1 1 1 3 1 3 1 1 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1
instanceKlass java/util/jar/JarFile
ciInstanceKlass java/util/zip/ZipFile 1 1 551 7 1 7 1 7 1 10 12 1 1 7 1 10 12 1 9 7 12 1 1 1 10 12 1 10 7 12 1 1 100 1 7 1 10 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 10 12 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 8 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 9 7 12 1 1 1 9 12 1 1 10 12 1 8 10 12 1 1 10 12 1 1 8 1 9 12 1 1 9 12 1 9 10 12 1 1 9 12 1 1 7 1 9 12 1 10 12 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 5 0 5 0 5 0 5 0 7 1 10 12 1 100 1 8 1 10 100 1 9 12 1 10 12 1 7 1 18 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 7 1 10 12 1 18 18 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 7 1 9 12 1 1 11 7 12 1 1 1 10 10 12 1 9 12 1 10 12 1 9 12 1 1 10 12 1 9 12 1 9 12 1 10 12 1 9 12 1 9 12 1 10 12 1 10 12 1 3 9 12 1 10 7 12 1 1 1 10 12 1 1 9 12 9 12 1 1 10 12 1 100 1 10 12 1 1 100 1 8 1 10 8 1 8 1 9 12 1 1 9 12 1 7 1 10 10 12 1 11 7 1 11 12 1 1 9 12 1 9 12 1 8 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 9 12 1 7 1 10 10 7 12 1 1 1 7 1 1 1 1 1 3 1 3 1 3 1 3 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 1 16 1 15 10 12 16 15 16 15 10 12 16 15 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/util/zip/ZipFile DISABLE_ZIP64_EXTRA_VALIDATION Z 0
ciMethod java/util/jar/JarFile getInputStream (Ljava/util/zip/ZipEntry;)Ljava/io/InputStream; 140 0 70 0 -1
ciInstanceKlass java/nio/file/OpenOption 1 0 7 100 1 100 1 1 1
ciInstanceKlass java/lang/ref/ReferenceQueue$Null 1 1 29 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/nio/ByteOrder 1 1 50 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 8 1 10 12 1 9 12 8 1 9 12 10 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/nio/ByteOrder BIG_ENDIAN Ljava/nio/ByteOrder; java/nio/ByteOrder
staticfield java/nio/ByteOrder LITTLE_ENDIAN Ljava/nio/ByteOrder; java/nio/ByteOrder
staticfield java/nio/ByteOrder NATIVE_ORDER Ljava/nio/ByteOrder; java/nio/ByteOrder
ciMethod java/nio/ByteOrder nativeOrder ()Ljava/nio/ByteOrder; 1024 0 31416 0 88
ciInstanceKlass java/nio/HeapByteBuffer 1 1 360 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 10 12 1 1 10 12 1 9 12 1 1 9 12 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 10 7 12 1 1 1 10 12 1 10 12 1 100 1 10 10 12 1 1 10 12 1 9 12 1 1 100 1 10 10 12 10 12 1 9 12 1 1 10 12 1 1 10 12 10 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 10 12 10 12 1 1 100 1 10 12 1 100 1 10 10 12 1 1 10 12 1 1 100 1 10 100 1 10 10 12 1 1 10 12 1 1 100 1 10 100 1 10 10 12 1 1 10 12 1 1 100 1 10 100 1 10 10 100 12 1 1 1 10 12 1 1 100 1 10 100 1 10 10 100 12 1 1 1 10 12 1 1 100 1 10 100 1 10 10 12 1 1 10 12 1 1 10 12 10 7 12 1 1 1 9 12 1 1 7 10 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
staticfield java/nio/HeapByteBuffer ARRAY_BASE_OFFSET J 16
staticfield java/nio/HeapByteBuffer ARRAY_INDEX_SCALE J 1
staticfield java/nio/HeapByteBuffer $assertionsDisabled Z 1
ciMethod java/nio/HeapByteBuffer getShort ()S 780 0 12709 0 232
ciMethod java/nio/HeapByteBuffer getInt ()I 1024 0 11789 0 224
ciMethod java/nio/HeapByteBuffer <init> (IILjava/lang/foreign/MemorySegment;)V 1024 0 13740 0 1096
ciMethod java/nio/HeapByteBuffer byteOffset (J)J 972 0 24482 0 88
ciInstanceKlass java/util/Collections$SetFromMap 1 1 182 10 7 12 1 1 1 9 7 12 1 1 1 11 7 12 1 1 1 9 12 1 1 11 12 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 9 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 11 12 1 1 11 12 1 11 12 1 11 12 1 1 11 12 1 11 12 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 10 100 12 1 1 100 1 8 1 10 12 1 7 1 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 100 1 1 1 1 1 100 1 1 1
ciMethod java/util/Collections$SetFromMap add (Ljava/lang/Object;)Z 512 0 3147 0 0
ciInstanceKlass java/util/Deque 1 1 101 10 100 12 1 1 1 11 7 12 1 1 1 7 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1
ciInstanceKlass java/util/Queue 1 0 70 7 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 8 1
ciMethod java/util/Deque poll ()Ljava/lang/Object; 0 0 1 0 -1
instanceKlass org/springframework/boot/loader/net/protocol/jar/UrlJarEntry
instanceKlass org/springframework/boot/loader/jar/NestedJarFile$NestedJarEntry
instanceKlass java/util/jar/JarFile$JarFileEntry
ciInstanceKlass java/util/jar/JarEntry 1 1 60 10 7 12 1 1 1 10 12 1 10 7 1 9 12 1 1 9 12 1 1 9 12 1 1 10 100 12 1 1 10 100 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1
instanceKlass java/util/jar/JarEntry
ciInstanceKlass java/util/zip/ZipEntry 1 1 327 100 1 7 1 10 7 12 1 1 1 5 0 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 3 100 1 8 1 10 12 1 9 12 1 8 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 5 0 5 0 10 12 1 1 10 100 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 5 0 10 12 1 3 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 10 12 1 1 5 0 5 0 5 0 5 0 5 0 5 0 10 12 1 8 1 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 5 0 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 100 100 1 7 1 10 12 1 7 1 7 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/zip/ZipEntry getName ()Ljava/lang/String; 0 0 1 0 -1
ciInstanceKlass java/util/ArrayDeque 1 1 438 7 1 9 7 12 1 1 1 3 10 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 100 1 8 1 10 12 1 1 3 10 7 12 1 1 7 1 11 7 12 1 1 1 10 12 1 10 12 1 1 100 1 10 100 1 10 10 12 1 10 12 1 10 12 1 10 18 12 1 1 11 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 10 12 1 100 1 10 100 1 10 10 7 12 1 1 1 11 7 12 1 10 12 1 1 18 12 1 1 18 11 100 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 7 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 100 1 100 1 10 10 100 12 1 1 10 12 1 10 12 1 10 100 12 1 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 7 1 9 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 8 1 10 12 1 1 10 12 1 11 12 1 7 1 7 1 7 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 16 15 16 15 10 12 15 10 12 15 10 100 12 1 1 1 1 1 1 1 100 1 100 1 1
ciMethod java/util/ArrayDeque poll ()Ljava/lang/Object; 768 0 2346 0 0
ciMethod java/util/ArrayDeque pollFirst ()Ljava/lang/Object; 486 0 4780 0 0
ciMethod java/util/ArrayDeque elementAt ([Ljava/lang/Object;I)Ljava/lang/Object; 514 0 4797 0 0
ciMethod java/util/ArrayDeque inc (II)I 512 0 4958 0 0
ciInstanceKlass java/lang/invoke/BoundMethodHandle$Species_L 1 1 126 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 12 1 9 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 100 1 10 100 12 1 1 1 9 12 1 10 12 1 9 12 1 10 12 1 9 12 1 10 12 1 9 12 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/nio/HeapCharBuffer 1 1 253 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 10 12 1 1 10 12 1 9 12 1 1 9 12 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 10 100 12 1 1 1 10 12 1 10 12 1 100 1 10 10 12 1 1 10 12 1 11 100 12 1 1 10 12 1 100 1 10 12 1 1 100 1 10 100 1 10 10 12 1 1 10 12 1 1 10 12 10 9 12 1 1 100 1 10 10 12 10 12 1 10 12 1 100 1 100 1 10 10 100 12 1 1 1 10 12 1 10 10 10 12 1 1 10 12 1 1 10 12 1 10 12 10 7 12 1 1 1 9 12 1 1 7 10 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1
staticfield java/nio/HeapCharBuffer ARRAY_BASE_OFFSET J 16
staticfield java/nio/HeapCharBuffer ARRAY_INDEX_SCALE J 2
staticfield java/nio/HeapCharBuffer $assertionsDisabled Z 1
ciInstanceKlass java/nio/file/StandardOpenOption 1 1 85 7 1 9 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 8 10 8 8 8 8 8 8 8 8 8 10 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/nio/file/StandardOpenOption READ Ljava/nio/file/StandardOpenOption; java/nio/file/StandardOpenOption
staticfield java/nio/file/StandardOpenOption WRITE Ljava/nio/file/StandardOpenOption; java/nio/file/StandardOpenOption
staticfield java/nio/file/StandardOpenOption APPEND Ljava/nio/file/StandardOpenOption; java/nio/file/StandardOpenOption
staticfield java/nio/file/StandardOpenOption TRUNCATE_EXISTING Ljava/nio/file/StandardOpenOption; java/nio/file/StandardOpenOption
staticfield java/nio/file/StandardOpenOption CREATE Ljava/nio/file/StandardOpenOption; java/nio/file/StandardOpenOption
staticfield java/nio/file/StandardOpenOption CREATE_NEW Ljava/nio/file/StandardOpenOption; java/nio/file/StandardOpenOption
staticfield java/nio/file/StandardOpenOption DELETE_ON_CLOSE Ljava/nio/file/StandardOpenOption; java/nio/file/StandardOpenOption
staticfield java/nio/file/StandardOpenOption SPARSE Ljava/nio/file/StandardOpenOption; java/nio/file/StandardOpenOption
staticfield java/nio/file/StandardOpenOption SYNC Ljava/nio/file/StandardOpenOption; java/nio/file/StandardOpenOption
staticfield java/nio/file/StandardOpenOption DSYNC Ljava/nio/file/StandardOpenOption; java/nio/file/StandardOpenOption
staticfield java/nio/file/StandardOpenOption $VALUES [Ljava/nio/file/StandardOpenOption; 10 [Ljava/nio/file/StandardOpenOption;
ciInstanceKlass java/util/zip/ZipUtils 1 1 331 7 1 7 1 100 1 10 7 12 1 1 1 5 0 5 0 9 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 5 0 5 0 5 0 5 0 5 0 5 0 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 5 0 10 12 1 1 10 12 1 1 5 0 100 1 5 0 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 100 1 9 12 1 10 12 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 8 1 10 12 1 1 8 1 1 1 1 5 0 1 5 0 1 1 3 1 3 1 5 0 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/zip/ZipUtils NIO_ACCESS Ljdk/internal/access/JavaNioAccess; java/nio/Buffer$2
staticfield java/util/zip/ZipUtils defaultBuf Ljava/nio/ByteBuffer; java/nio/HeapByteBuffer
staticfield java/util/zip/ZipUtils unsafe Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/util/zip/ZipUtils byteBufferArrayOffset J 48
staticfield java/util/zip/ZipUtils byteBufferOffsetOffset J 40
instanceKlass org/springframework/boot/loader/jar/ZipInflaterInputStream
instanceKlass java/util/zip/ZipFile$ZipFileInflaterInputStream
ciInstanceKlass java/util/zip/InflaterInputStream 1 1 180 9 7 12 1 1 1 100 1 8 1 10 12 1 1 10 7 12 1 1 9 12 1 9 12 1 9 12 1 1 100 1 10 12 1 100 1 8 1 10 9 12 1 1 9 12 1 10 12 1 7 1 10 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 1 100 1 8 1 10 8 1 7 1 5 0 10 100 12 1 1 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 9 12 1 1 100 1 8 1 10 10 12 1 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/zip/InflaterInputStream read ([BII)I 768 0 5653 0 -1
ciMethod java/util/zip/InflaterInputStream fill ()V 768 0 2214 0 -1
ciMethod java/util/zip/InflaterInputStream <init> (Ljava/io/InputStream;Ljava/util/zip/Inflater;I)V 512 0 2340 0 0
ciMethod java/util/zip/InflaterInputStream ensureOpen ()V 514 0 7867 0 -1
ciInstanceKlass java/util/zip/Inflater 1 1 280 10 7 12 1 1 1 9 7 12 1 1 1 9 7 12 1 1 7 1 10 12 1 1 10 12 1 9 12 1 1 10 12 1 9 7 12 1 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 10 7 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 11 100 12 1 1 1 100 1 11 10 12 1 1 11 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 10 12 1 1 100 1 9 12 1 10 12 1 1 9 12 1 1 9 12 1 9 12 1 5 0 9 12 1 10 12 1 1 10 12 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 7 12 1 1 1 100 1 10 100 1 8 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/zip/Inflater $assertionsDisabled Z 1
ciMethod java/util/zip/Inflater inflate ([BII)I 620 0 6465 0 -1
ciMethod java/util/zip/Inflater finished ()Z 768 0 6428 0 -1
ciMethod java/util/zip/Inflater needsInput ()Z 512 0 5385 0 -1
ciMethod java/util/zip/Inflater needsDictionary ()Z 512 0 5692 0 -1
ciMethod java/util/zip/Inflater hasPendingOutput ()Z 258 0 129 0 -1
ciMethod java/util/zip/Inflater <init> (Z)V 282 0 150 0 0
ciMethod java/util/zip/Inflater init (Z)J 300 0 150 0 -1
ciInstanceKlass java/util/zip/Inflater$InflaterZStreamRef 1 1 60 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 11 7 12 1 1 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/zip/Inflater$InflaterZStreamRef <init> (Ljava/util/zip/Inflater;J)V 300 0 150 0 0
ciMethod java/util/Set add (Ljava/lang/Object;)Z 0 0 1 0 -1
ciMethod jdk/internal/ref/CleanerImpl$PhantomCleanableRef <init> (Ljava/lang/Object;Ljava/lang/ref/Cleaner;Ljava/lang/Runnable;)V 772 0 5533 0 0
ciMethod jdk/internal/ref/PhantomCleanable <init> (Ljava/lang/Object;Ljava/lang/ref/Cleaner;)V 514 0 5544 0 0
ciMethod jdk/internal/ref/PhantomCleanable insert ()V 514 0 5544 0 0
ciMethod jdk/internal/ref/CleanerImpl getCleanerImpl (Ljava/lang/ref/Cleaner;)Ljdk/internal/ref/CleanerImpl; 524 0 11090 0 0
ciMethod java/lang/Integer toUnsignedLong (I)J 520 0 5198 0 0
ciMethod java/lang/Integer valueOf (I)Ljava/lang/Integer; 148 0 88026 0 216
ciMethod java/lang/Integer reverseBytes (I)I 0 0 118 0 -1
ciMethod java/lang/Integer <init> (I)V 534 0 13045 0 -1
ciMethod jdk/internal/misc/Unsafe convEndian (ZS)S 1024 0 13733 0 0
ciMethod jdk/internal/misc/Unsafe convEndian (ZI)I 1024 0 11785 0 0
ciMethod jdk/internal/misc/Unsafe getIntUnaligned (Ljava/lang/Object;JZ)I 1024 0 11785 0 128
ciMethod jdk/internal/misc/Unsafe getIntUnaligned (Ljava/lang/Object;J)I 1024 0 928 0 -1
ciMethod jdk/internal/misc/Unsafe getShortUnaligned (Ljava/lang/Object;J)S 1024 0 959 0 -1
ciMethod jdk/internal/misc/Unsafe getShortUnaligned (Ljava/lang/Object;JZ)S 824 0 13733 0 136
ciMethod jdk/internal/misc/Unsafe allocateInstance (Ljava/lang/Class;)Ljava/lang/Object; 1026 0 513 0 -1
ciMethod java/io/FilterInputStream <init> (Ljava/io/InputStream;)V 514 0 2487 0 0
ciMethod java/io/InputStream close ()V 0 0 1 0 -1
ciMethod java/io/InputStream <init> ()V 516 0 6710 0 0
ciMethod java/io/InputStream read ([BII)I 0 0 1 0 -1
ciMethod java/util/Map put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 0 0 1 0 -1
ciMethod java/lang/Object <init> ()V 1024 0 777313 0 80
ciInstanceKlass org/springframework/boot/loader/net/protocol/jar/JarUrlConnection 1 1 467 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 100 1 5 0 10 7 12 1 1 10 100 1 5 0 9 12 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 7 12 1 1 1 100 1 10 12 1 10 12 1 1 10 100 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 8 1 10 12 1 10 12 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 7 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 9 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 11 100 12 1 1 7 1 10 12 1 18 12 1 1 10 10 12 1 8 1 10 7 12 1 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 8 1 10 12 1 10 12 1 1 8 1 10 12 10 12 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 10 12 9 12 1 1 18 12 1 10 12 1 10 12 1 10 12 7 1 10 12 1 8 1 8 1 8 1 7 1 10 10 12 1 18 12 1 100 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 8 1 15 10 7 12 1 1 1 16 15 10 12 16 15 10 12 1 1 1 100 1 100 1 1
staticfield org/springframework/boot/loader/net/protocol/jar/JarUrlConnection jarFiles Lorg/springframework/boot/loader/net/protocol/jar/UrlJarFiles; org/springframework/boot/loader/net/protocol/jar/UrlJarFiles
staticfield org/springframework/boot/loader/net/protocol/jar/JarUrlConnection emptyInputStream Ljava/io/InputStream; java/io/ByteArrayInputStream
staticfield org/springframework/boot/loader/net/protocol/jar/JarUrlConnection FILE_NOT_FOUND_EXCEPTION Ljava/io/FileNotFoundException; java/io/FileNotFoundException
staticfield org/springframework/boot/loader/net/protocol/jar/JarUrlConnection NOT_FOUND_URL Ljava/net/URL; java/net/URL
staticfield org/springframework/boot/loader/net/protocol/jar/JarUrlConnection NOT_FOUND_CONNECTION Lorg/springframework/boot/loader/net/protocol/jar/JarUrlConnection; org/springframework/boot/loader/net/protocol/jar/JarUrlConnection
instanceKlass org/springframework/boot/loader/net/protocol/jar/JarUrlConnection$ConnectionInputStream
ciInstanceKlass org/springframework/boot/loader/net/protocol/jar/LazyDelegatingInputStream 1 1 76 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1
ciInstanceKlass org/springframework/boot/loader/net/protocol/jar/JarUrlConnection$ConnectionInputStream 1 1 57 9 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 9 12 1 1 10 7 1 9 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1
instanceKlass org/springframework/boot/loader/net/protocol/jar/UrlNestedJarFile
ciInstanceKlass org/springframework/boot/loader/jar/NestedJarFile 1 1 531 7 1 9 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 1 9 12 1 7 1 10 12 1 9 12 1 1 11 12 1 1 9 12 1 1 10 7 12 1 1 1 18 12 1 1 8 1 18 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 100 1 10 12 1 1 10 7 12 1 1 1 11 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 7 1 18 12 1 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 18 11 100 12 1 1 1 18 18 12 1 1 11 12 1 1 11 12 1 18 18 100 1 10 10 100 12 1 1 1 10 7 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 100 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 1 10 12 1 1 10 12 1 1 10 12 1 1 8 10 7 12 1 1 1 10 12 1 1 10 12 1 9 12 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 8 1 10 12 1 9 12 1 10 12 1 1 7 1 10 12 10 12 1 10 7 12 1 1 7 1 10 12 1 1 100 1 10 12 1 9 12 1 1 18 12 1 8 1 10 12 1 1 10 12 1 1 10 100 1 10 12 1 100 1 8 1 10 7 1 10 12 1 7 1 10 12 1 100 1 10 12 1 10 12 1 10 9 12 1 1 11 7 12 1 1 100 1 8 1 10 8 1 10 12 1 10 10 12 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 7 12 1 1 8 1 8 1 15 10 7 12 1 1 1 16 1 15 10 12 16 15 10 12 16 15 10 12 16 16 15 10 12 1 16 15 10 12 16 16 1 15 10 12 1 16 1 100 1 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield org/springframework/boot/loader/jar/NestedJarFile BASE_VERSION I 8
staticfield org/springframework/boot/loader/jar/NestedJarFile debug Lorg/springframework/boot/loader/log/DebugLogger; org/springframework/boot/loader/log/DebugLogger$DisabledDebugLogger
ciInstanceKlass org/springframework/boot/loader/net/protocol/jar/UrlNestedJarFile 1 1 105 10 7 12 1 1 1 7 1 18 12 1 1 10 12 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 15 10 7 12 1 1 1 16 15 10 12 1 100 1 100 1 1 100 1 1 100 1 100 1 1
ciInstanceKlass org/springframework/boot/loader/ref/Cleaner 1 1 31 9 7 12 1 1 1 9 7 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield org/springframework/boot/loader/ref/Cleaner instance Lorg/springframework/boot/loader/ref/Cleaner; org/springframework/boot/loader/ref/DefaultCleaner
ciInstanceKlass org/springframework/boot/loader/ref/DefaultCleaner 1 1 61 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 9 12 1 1 11 100 12 1 1 1 10 9 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1
staticfield org/springframework/boot/loader/ref/DefaultCleaner instance Lorg/springframework/boot/loader/ref/DefaultCleaner; org/springframework/boot/loader/ref/DefaultCleaner
ciInstanceKlass org/springframework/boot/loader/jar/NestedJarFile$JarEntryInputStream 1 1 140 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 9 12 1 1 11 7 12 1 1 9 12 1 10 12 1 10 12 1 1 10 12 1 10 100 12 1 1 1 100 1 5 0 3 9 7 12 1 1 1 9 100 1 8 1 10 12 1 11 9 12 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 100 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1
instanceKlass org/springframework/boot/loader/jar/NestedJarFile$JarEntryInflaterInputStream
ciInstanceKlass org/springframework/boot/loader/jar/ZipInflaterInputStream 1 1 81 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 5 0 5 0 5 0 5 0 10 12 1 10 12 1 1 10 12 1 1 100 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
ciInstanceKlass org/springframework/boot/loader/jar/NestedJarFile$JarEntryInflaterInputStream 1 1 93 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 9 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 11 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass org/springframework/boot/loader/jar/NestedJarFile$NestedJarEntry 1 1 252 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 5 0 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 10 100 12 1 1 1 10 10 100 12 1 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 18 12 1 1 10 100 12 1 1 1 9 12 1 1 18 10 12 1 1 10 100 1 10 12 1 1 10 10 12 1 1 10 10 12 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 1 10 10 12 1 7 1 8 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 1 15 10 12 1 1 16 15 10 16 1 1 1 1 100 1 100 1 1
staticfield org/springframework/boot/loader/jar/NestedJarFile$NestedJarEntry CANNOT_BE_MODIFIED_EXCEPTION Ljava/lang/IllegalStateException; java/lang/IllegalStateException
instanceKlass org/springframework/boot/loader/log/DebugLogger$SystemErrDebugLogger
instanceKlass org/springframework/boot/loader/log/DebugLogger$DisabledDebugLogger
ciInstanceKlass org/springframework/boot/loader/log/DebugLogger 1 1 65 10 7 12 1 1 1 9 7 12 1 1 1 100 1 10 12 1 8 1 10 7 12 1 1 1 7 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield org/springframework/boot/loader/log/DebugLogger disabled Lorg/springframework/boot/loader/log/DebugLogger; org/springframework/boot/loader/log/DebugLogger$DisabledDebugLogger
ciInstanceKlass org/springframework/boot/loader/log/DebugLogger$DisabledDebugLogger 1 1 33 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass org/springframework/boot/loader/jar/NestedJarFileResources 1 1 235 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 9 7 12 1 1 1 10 12 1 9 12 1 11 7 12 1 1 1 11 12 1 18 12 1 1 11 7 12 1 1 1 7 1 10 12 1 11 12 1 1 10 12 1 11 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 18 12 1 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 11 12 1 100 1 10 12 1 100 1 10 12 1 1 11 12 1 10 10 12 1 1 10 12 1 1 100 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 7 12 1 1 1 16 15 10 12 16 1 15 16 1 1 100 1 100 1 1
ciInstanceKlass org/springframework/boot/loader/zip/ZipContent 1 1 404 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 100 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 10 12 1 10 7 12 1 1 1 100 1 10 12 1 1 100 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 7 1 7 1 10 18 12 1 1 11 12 1 1 10 12 1 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 10 12 1 9 12 1 1 11 12 1 9 12 1 1 8 1 10 7 12 1 1 1 8 1 10 7 12 1 11 12 1 1 8 1 10 8 1 10 7 12 1 1 10 12 1 11 7 12 1 8 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 100 1 1 1 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 15 10 7 12 1 1 1 16 15 10 12 16 1 1 1 1 1 1 100 1 100 1 1
staticfield org/springframework/boot/loader/zip/ZipContent SIGNATURE_SUFFIX [B 4
staticfield org/springframework/boot/loader/zip/ZipContent debug Lorg/springframework/boot/loader/log/DebugLogger; org/springframework/boot/loader/log/DebugLogger$DisabledDebugLogger
staticfield org/springframework/boot/loader/zip/ZipContent cache Ljava/util/Map; java/util/concurrent/ConcurrentHashMap
ciInstanceKlass org/springframework/boot/loader/zip/DataBlock 1 1 50 11 7 12 1 1 1 100 1 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass org/springframework/boot/loader/zip/CloseableDataBlock 1 0 29 100 1 100 1 100 1 100 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1
ciInstanceKlass org/springframework/boot/loader/zip/FileDataBlock 1 1 178 10 7 12 1 1 1 7 1 10 12 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 100 1 8 1 10 12 1 18 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 100 1 5 0 3 10 12 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 1 8 1 8 1 9 12 1 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 9 7 12 1 1 1 9 12 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1 15 10 7 12 1 1 1 16 1 15 10 7 1 16 1 1 1 1 100 1 100 1 1
staticfield org/springframework/boot/loader/zip/FileDataBlock debug Lorg/springframework/boot/loader/log/DebugLogger; org/springframework/boot/loader/log/DebugLogger$DisabledDebugLogger
ciInstanceKlass org/springframework/boot/loader/zip/FileDataBlock$Tracker 1 1 30 7 1 10 12 1 1 9 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
staticfield org/springframework/boot/loader/zip/FileDataBlock$Tracker NONE Lorg/springframework/boot/loader/zip/FileDataBlock$Tracker; org/springframework/boot/loader/zip/FileDataBlock$Tracker$1
ciInstanceKlass org/springframework/boot/loader/zip/FileDataBlock$FileAccess 1 1 269 10 7 12 1 1 1 5 0 9 7 12 1 1 1 9 12 1 1 7 1 10 7 12 1 1 1 100 1 10 100 12 1 1 1 18 12 1 1 10 12 1 9 12 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 10 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 100 1 9 12 1 1 100 1 11 100 12 1 1 1 8 1 10 12 1 9 7 12 1 1 1 11 7 12 1 1 1 10 12 1 10 12 1 10 12 1 11 12 1 7 1 9 7 12 1 1 1 10 12 1 1 9 12 1 9 12 1 1 8 1 10 7 12 1 1 1 10 12 1 8 1 10 7 12 1 1 10 12 1 8 1 10 12 1 10 8 1 11 100 12 1 1 1 100 1 11 12 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 8 1 1 1 1 100 1 100 1 1
ciInstanceKlass  @bci org/springframework/boot/loader/zip/FileDataBlock read (Ljava/nio/ByteBuffer;J)I 17 <appendix> argL0 ; 1 1 18 1 7 1 7 1 100 1 1 12 10 1 1 1 100 10 1 1
ciInstanceKlass org/springframework/boot/loader/zip/ZipCentralDirectoryFileHeaderRecord 1 1 404 7 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 1 10 12 10 12 10 100 12 1 1 1 10 12 10 100 12 1 1 1 10 12 10 12 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 10 12 1 10 12 10 12 1 5 0 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 1 10 12 1 1 3 10 12 1 10 12 1 1 9 12 1 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 100 1 18 12 1 1 10 12 10 12 1 18 12 1 1 18 12 1 1 18 12 1 1 10 12 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 8 1 15 10 100 12 1 1 1 8 1 15 15 15 15 15 15 15 15 15 15 15 15 15 15 15 15 1 100 1 100 1 1
staticfield org/springframework/boot/loader/zip/ZipCentralDirectoryFileHeaderRecord debug Lorg/springframework/boot/loader/log/DebugLogger; org/springframework/boot/loader/log/DebugLogger$DisabledDebugLogger
ciInstanceKlass org/springframework/boot/loader/zip/ZipContent$Entry 1 1 236 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 11 100 12 1 1 10 12 1 10 7 12 1 1 1 7 1 5 0 9 12 1 1 10 12 1 1 10 7 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 9 12 1 10 12 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 5 0 100 1 8 1 10 12 1 18 12 1 1 10 12 1 1 11 100 12 1 1 100 1 10 12 1 1 100 1 10 12 1 11 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 12 16 1 1 1 100 1 100 1 1
ciInstanceKlass org/springframework/boot/loader/zip/ZipLocalFileHeaderRecord 1 1 205 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 1 10 12 10 12 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 3 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 18 12 1 1 10 12 1 10 12 1 18 12 1 1 18 12 1 1 18 12 1 1 10 12 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 8 1 15 10 100 12 1 1 1 8 1 15 15 15 15 15 15 15 15 15 15 1 100 1 100 1 1
staticfield org/springframework/boot/loader/zip/ZipLocalFileHeaderRecord debug Lorg/springframework/boot/loader/log/DebugLogger; org/springframework/boot/loader/log/DebugLogger$DisabledDebugLogger
ciInstanceKlass  @bci org/springframework/boot/loader/jar/NestedJarFileResources createInflatorCleanupAction (Ljava/util/zip/Inflater;)Ljava/lang/Runnable; 2 <appendix> member <vmtarget> ; 1 1 29 1 7 1 7 1 100 1 1 1 1 1 1 1 12 10 12 9 12 9 1 1 7 1 1 12 10 1 1
instanceKlass com/google/gson/stream/MalformedJsonException
instanceKlass java/nio/charset/CharacterCodingException
instanceKlass java/io/CharConversionException
instanceKlass java/net/SocketException
instanceKlass java/net/UnknownHostException
instanceKlass java/net/ProtocolException
instanceKlass java/nio/file/FileSystemException
instanceKlass java/io/ObjectStreamException
instanceKlass com/fasterxml/jackson/core/JacksonException
instanceKlass java/io/UnsupportedEncodingException
instanceKlass java/nio/channels/ClosedChannelException
instanceKlass java/io/EOFException
instanceKlass java/util/zip/ZipException
instanceKlass java/io/FileNotFoundException
instanceKlass java/net/MalformedURLException
ciInstanceKlass java/io/IOException 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/concurrent/CancellationException
ciInstanceKlass java/lang/IllegalStateException 1 1 35 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/net/HttpURLConnection
instanceKlass org/springframework/boot/loader/net/protocol/nested/NestedUrlConnection
instanceKlass java/net/JarURLConnection
ciInstanceKlass java/net/URLConnection 1 1 659 9 7 12 1 1 1 100 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 100 1 8 1 10 12 1 9 12 1 1 9 12 1 10 7 1 9 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 5 0 8 1 5 0 10 12 1 1 8 1 10 12 1 1 8 1 8 1 10 12 1 8 1 8 1 10 100 12 1 1 1 10 12 1 1 100 1 10 100 12 1 1 1 10 100 12 1 1 100 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 9 100 12 1 1 1 100 1 8 1 10 8 1 100 1 10 10 12 1 1 10 7 12 1 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 9 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 100 1 8 1 10 10 12 1 10 12 1 8 1 9 12 1 1 10 7 1 11 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 9 100 12 1 1 1 9 12 1 100 1 10 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 100 1 8 1 10 12 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 10 12 1 1 100 1 10 12 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 8 1 8 1 10 100 12 1 1 1 8 1 10 12 1 1 11 100 12 1 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 5 0 10 12 1 1 10 12 1 1 5 0 5 0 5 0 5 0 5 0 10 12 1 10 12 1 1 100 1 8 1 10 10 12 1 10 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1
staticfield java/net/URLConnection defaultCaching Ljava/util/concurrent/ConcurrentHashMap; java/util/concurrent/ConcurrentHashMap
staticfield java/net/URLConnection handlers Ljava/util/Hashtable; java/util/Hashtable
staticfield java/net/URLConnection $assertionsDisabled Z 1
instanceKlass org/springframework/boot/loader/net/protocol/jar/JarUrlConnection
ciInstanceKlass java/net/JarURLConnection 1 1 144 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 8 1 10 7 12 1 1 1 100 1 100 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 9 12 1 1 8 1 10 12 1 10 12 1 1 8 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 10 100 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 100 1 1 1
ciInstanceKlass java/util/zip/ZipException 0 0 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/io/EOFException 0 0 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
instanceKlass sun/nio/ch/FileChannelImpl
ciInstanceKlass java/nio/channels/FileChannel 1 1 153 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 100 1 10 100 1 5 0 10 12 1 1 10 12 1 10 12 1 1 10 12 1 7 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 100 1 1
staticfield java/nio/channels/FileChannel NO_ATTRIBUTES [Ljava/nio/file/attribute/FileAttribute; 0 [Ljava/nio/file/attribute/FileAttribute;
ciInstanceKlass java/lang/foreign/MemorySegment$Scope 0 0 31 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/foreign/MemorySessionImpl 0 0 326 100 1 100 1 10 12 1 1 11 100 12 1 1 1 100 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 100 1 10 12 1 1 10 12 1 9 12 1 1 10 100 12 1 1 10 100 12 1 9 12 1 1 100 1 10 12 1 100 1 10 100 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 11 100 12 1 1 10 12 1 10 100 12 1 1 1 9 12 1 1 9 12 1 10 12 1 100 1 10 12 1 1 100 1 10 12 1 1 100 1 10 10 12 1 10 100 1 10 12 1 100 1 8 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 1 8 1 100 1 8 1 10 100 1 8 1 10 9 12 1 1 18 12 1 1 10 12 1 18 10 100 12 1 1 1 8 9 12 1 1 10 100 12 1 1 1 9 12 1 1 100 1 100 1 10 12 1 100 1 1 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 16 15 10 12 16 15 10 12 15 10 100 12 1 1 1 1 100 1 1 1 1 100 1 1 1
ciInstanceKlass java/lang/foreign/MemorySegment 0 0 449 10 100 12 1 1 1 11 100 12 1 1 1 11 12 1 11 100 12 1 1 1 11 11 12 1 1 11 12 1 1 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 11 12 1 11 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 10 100 12 1 1 10 100 12 1 1 10 100 12 1 1 10 100 12 1 1 10 100 12 1 1 10 100 12 1 1 1 9 100 12 1 1 1 11 12 1 10 100 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 100 1 10 10 12 1 10 12 1 100 1 10 10 12 1 10 12 1 100 1 10 10 12 1 10 12 1 100 1 10 10 12 1 10 12 1 100 1 10 10 12 1 10 12 1 100 1 10 10 12 1 10 12 1 100 1 10 10 12 1 10 12 1 100 1 10 10 12 1 10 12 1 8 1 10 12 1 1 11 100 1 11 100 1 11 100 1 11 100 1 11 100 1 11 100 1 11 100 1 11 100 1 11 100 1 10 12 1 10 12 1 10 10 12 1 1 9 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/nio/BufferUnderflowException 0 0 21 10 100 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1
ciInstanceKlass java/util/zip/DataFormatException 0 0 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciMethodData java/lang/Object <init> ()V 2 776805 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 4 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/Objects requireNonNull (Ljava/lang/Object;)Ljava/lang/Object; 2 70976 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 15 0x10007 0x1153f 0x30 0x0 0x80002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethod java/io/IOException <init> (Ljava/lang/String;)V 2 0 258 0 -1
ciMethodData java/lang/ref/Reference reachabilityFence (Ljava/lang/Object;)V 2 151173 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 4 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/nio/Buffer session ()Ljdk/internal/foreign/MemorySessionImpl; 2 106493 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 27 0x40007 0x19ffd 0x90 0x0 0xb0004 0x0 0x0 0x0 0x0 0x0 0x0 0xe0005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/Objects requireNonNull (Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object; 2 29921 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x10007 0x74e1 0x30 0x0 0x90002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0xffffffffffffffff oops 0 methods 0
ciMethodData java/lang/Record <init> ()V 2 16124 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x10002 0x3efc 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/ref/Reference <init> (Ljava/lang/Object;Ljava/lang/ref/ReferenceQueue;)V 2 18770 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 20 0x10002 0x4952 0xb0007 0x2f17 0x38 0x1a3d 0x110003 0x1a3d 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x6 0x0 0x0 oops 0 methods 0
ciMethod java/util/zip/ZipException <init> (Ljava/lang/String;)V 0 0 1 0 -1
ciMethod java/nio/channels/FileChannel open (Ljava/nio/file/Path;[Ljava/nio/file/OpenOption;)Ljava/nio/channels/FileChannel; 2 0 1 0 -1
ciMethodData java/util/ArrayDeque inc (II)I 2 4702 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x50007 0x1176 0x20 0xe8 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/nio/Buffer nextGetIndex (I)I 2 23992 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0xc0007 0x5db8 0x30 0x0 0x130002 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/nio/HeapByteBuffer byteOffset (J)J 2 23996 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/nio/HeapByteBuffer getShort ()S 2 12319 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 42 0x40005 0x301f 0x0 0x0 0x0 0x0 0x0 0xe0005 0x301f 0x0 0x0 0x0 0x0 0x0 0x120005 0x301f 0x0 0x0 0x0 0x0 0x0 0x19000b 0x0 0x0 0x29efd7ec158 0x301f 0x0 0x0 0x4 0x1 0x1 0x2 0x29efc2efb18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 2 24 jdk/internal/misc/ScopedMemoryAccess 32 [B methods 0
ciMethodData jdk/internal/misc/ScopedMemoryAccess getShortUnaligned (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)S 2 12297 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 33 0x6000b 0x3009 0x0 0x0 0x0 0x0 0x0 0x4 0x1 0x1 0x2 0x29efc2efb18 0xe0005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x6 0x0 0x0 0x0 0x0 0x0 0x0 oops 1 11 [B methods 0
ciMethodData jdk/internal/misc/ScopedMemoryAccess getShortUnalignedInternal (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)S 2 12297 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 39 0x10007 0x3009 0x58 0x0 0x50005 0x0 0x0 0x0 0x0 0x0 0x0 0xf000b 0x3009 0x0 0x0 0x0 0x0 0x0 0x2 0x1 0x29efc2efb18 0x150002 0x300a 0x1e0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x6 0x0 0x0 0x0 0x0 0x0 0x0 oops 1 20 [B methods 0
ciMethodData jdk/internal/misc/Unsafe getShortUnaligned (Ljava/lang/Object;JZ)S 2 13321 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 25 0x5000b 0x223 0x0 0x0 0x0 0x0 0x0 0x2 0x1 0x29efc2efb18 0x80002 0x3409 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0x0 0x0 0x0 0x0 0x0 oops 1 9 [B methods 0
ciMethodData jdk/internal/misc/Unsafe convEndian (ZS)S 2 13221 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 19 0x8000000600040007 0x1204 0x38 0x21a4 0x80003 0x21a4 0x28 0xc0002 0x1204 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/nio/Buffer remaining ()I 2 45275 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0xb0007 0x1 0x38 0xb0db 0xf0003 0xb0db 0x18 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/nio/HeapByteBuffer getInt ()I 2 11277 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 42 0x40005 0x2c0e 0x0 0x0 0x0 0x0 0x0 0xe0005 0x2c0e 0x0 0x0 0x0 0x0 0x0 0x120005 0x2c0e 0x0 0x0 0x0 0x0 0x0 0x19000b 0x0 0x0 0x29efd7ec158 0x2c0e 0x0 0x0 0x4 0x1 0x1 0x2 0x29efc2efb18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 2 24 jdk/internal/misc/ScopedMemoryAccess 32 [B methods 0
ciMethodData jdk/internal/misc/ScopedMemoryAccess getIntUnaligned (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)I 2 11277 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 33 0x6000b 0x2c0e 0x0 0x0 0x0 0x0 0x0 0x4 0x1 0x1 0x2 0x29efc2efb18 0xe0005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x6 0x0 0x0 0x0 0x0 0x0 0x0 oops 1 11 [B methods 0
ciMethodData jdk/internal/misc/ScopedMemoryAccess getIntUnalignedInternal (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)I 2 11277 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 39 0x10007 0x2c0e 0x58 0x0 0x50005 0x0 0x0 0x0 0x0 0x0 0x0 0xf000b 0x2c0e 0x0 0x0 0x0 0x0 0x0 0x2 0x1 0x29efc2efb18 0x150002 0x2c0f 0x1e0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x6 0x0 0x0 0x0 0x0 0x0 0x0 oops 1 20 [B methods 0
ciMethodData jdk/internal/misc/Unsafe getIntUnaligned (Ljava/lang/Object;JZ)I 2 11273 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 25 0x5000b 0x177 0x0 0x0 0x0 0x0 0x0 0x2 0x1 0x29efc2efb18 0x80002 0x2c0a 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0x0 0x0 0x0 0x0 0x0 oops 1 9 [B methods 0
ciMethodData jdk/internal/misc/Unsafe convEndian (ZI)I 2 11273 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 19 0x8000000600040007 0x76 0x38 0x2b95 0x80003 0x2b95 0x28 0xc0002 0x76 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/nio/Buffer position (I)Ljava/nio/Buffer; 2 8206 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 39 0x50007 0x200f 0x38 0x0 0x90003 0x0 0x18 0xe0007 0x200f 0x38 0x0 0x120003 0x0 0x18 0x170007 0x200f 0x58 0x0 0x1c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x250007 0x200f 0x20 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/nio/ByteBuffer position (I)Ljava/nio/ByteBuffer; 2 16388 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x20002 0x4005 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/nio/Buffer limit (I)Ljava/nio/Buffer; 2 12288 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 43 0x50007 0x3001 0x38 0x0 0x90003 0x0 0x18 0xe0007 0x3001 0x38 0x0 0x120003 0x0 0x18 0x170007 0x3001 0x58 0x0 0x1c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x80000006002a0007 0x3001 0x20 0x2 0x370007 0x3003 0x20 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/nio/ByteBuffer limit (I)Ljava/nio/ByteBuffer; 2 12617 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x20002 0x314a 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/nio/ByteBuffer allocate (I)Ljava/nio/ByteBuffer; 2 13541 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x10007 0x34e5 0x30 0x0 0x50002 0x0 0x100002 0x34e5 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethod jdk/internal/foreign/MemorySessionImpl checkValidStateRaw ()V 0 0 1 0 -1
ciMethodData java/nio/ByteOrder nativeOrder ()Ljava/nio/ByteOrder; 2 30904 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 4 0x0 0x0 0x9 0x0 oops 0 methods 0
ciMethodData java/nio/Buffer hasRemaining ()Z 2 13127 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0x80007 0x331b 0x38 0x2d 0xc0003 0x2d 0x18 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/nio/Buffer <init> (IIIILjava/lang/foreign/MemorySegment;)V 2 16669 orig 80 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 5 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 90 0x10002 0x411e 0x100007 0x411e 0x30 0x0 0x150002 0x0 0x8000000400270005 0x58 0x0 0x29efcfcdd38 0x40b8 0x29efe18b090 0x14 0x2d0005 0x58 0x0 0x29efcfcdd38 0x40b7 0x29efe18b090 0x14 0x320007 0x4121 0x1b0 0x0 0x370007 0x0 0x190 0x0 0x420002 0x0 0x470005 0x0 0x0 0x0 0x0 0x0 0x0 0x4b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x500005 0x0 0x0 0x0 0x0 0x0 0x0 0x540005 0x0 0x0 0x0 0x0 0x0 0x0 0x590005 0x0 0x0 0x0 0x0 0x0 0x0 0x5c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x5f0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x6 0xffffffffffffffff 0x0 0x0 0x0 0x0 0x0 oops 4 11 java/nio/HeapByteBuffer 13 java/nio/HeapCharBuffer 18 java/nio/HeapByteBuffer 20 java/nio/HeapCharBuffer methods 0
ciMethodData java/nio/ByteBuffer <init> (IIII[BILjava/lang/foreign/MemorySegment;)V 2 16895 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 27 0x80002 0x41ff 0x110002 0x41ff 0x170007 0x41fe 0x38 0x0 0x1b0003 0x0 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x8 0xffffffffffffffff 0x0 0x0 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/nio/ByteBuffer order (Ljava/nio/ByteOrder;)Ljava/nio/ByteBuffer; 2 13642 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 33 0x8000000600050007 0x3548 0x38 0x4 0x90003 0x4 0x18 0x150002 0x354c 0x1b0007 0x354c 0x38 0x0 0x1f0003 0x0 0x18 0x230007 0x4 0x38 0x3548 0x270003 0x3548 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/nio/HeapByteBuffer <init> (IILjava/lang/foreign/MemorySegment;)V 2 13228 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 12 0xa0002 0x33ad 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/nio/ByteBuffer limit (I)Ljava/nio/Buffer; 2 12521 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x20005 0x0 0x0 0x29efcfcdd38 0x30ea 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 1 3 java/nio/HeapByteBuffer methods 0
ciMethodData java/nio/ByteBuffer position (I)Ljava/nio/Buffer; 2 12521 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x20005 0x0 0x0 0x29efcfcdd38 0x30ea 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 1 3 java/nio/HeapByteBuffer methods 0
ciMethodData java/nio/ByteBuffer rewind ()Ljava/nio/ByteBuffer; 2 12843 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x10002 0x322c 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/nio/Buffer rewind ()Ljava/nio/Buffer; 2 12843 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData jdk/internal/ref/CleanerImpl getCleanerImpl (Ljava/lang/ref/Cleaner;)Ljdk/internal/ref/CleanerImpl; 2 10828 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0x40005 0x0 0x0 0x29efd1682a8 0x2a4c 0x0 0x0 0x90004 0x0 0x0 0x29efd165d88 0x2a4c 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xffffffffffffffff oops 2 3 java/lang/ref/Cleaner$1 10 jdk/internal/ref/CleanerImpl methods 0
ciMethodData java/lang/ref/Cleaner$1 apply (Ljava/lang/Object;)Ljava/lang/Object; 2 5122 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 24 0x20004 0x0 0x0 0x29efd4e7c80 0x1402 0x0 0x0 0x50005 0x0 0x0 0x29efd1682a8 0x1402 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 2 3 java/lang/ref/Cleaner 10 java/lang/ref/Cleaner$1 methods 0
ciMethodData java/lang/ref/Cleaner$1 apply (Ljava/lang/ref/Cleaner;)Ljdk/internal/ref/CleanerImpl; 2 5122 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 6 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/invoke/Invokers$Holder linkToTargetMethod (Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 2 9830 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 37 0x10004 0x0 0x0 0x29efe03f558 0x2b0 0x29efe040038 0x353 0x6000b 0x2666 0x0 0x0 0x0 0x0 0x0 0x5 0x1 0x2 0x2 0x2 0x2 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 0xc 0x4 0x0 0x2 0x1 0x2 oops 2 3 java/lang/invoke/DirectMethodHandle$Constructor 5 java/lang/invoke/BoundMethodHandle$Species_L methods 0
ciMethodData java/util/zip/InflaterInputStream ensureOpen ()V 2 7610 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 13 0x40007 0x1dba 0x30 0x0 0xd0002 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/invoke/DirectMethodHandle allocateInstance (Ljava/lang/Object;)Ljava/lang/Object; 2 13073 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0x10004 0x0 0x0 0x29efe03f558 0x1626 0x0 0x0 0xc0005 0x3311 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 3 java/lang/invoke/DirectMethodHandle$Constructor methods 0
ciMethodData java/lang/invoke/DirectMethodHandle constructorMethod (Ljava/lang/Object;)Ljava/lang/Object; 2 14841 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x10004 0x0 0x0 0x29efe03f558 0x1d1a 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 3 java/lang/invoke/DirectMethodHandle$Constructor methods 0
ciMethodData java/io/InputStream <init> ()V 2 6452 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x10002 0x1934 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/ref/PhantomReference <init> (Ljava/lang/Object;Ljava/lang/ref/ReferenceQueue;)V 2 5375 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x30002 0x14ff 0x0 0x0 0x9 0x3 0x6 0x0 0x0 oops 0 methods 0
ciMethodData jdk/internal/ref/PhantomCleanable <init> (Ljava/lang/Object;Ljava/lang/ref/Cleaner;)V 2 5287 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 30 0x20002 0x14a7 0x60002 0x14a7 0xc0002 0x14a7 0x1b0002 0x14a7 0x250005 0x14a7 0x0 0x0 0x0 0x0 0x0 0x290002 0x14a7 0x2d0002 0x14a7 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x3e 0x0 0xffffffffffffffff oops 0 methods 0
ciMethodData jdk/internal/ref/PhantomCleanable insert ()V 2 5287 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 13 0x2c0003 0x14a7 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/Integer toUnsignedLong (I)J 2 4938 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 5 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/ref/Cleaner register (Ljava/lang/Object;Ljava/lang/Runnable;)Ljava/lang/ref/Cleaner$Cleanable; 2 5147 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x30002 0x141b 0xa0002 0x141b 0x150002 0x141b 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0xffffffffffffffff 0x0 0x0 oops 0 methods 0
ciMethodData jdk/internal/ref/CleanerImpl$PhantomCleanableRef <init> (Ljava/lang/Object;Ljava/lang/ref/Cleaner;Ljava/lang/Runnable;)V 2 5147 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 12 0x30002 0x141b 0x0 0x0 0x0 0x0 0x9 0x4 0x3e 0x0 0xffffffffffffffff 0x0 oops 0 methods 0
ciMethodData jdk/internal/ref/CleanerFactory cleaner ()Ljava/lang/ref/Cleaner; 1 452 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 4 0x0 0x0 0x9 0x0 oops 0 methods 0
ciMethodData java/util/ArrayDeque pollFirst ()Ljava/lang/Object; 2 4537 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 24 0xc0002 0x11b9 0x110007 0x934 0x68 0x885 0x170104 0x0 0x0 0x0 0x0 0x0 0x0 0x1c0002 0x885 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x6 oops 0 methods 0
ciMethodData java/util/ArrayDeque elementAt ([Ljava/lang/Object;I)Ljava/lang/Object; 2 4540 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/zip/InflaterInputStream read ([BII)I 2 5269 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 109 0x10005 0x1495 0x0 0x0 0x0 0x0 0x0 0x50007 0x1495 0x30 0x0 0xc0002 0x0 0x140002 0x1495 0x190007 0x1495 0x20 0x0 0x220005 0x0 0x0 0x29efd4d5d08 0x1495 0x0 0x0 0x250007 0x44 0x78 0x1451 0x2c0005 0x0 0x0 0x29efd4d5d08 0x1451 0x0 0x0 0x2f0007 0x1451 0x20 0x0 0x3d0005 0x0 0x0 0x29efd4d5d08 0x1451 0x0 0x0 0x400007 0xc04 0xb0 0x84d 0x470005 0x0 0x0 0x29efd4d5d08 0x84d 0x0 0x0 0x4a0007 0x0 0x58 0x84d 0x4e0005 0x0 0x0 0x29efdddc458 0x84d 0x0 0x0 0x580005 0x0 0x0 0x29efd4d5d08 0x1451 0x0 0x0 0x5e0007 0x0 0xfffffffffffffe30 0x1451 0x680005 0x0 0x0 0x0 0x0 0x0 0x0 0x730007 0x0 0x38 0x0 0x780003 0x0 0x18 0x7d0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0xffffffffffffffff 0xffffffffffffffff 0x0 0x0 oops 6 22 java/util/zip/Inflater 33 java/util/zip/Inflater 44 java/util/zip/Inflater 55 java/util/zip/Inflater 66 org/springframework/boot/loader/jar/NestedJarFile$JarEntryInflaterInputStream 73 java/util/zip/Inflater methods 0
ciMethodData java/util/zip/Inflater inflate ([BII)I 2 6155 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 208 0x70002 0x180b 0x140005 0x180b 0x0 0x0 0x0 0x0 0x0 0x1f0007 0x17d 0xa8 0x168e 0x2d0005 0x0 0x0 0x29efcb69240 0x168e 0x0 0x0 0x400005 0x168e 0x0 0x0 0x0 0x0 0x0 0x450003 0x168e 0x388 0x5a0005 0x17d 0x0 0x0 0x0 0x0 0x0 0x610005 0x17d 0x0 0x0 0x0 0x0 0x0 0x680002 0x17d 0x6f0005 0x0 0x0 0x29efcfcdd38 0x17d 0x0 0x0 0x720007 0x17d 0x1d8 0x0 0x7a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x810004 0x0 0x0 0x0 0x0 0x0 0x0 0x840005 0x0 0x0 0x0 0x0 0x0 0x0 0x900005 0x0 0x0 0x0 0x0 0x0 0x0 0x9e0005 0x0 0x0 0x0 0x0 0x0 0x0 0xa80005 0x0 0x0 0x0 0x0 0x0 0x0 0xad0003 0x0 0x50 0xb70005 0x0 0x0 0x0 0x0 0x0 0x0 0xbf0003 0x0 0xa8 0xc40002 0x17d 0xcb0002 0x17d 0xd50005 0x0 0x0 0x29efcb69240 0x17d 0x0 0x0 0xe40005 0x17d 0x0 0x0 0x0 0x0 0x0 0xe90003 0x17d 0x50 0xf70005 0x0 0x0 0x0 0x0 0x0 0x0 0xfe0003 0x180b 0x18 0x14e0007 0xe5c 0x20 0x9af 0x1590007 0x2f0 0x58 0x151b 0x1600007 0x877 0x38 0xca4 0x1680003 0xca4 0x18 0x1790007 0x180b 0x20 0x0 0x1830007 0x168e 0x90 0x17d 0x1880007 0x17d 0x70 0x0 0x1920005 0x0 0x0 0x0 0x0 0x0 0x0 0x1960003 0x0 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 3 16 java/util/zip/Inflater$InflaterZStreamRef 49 java/nio/HeapByteBuffer 119 java/util/zip/Inflater$InflaterZStreamRef methods 0
ciMethodData java/io/FilterInputStream <init> (Ljava/io/InputStream;)V 2 2230 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 10 0x10002 0x8b6 0x0 0x0 0x0 0x0 0x9 0x2 0x6 0x0 oops 0 methods 0
ciMethodData java/util/Collections$SetFromMap add (Ljava/lang/Object;)Z 2 2891 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 24 0x80005 0x2ff 0x0 0x29efd123868 0x82f 0x29efd123918 0x1f 0xd0007 0x2 0x38 0xb5b 0x110003 0xb5b 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 2 3 java/util/WeakHashMap 5 java/util/IdentityHashMap methods 0
ciMethodData java/util/zip/InflaterInputStream <init> (Ljava/io/InputStream;Ljava/util/zip/Inflater;I)V 2 2084 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 30 0x20002 0x824 0x1c0007 0x0 0x40 0x824 0x200007 0x824 0x30 0x0 0x270002 0x0 0x2c0007 0x824 0x30 0x0 0x350002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x1e 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/invoke/DirectMethodHandle$Holder newInvokeSpecial (Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 2 2418 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 43 0x1000a 0x972 0x3 0x0 0x29efe03f558 0x2 0x6000a 0x972 0x3 0x0 0x29efe03f558 0x29efc1c7358 0x100004 0x0 0x0 0x0 0x0 0x0 0x0 0x13000a 0x972 0x4 0x0 0x2 0x1 0x2 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 0xc 0x4 0x0 0x29efe03f558 0x1 0x2 oops 4 4 java/lang/invoke/DirectMethodHandle$Constructor 10 java/lang/invoke/DirectMethodHandle$Constructor 11 java/lang/invoke/MemberName 40 java/lang/invoke/DirectMethodHandle$Constructor methods 0
ciMethodData java/util/ArrayDeque poll ()Ljava/lang/Object; 2 1962 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x10005 0x0 0x0 0x29efcb63430 0x7aa 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 3 java/util/ArrayDeque methods 0
ciMethodData java/util/zip/Inflater <init> (Z)V 1 9 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x10002 0x9 0x120002 0x9 0x150002 0x9 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xc0 0x0 oops 0 methods 0
ciMethod org/springframework/boot/loader/net/protocol/jar/JarUrlConnection$ConnectionInputStream getDelegateInputStream ()Ljava/io/InputStream; 206 0 2131 0 0
ciMethod org/springframework/boot/loader/net/protocol/jar/LazyDelegatingInputStream read ([BII)I 512 0 5376 0 -1
ciMethod org/springframework/boot/loader/net/protocol/jar/LazyDelegatingInputStream in ()Ljava/io/InputStream; 512 0 5384 0 0
ciMethod org/springframework/boot/loader/net/protocol/jar/LazyDelegatingInputStream getDelegateInputStream ()Ljava/io/InputStream; 0 0 1 0 -1
ciMethod org/springframework/boot/loader/jar/NestedJarFile getNestedJarEntry (Ljava/lang/String;)Lorg/springframework/boot/loader/jar/NestedJarFile$NestedJarEntry; 514 0 4265 0 -1
ciMethod org/springframework/boot/loader/jar/NestedJarFile getInputStream (Ljava/util/zip/ZipEntry;)Ljava/io/InputStream; 780 0 2131 0 0
ciMethod org/springframework/boot/loader/jar/NestedJarFile getInputStream (Lorg/springframework/boot/loader/zip/ZipContent$Entry;)Ljava/io/InputStream; 512 0 2264 0 0
ciMethod org/springframework/boot/loader/jar/NestedJarFile ensureOpen ()V 770 0 5541 0 144
ciMethod org/springframework/boot/loader/ref/Cleaner register (Ljava/lang/Object;Ljava/lang/Runnable;)Ljava/lang/ref/Cleaner$Cleanable; 0 0 1 0 -1
ciMethod org/springframework/boot/loader/ref/DefaultCleaner register (Ljava/lang/Object;Ljava/lang/Runnable;)Ljava/lang/ref/Cleaner$Cleanable; 512 0 4834 0 0
ciMethod org/springframework/boot/loader/jar/NestedJarFile$JarEntryInputStream <init> (Lorg/springframework/boot/loader/jar/NestedJarFile;Lorg/springframework/boot/loader/zip/ZipContent$Entry;)V 512 0 2265 0 0
ciMethod org/springframework/boot/loader/jar/NestedJarFile$JarEntryInputStream getUncompressedSize ()I 256 0 128 0 0
ciMethod org/springframework/boot/loader/jar/NestedJarFile$JarEntryInflaterInputStream <init> (Lorg/springframework/boot/loader/jar/NestedJarFile;Lorg/springframework/boot/loader/jar/NestedJarFile$JarEntryInputStream;Lorg/springframework/boot/loader/jar/NestedJarFileResources;)V 514 0 2264 0 0
ciMethod org/springframework/boot/loader/jar/NestedJarFile$JarEntryInflaterInputStream <init> (Lorg/springframework/boot/loader/jar/NestedJarFile;Lorg/springframework/boot/loader/jar/NestedJarFile$JarEntryInputStream;Lorg/springframework/boot/loader/jar/NestedJarFileResources;Ljava/util/zip/Inflater;)V 768 0 2265 0 0
ciMethod org/springframework/boot/loader/jar/ZipInflaterInputStream <init> (Ljava/io/InputStream;Ljava/util/zip/Inflater;I)V 768 0 2265 0 0
ciMethod org/springframework/boot/loader/jar/ZipInflaterInputStream getInflaterBufferSize (J)I 770 0 2265 0 0
ciMethod org/springframework/boot/loader/jar/ZipInflaterInputStream read ([BII)I 512 0 5471 0 5440
ciMethod org/springframework/boot/loader/jar/NestedJarFile$NestedJarEntry isOwnedBy (Lorg/springframework/boot/loader/jar/NestedJarFile;)Z 780 0 2132 0 0
ciMethod org/springframework/boot/loader/jar/NestedJarFile$NestedJarEntry contentEntry ()Lorg/springframework/boot/loader/zip/ZipContent$Entry; 256 0 128 0 0
ciMethod org/springframework/boot/loader/log/DebugLogger log (Ljava/lang/String;Ljava/lang/Object;)V 0 0 1 0 -1
ciMethod org/springframework/boot/loader/log/DebugLogger log (Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V 0 0 1 0 -1
ciMethod org/springframework/boot/loader/log/DebugLogger log (Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)V 0 0 1 0 -1
ciMethod org/springframework/boot/loader/log/DebugLogger$DisabledDebugLogger log (Ljava/lang/String;Ljava/lang/Object;)V 1024 0 16867 0 80
ciMethod org/springframework/boot/loader/log/DebugLogger$DisabledDebugLogger log (Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V 14 0 5534 0 80
ciMethod org/springframework/boot/loader/log/DebugLogger$DisabledDebugLogger log (Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)V 520 0 5026 0 0
ciMethod org/springframework/boot/loader/jar/NestedJarFileResources zipContent ()Lorg/springframework/boot/loader/zip/ZipContent; 318 0 159 0 0
ciMethod org/springframework/boot/loader/jar/NestedJarFileResources addInputStream (Ljava/io/InputStream;)V 514 0 2264 0 0
ciMethod org/springframework/boot/loader/jar/NestedJarFileResources createInflatorCleanupAction (Ljava/util/zip/Inflater;)Ljava/lang/Runnable; 768 0 2265 0 0
ciMethod org/springframework/boot/loader/jar/NestedJarFileResources getOrCreateInflater ()Ljava/util/zip/Inflater; 768 0 2265 0 0
ciMethod org/springframework/boot/loader/zip/DataBlock read (Ljava/nio/ByteBuffer;J)I 0 0 1 0 -1
ciMethod org/springframework/boot/loader/zip/DataBlock readFully (Ljava/nio/ByteBuffer;J)V 1024 4 11186 0 272
ciMethod org/springframework/boot/loader/zip/FileDataBlock <init> (Lorg/springframework/boot/loader/zip/FileDataBlock$FileAccess;JJ)V 350 0 2398 0 0
ciMethod org/springframework/boot/loader/zip/FileDataBlock read (Ljava/nio/ByteBuffer;J)I 1024 0 16359 0 3232
ciMethod org/springframework/boot/loader/zip/FileDataBlock open ()V 512 0 2674 0 0
ciMethod org/springframework/boot/loader/zip/FileDataBlock ensureOpen (Ljava/util/function/Supplier;)V 1024 0 16359 0 -1
ciMethod org/springframework/boot/loader/zip/FileDataBlock slice (JJ)Lorg/springframework/boot/loader/zip/FileDataBlock; 512 0 2532 0 0
ciMethod org/springframework/boot/loader/zip/FileDataBlock$Tracker openedFileChannel (Ljava/nio/file/Path;)V 0 0 1 0 -1
ciMethod org/springframework/boot/loader/zip/FileDataBlock$FileAccess read (Ljava/nio/ByteBuffer;J)I 1024 0 16359 0 -1
ciMethod org/springframework/boot/loader/zip/FileDataBlock$FileAccess open ()V 512 0 2674 0 0
ciMethod org/springframework/boot/loader/zip/ZipCentralDirectoryFileHeaderRecord compressionMethod ()S 256 0 128 0 0
ciMethod org/springframework/boot/loader/zip/ZipCentralDirectoryFileHeaderRecord compressedSize ()I 256 0 128 0 0
ciMethod org/springframework/boot/loader/zip/ZipCentralDirectoryFileHeaderRecord uncompressedSize ()I 256 0 128 0 0
ciMethod org/springframework/boot/loader/zip/ZipCentralDirectoryFileHeaderRecord offsetToLocalHeader ()I 256 0 128 0 0
ciMethodData org/springframework/boot/loader/zip/FileDataBlock read (Ljava/nio/ByteBuffer;J)I 2 15847 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 90 0x30007 0x3de8 0x30 0x0 0xc0002 0x0 0x11000a 0x3de8 0x1 0x29efd126a30 0x160005 0x0 0x0 0x29efd593928 0x3de8 0x0 0x0 0x250007 0x3de8 0x20 0x0 0x2e0005 0x3de8 0x0 0x0 0x0 0x0 0x0 0x350007 0x3dce 0x100 0x1a 0x390005 0x1a 0x0 0x0 0x0 0x0 0x0 0x3f0005 0x1a 0x0 0x0 0x0 0x0 0x0 0x4f0007 0x1a 0x38 0x0 0x540003 0x0 0x18 0x5a0005 0x0 0x0 0x29efcfcdd38 0x1a 0x0 0x0 0x690005 0x0 0x0 0x29efd7e8678 0x3de8 0x0 0x0 0x710007 0x3dce 0x58 0x1a 0x770005 0x0 0x0 0x29efcfcdd38 0x1a 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 5 9  @bci org/springframework/boot/loader/zip/FileDataBlock read (Ljava/nio/ByteBuffer;J)I 17 <appendix> argL0 ; 13 org/springframework/boot/loader/zip/FileDataBlock 56 java/nio/HeapByteBuffer 63 org/springframework/boot/loader/zip/FileDataBlock$FileAccess 74 java/nio/HeapByteBuffer methods 0
ciMethodData org/springframework/boot/loader/zip/FileDataBlock ensureOpen (Ljava/util/function/Supplier;)V 2 15847 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x50005 0x0 0x0 0x29efd7e8678 0x3de8 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0xffffffffffffffff oops 1 3 org/springframework/boot/loader/zip/FileDataBlock$FileAccess methods 0
ciMethodData org/springframework/boot/loader/zip/FileDataBlock$FileAccess read (Ljava/nio/ByteBuffer;J)I 2 15847 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 69 0xe0007 0x3d 0x40 0x3dab 0x1d0007 0x3d4a 0x58 0x61 0x220005 0x9e 0x0 0x0 0x0 0x0 0x0 0x290007 0x3de8 0x20 0x0 0x450005 0x3de8 0x0 0x0 0x0 0x0 0x0 0x480002 0x3de8 0x4f0005 0x3de8 0x0 0x0 0x0 0x0 0x0 0x5a0005 0x0 0x0 0x29efcfcdd38 0x3de8 0x0 0x0 0x600005 0x3de8 0x0 0x0 0x0 0x0 0x0 0x660005 0x0 0x0 0x29efcfcdd38 0x3de8 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x3c 0xffffffffffffffff 0x0 0x0 oops 2 38 java/nio/HeapByteBuffer 52 java/nio/HeapByteBuffer methods 0
ciMethod org/springframework/boot/loader/zip/ZipContent$Entry getCompressionMethod ()I 512 0 2265 0 0
ciMethod org/springframework/boot/loader/zip/ZipContent$Entry getUncompressedSize ()I 512 0 4393 0 0
ciMethod org/springframework/boot/loader/zip/ZipContent$Entry openContent ()Lorg/springframework/boot/loader/zip/CloseableDataBlock; 514 0 2265 0 0
ciMethod org/springframework/boot/loader/zip/ZipContent$Entry getContent ()Lorg/springframework/boot/loader/zip/FileDataBlock; 768 0 2398 0 0
ciMethod org/springframework/boot/loader/zip/ZipContent$Entry checkNotZip64Extended (J)V 768 0 4796 0 0
ciMethodData org/springframework/boot/loader/zip/DataBlock readFully (Ljava/nio/ByteBuffer;J)V 2 10674 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 36 0x30005 0x0 0x0 0x29efd593928 0x29ce 0x0 0x0 0xc0007 0x29ce 0x30 0x0 0x130002 0x0 0x1e0005 0x29ce 0x0 0x0 0x0 0x0 0x0 0x210007 0x1b 0xffffffffffffff60 0x29b3 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 1 3 org/springframework/boot/loader/zip/FileDataBlock methods 0
ciMethodData org/springframework/boot/loader/log/DebugLogger$DisabledDebugLogger log (Ljava/lang/String;Ljava/lang/Object;)V 2 16355 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 6 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/springframework/boot/loader/log/DebugLogger$DisabledDebugLogger log (Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V 2 5527 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethod org/springframework/boot/loader/zip/ZipLocalFileHeaderRecord <init> (SSSSSIIISS)V 768 0 2398 0 0
ciMethod org/springframework/boot/loader/zip/ZipLocalFileHeaderRecord size ()J 766 0 2398 0 0
ciMethod org/springframework/boot/loader/zip/ZipLocalFileHeaderRecord load (Lorg/springframework/boot/loader/zip/DataBlock;J)Lorg/springframework/boot/loader/zip/ZipLocalFileHeaderRecord; 768 0 2398 0 0
ciMethod org/springframework/boot/loader/zip/ZipLocalFileHeaderRecord fileNameLength ()S 258 0 129 0 0
ciMethod org/springframework/boot/loader/zip/ZipLocalFileHeaderRecord extraFieldLength ()S 258 0 129 0 0
ciMethod  @bci org/springframework/boot/loader/jar/NestedJarFileResources createInflatorCleanupAction (Ljava/util/zip/Inflater;)Ljava/lang/Runnable; 2 <appendix> member <vmtarget> ; <init> (Lorg/springframework/boot/loader/jar/NestedJarFileResources;Ljava/util/zip/Inflater;)V 768 0 2265 0 0
ciMethodData org/springframework/boot/loader/log/DebugLogger$DisabledDebugLogger log (Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)V 2 4766 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x0 0x9 0x5 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/springframework/boot/loader/jar/NestedJarFile ensureOpen ()V 2 5156 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 28 0x40007 0x1424 0x30 0x0 0xe0002 0x0 0x160005 0x0 0x0 0x29efcac76c8 0x1424 0x0 0x0 0x190007 0x1424 0x30 0x0 0x230002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 9 org/springframework/boot/loader/jar/NestedJarFileResources methods 0
ciMethodData org/springframework/boot/loader/zip/FileDataBlock open ()V 2 2418 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x40005 0x0 0x0 0x29efd7e8678 0x972 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 3 org/springframework/boot/loader/zip/FileDataBlock$FileAccess methods 0
ciMethodData org/springframework/boot/loader/zip/FileDataBlock$FileAccess open ()V 2 2418 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 53 0xb0007 0x972 0xe8 0x0 0x170005 0x0 0x0 0x0 0x0 0x0 0x0 0x280004 0x0 0x0 0x0 0x0 0x0 0x0 0x290002 0x0 0x330002 0x0 0x400005 0x0 0x0 0x0 0x0 0x0 0x0 0x5c0002 0x972 0x5f0005 0x96f 0x0 0x29efcfcdc88 0x3 0x0 0x0 0x640003 0x972 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 34 org/springframework/boot/loader/log/DebugLogger$DisabledDebugLogger methods 0
ciMethodData org/springframework/boot/loader/zip/FileDataBlock slice (JJ)Lorg/springframework/boot/loader/zip/FileDataBlock; 2 2276 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 50 0x30007 0x8b0 0x40 0x34 0xc0007 0x0 0x20 0x34 0x140007 0x8b0 0x30 0x0 0x1d0002 0x0 0x240007 0x0 0x40 0x8b0 0x2f0007 0x8b0 0x30 0x0 0x380002 0x0 0x460002 0x8b0 0x4a0002 0x8b0 0x4d0005 0x8af 0x0 0x29efcfcdc88 0x1 0x0 0x0 0x5f0002 0x8b0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0x0 0x0 0x0 0x0 0x0 oops 1 31 org/springframework/boot/loader/log/DebugLogger$DisabledDebugLogger methods 0
ciMethodData org/springframework/boot/loader/zip/FileDataBlock <init> (Lorg/springframework/boot/loader/zip/FileDataBlock$FileAccess;JJ)V 2 2223 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0x10002 0x8af 0x0 0x0 0x0 0x0 0x9 0x6 0x1e 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/springframework/boot/loader/zip/ZipContent$Entry checkNotZip64Extended (J)V 2 4412 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 15 0x50007 0x113c 0x30 0x0 0xe0002 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/springframework/boot/loader/jar/ZipInflaterInputStream read ([BII)I 2 5215 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x40002 0x145f 0xc0007 0x28 0x20 0x1437 0x0 0x0 0x0 0x0 0x9 0x4 0xffffffffffffffff 0xffffffffffffffff 0x0 0x0 oops 0 methods 0
ciMethodData org/springframework/boot/loader/ref/DefaultCleaner register (Ljava/lang/Object;Ljava/lang/Runnable;)Ljava/lang/ref/Cleaner$Cleanable; 2 4578 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 36 0x10007 0x0 0x70 0x11e2 0xa0005 0x11e2 0x0 0x0 0x0 0x0 0x0 0xd0003 0x11e2 0x18 0x150007 0x11e2 0x58 0x0 0x1d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0xffffffffffffffff 0x0 oops 0 methods 0
ciMethodData org/springframework/boot/loader/zip/ZipContent$Entry getUncompressedSize ()I 2 4137 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x40005 0x1029 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/springframework/boot/loader/net/protocol/jar/LazyDelegatingInputStream in ()Ljava/io/InputStream; 2 5128 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 27 0x60007 0xc1d 0x90 0x7eb 0x130007 0x0 0x58 0x7eb 0x170005 0x0 0x0 0x29efdddd030 0x7eb 0x0 0x0 0x220003 0x7eb 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 11 org/springframework/boot/loader/net/protocol/jar/JarUrlConnection$ConnectionInputStream methods 0
ciMethodData org/springframework/boot/loader/net/protocol/jar/JarUrlConnection$ConnectionInputStream getDelegateInputStream ()Ljava/io/InputStream; 2 2028 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0xe0005 0x0 0x0 0x29efe7c23e0 0x7ec 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 3 org/springframework/boot/loader/net/protocol/jar/UrlNestedJarFile methods 0
ciMethodData org/springframework/boot/loader/net/protocol/jar/LazyDelegatingInputStream read ([BII)I 2 5120 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 26 0x10005 0x1400 0x0 0x0 0x0 0x0 0x0 0x70005 0x0 0x0 0x29efdddc458 0x1400 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 1 10 org/springframework/boot/loader/jar/NestedJarFile$JarEntryInflaterInputStream methods 0
ciMethodData org/springframework/boot/loader/zip/ZipContent$Entry getContent ()Lorg/springframework/boot/loader/zip/FileDataBlock; 2 2014 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 61 0x60007 0x0 0x1a0 0x7de 0xd0005 0x7de 0x0 0x0 0x0 0x0 0x0 0x100002 0x7de 0x160005 0x7de 0x0 0x0 0x0 0x0 0x0 0x210002 0x7de 0x2a0005 0x7de 0x0 0x0 0x0 0x0 0x0 0x2d0002 0x7de 0x350005 0x7de 0x0 0x0 0x0 0x0 0x0 0x420005 0x7de 0x0 0x0 0x0 0x0 0x0 0x480005 0x0 0x0 0x29efd593928 0x7de 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x8 oops 1 48 org/springframework/boot/loader/zip/FileDataBlock methods 0
ciMethodData org/springframework/boot/loader/zip/ZipLocalFileHeaderRecord load (Lorg/springframework/boot/loader/zip/DataBlock;J)Lorg/springframework/boot/loader/zip/ZipLocalFileHeaderRecord; 2 2014 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 132 0x60002 0x7de 0x90005 0x7dc 0x0 0x29efcfcdc88 0x2 0x0 0x0 0xe0002 0x7de 0x160005 0x7de 0x0 0x0 0x0 0x0 0x0 0x1d0005 0x0 0x0 0x29efd593928 0x7de 0x0 0x0 0x230005 0x0 0x0 0x29efcfcdd38 0x7de 0x0 0x0 0x280005 0x0 0x0 0x29efcfcdd38 0x7de 0x0 0x0 0x2d0007 0x7de 0x50 0x0 0x35000a 0x0 0x1 0x29efc1c4d08 0x3a0002 0x0 0x430005 0x0 0x0 0x29efcfcdd38 0x7de 0x0 0x0 0x470005 0x0 0x0 0x29efcfcdd38 0x7de 0x0 0x0 0x4b0005 0x0 0x0 0x29efcfcdd38 0x7de 0x0 0x0 0x4f0005 0x0 0x0 0x29efcfcdd38 0x7de 0x0 0x0 0x530005 0x0 0x0 0x29efcfcdd38 0x7de 0x0 0x0 0x570005 0x0 0x0 0x29efcfcdd38 0x7de 0x0 0x0 0x5b0005 0x0 0x0 0x29efcfcdd38 0x7de 0x0 0x0 0x5f0005 0x0 0x0 0x29efcfcdd38 0x7de 0x0 0x0 0x630005 0x0 0x0 0x29efcfcdd38 0x7de 0x0 0x0 0x670005 0x0 0x0 0x29efcfcdd38 0x7de 0x0 0x0 0x6a0002 0x7de 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 15 5 org/springframework/boot/loader/log/DebugLogger$DisabledDebugLogger 21 org/springframework/boot/loader/zip/FileDataBlock 28 java/nio/HeapByteBuffer 35 java/nio/HeapByteBuffer 46 java/lang/String 52 java/nio/HeapByteBuffer 59 java/nio/HeapByteBuffer 66 java/nio/HeapByteBuffer 73 java/nio/HeapByteBuffer 80 java/nio/HeapByteBuffer 87 java/nio/HeapByteBuffer 94 java/nio/HeapByteBuffer 101 java/nio/HeapByteBuffer 108 java/nio/HeapByteBuffer 115 java/nio/HeapByteBuffer methods 0
ciMethodData org/springframework/boot/loader/zip/ZipLocalFileHeaderRecord size ()J 2 2015 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0x30005 0x7df 0x0 0x0 0x0 0x0 0x0 0x80005 0x7df 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/springframework/boot/loader/zip/ZipLocalFileHeaderRecord <init> (SSSSSIIISS)V 2 2014 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 21 0x10002 0x7de 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0xb 0x3e 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/springframework/boot/loader/jar/NestedJarFile getInputStream (Lorg/springframework/boot/loader/zip/ZipContent$Entry;)Ljava/io/InputStream; 2 2009 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 63 0x10005 0x0 0x0 0x29efe7c4d00 0x7d9 0x0 0x0 0x60007 0x0 0x50 0x7d9 0xc0007 0x7d9 0x30 0x0 0x160002 0x0 0x1f0005 0x7d9 0x0 0x0 0x0 0x0 0x0 0x280002 0x7d9 0x300007 0x0 0x68 0x7d8 0x3a0004 0x0 0x0 0x29efcac6a78 0x2 0x0 0x0 0x410002 0x7d8 0x4c0005 0x0 0x0 0x29efcac76c8 0x7d8 0x0 0x0 0x580005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 3 3 org/springframework/boot/loader/zip/ZipContent$Entry 33 org/springframework/boot/loader/jar/NestedJarFile$JarEntryInputStream 42 org/springframework/boot/loader/jar/NestedJarFileResources methods 0
ciMethodData org/springframework/boot/loader/zip/ZipContent$Entry getCompressionMethod ()I 2 2009 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x40005 0x7d9 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/springframework/boot/loader/jar/NestedJarFile$JarEntryInputStream <init> (Lorg/springframework/boot/loader/jar/NestedJarFile;Lorg/springframework/boot/loader/zip/ZipContent$Entry;)V 2 2009 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 27 0x60002 0x7d9 0xb0005 0x0 0x0 0x29efe7c4d00 0x7d9 0x0 0x0 0x130005 0x0 0x0 0x29efe7c4d00 0x7d9 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 2 5 org/springframework/boot/loader/zip/ZipContent$Entry 12 org/springframework/boot/loader/zip/ZipContent$Entry methods 0
ciMethodData org/springframework/boot/loader/zip/ZipContent$Entry openContent ()Lorg/springframework/boot/loader/zip/CloseableDataBlock; 2 2008 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0x10005 0x7d8 0x0 0x0 0x0 0x0 0x0 0x60005 0x0 0x0 0x29efd593928 0x7d8 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x8 oops 1 10 org/springframework/boot/loader/zip/FileDataBlock methods 0
ciMethodData org/springframework/boot/loader/jar/NestedJarFile$JarEntryInflaterInputStream <init> (Lorg/springframework/boot/loader/jar/NestedJarFile;Lorg/springframework/boot/loader/jar/NestedJarFile$JarEntryInputStream;Lorg/springframework/boot/loader/jar/NestedJarFileResources;)V 2 2008 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 21 0x50005 0x0 0x0 0x29efcac76c8 0x7d8 0x0 0x0 0x80002 0x7d8 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 1 3 org/springframework/boot/loader/jar/NestedJarFileResources methods 0
ciMethodData org/springframework/boot/loader/jar/NestedJarFileResources addInputStream (Ljava/io/InputStream;)V 2 2008 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 20 0xc0005 0x0 0x0 0x29efd121b88 0x7d8 0x0 0x0 0x140003 0x7d8 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 1 3 java/util/Collections$SetFromMap methods 0
ciMethodData org/springframework/boot/loader/jar/NestedJarFileResources getOrCreateInflater ()Ljava/util/zip/Inflater; 2 1881 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 36 0x60007 0x0 0xc8 0x759 0x110005 0x0 0x0 0x29efcb63430 0x759 0x0 0x0 0x160104 0x0 0x0 0x29efd4d5d08 0x755 0x0 0x0 0x1b0007 0x4 0x20 0x755 0x240003 0x4 0x18 0x330002 0x4 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 2 7 java/util/ArrayDeque 14 java/util/zip/Inflater methods 0
ciMethodData org/springframework/boot/loader/jar/NestedJarFile$JarEntryInflaterInputStream <init> (Lorg/springframework/boot/loader/jar/NestedJarFile;Lorg/springframework/boot/loader/jar/NestedJarFile$JarEntryInputStream;Lorg/springframework/boot/loader/jar/NestedJarFileResources;Ljava/util/zip/Inflater;)V 2 1881 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 36 0xa0005 0x0 0x0 0x29efcac6a78 0x759 0x0 0x0 0xd0002 0x759 0x190005 0x0 0x0 0x29efcac76c8 0x759 0x0 0x0 0x1c0005 0x0 0x0 0x29efd4eb698 0x759 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0x0 0x0 0x0 0x0 0x0 oops 3 3 org/springframework/boot/loader/jar/NestedJarFile$JarEntryInputStream 12 org/springframework/boot/loader/jar/NestedJarFileResources 19 org/springframework/boot/loader/ref/DefaultCleaner methods 0
ciMethodData org/springframework/boot/loader/jar/ZipInflaterInputStream <init> (Ljava/io/InputStream;Ljava/util/zip/Inflater;I)V 2 1881 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x50002 0x759 0x80002 0x759 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x3e 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/springframework/boot/loader/jar/NestedJarFileResources createInflatorCleanupAction (Ljava/util/zip/Inflater;)Ljava/lang/Runnable; 2 1881 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0x2000a 0x759 0x5 0x0 0x29efcac76c8 0x1 0x29efd4d5d08 0x29efe03f1a0 0x0 0x0 0x9 0x2 0x0 0x0 oops 3 4 org/springframework/boot/loader/jar/NestedJarFileResources 6 java/util/zip/Inflater 7  @bci org/springframework/boot/loader/jar/NestedJarFileResources createInflatorCleanupAction (Ljava/util/zip/Inflater;)Ljava/lang/Runnable; 2 <appendix> member <vmtarget> ; methods 0
ciMethodData  @bci org/springframework/boot/loader/jar/NestedJarFileResources createInflatorCleanupAction (Ljava/util/zip/Inflater;)Ljava/lang/Runnable; 2 <appendix> member <vmtarget> ; <init> (Lorg/springframework/boot/loader/jar/NestedJarFileResources;Ljava/util/zip/Inflater;)V 2 1881 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x10002 0x759 0x0 0x0 0x0 0x0 0x9 0x3 0x6 0x0 0x0 oops 0 methods 0
ciMethodData org/springframework/boot/loader/jar/ZipInflaterInputStream getInflaterBufferSize (J)I 2 1880 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 22 0xb0007 0x756 0x38 0x2 0x110003 0x2 0x18 0x190007 0x758 0x38 0x0 0x1f0003 0x0 0x18 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData org/springframework/boot/loader/jar/NestedJarFile getInputStream (Ljava/util/zip/ZipEntry;)Ljava/io/InputStream; 2 1742 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 83 0x40002 0x6ce 0x90004 0x0 0x0 0x29efe7c4530 0x6ce 0x0 0x0 0xc0007 0x0 0x120 0x6ce 0x100004 0x0 0x0 0x29efe7c4530 0x6ce 0x0 0x0 0x160005 0x0 0x0 0x29efe7c4530 0x6ce 0x0 0x0 0x190007 0x0 0x90 0x6ce 0x1e0005 0x0 0x0 0x29efe7c4530 0x6ce 0x0 0x0 0x210005 0x6ce 0x0 0x0 0x0 0x0 0x0 0x280005 0x0 0x0 0x0 0x0 0x0 0x0 0x2b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x310005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 4 5 org/springframework/boot/loader/jar/NestedJarFile$NestedJarEntry 16 org/springframework/boot/loader/jar/NestedJarFile$NestedJarEntry 23 org/springframework/boot/loader/jar/NestedJarFile$NestedJarEntry 34 org/springframework/boot/loader/jar/NestedJarFile$NestedJarEntry methods 0
ciMethodData org/springframework/boot/loader/jar/NestedJarFile$NestedJarEntry isOwnedBy (Lorg/springframework/boot/loader/jar/NestedJarFile;)Z 2 1742 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 158 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x50007 0x0 0x38 0x6ce 0x90003 0x6ce 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
compile org/springframework/boot/loader/net/protocol/jar/LazyDelegatingInputStream read ([BII)I -1 4 inline 208 0 -1 0 org/springframework/boot/loader/net/protocol/jar/LazyDelegatingInputStream read ([BII)I 1 1 0 org/springframework/boot/loader/net/protocol/jar/LazyDelegatingInputStream in ()Ljava/io/InputStream; 2 23 0 org/springframework/boot/loader/net/protocol/jar/JarUrlConnection$ConnectionInputStream getDelegateInputStream ()Ljava/io/InputStream; 3 14 0 org/springframework/boot/loader/jar/NestedJarFile getInputStream (Ljava/util/zip/ZipEntry;)Ljava/io/InputStream; 4 4 0 java/util/Objects requireNonNull (Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object; 4 22 0 org/springframework/boot/loader/jar/NestedJarFile$NestedJarEntry isOwnedBy (Lorg/springframework/boot/loader/jar/NestedJarFile;)Z 4 30 0 org/springframework/boot/loader/jar/NestedJarFile$NestedJarEntry contentEntry ()Lorg/springframework/boot/loader/zip/ZipContent$Entry; 4 33 0 org/springframework/boot/loader/jar/NestedJarFile getInputStream (Lorg/springframework/boot/loader/zip/ZipContent$Entry;)Ljava/io/InputStream; 5 1 0 org/springframework/boot/loader/zip/ZipContent$Entry getCompressionMethod ()I 6 4 0 org/springframework/boot/loader/zip/ZipCentralDirectoryFileHeaderRecord compressionMethod ()S 5 31 0 org/springframework/boot/loader/jar/NestedJarFile ensureOpen ()V 6 22 0 org/springframework/boot/loader/jar/NestedJarFileResources zipContent ()Lorg/springframework/boot/loader/zip/ZipContent; 5 40 0 org/springframework/boot/loader/jar/NestedJarFile$JarEntryInputStream <init> (Lorg/springframework/boot/loader/jar/NestedJarFile;Lorg/springframework/boot/loader/zip/ZipContent$Entry;)V 6 6 0 java/io/InputStream <init> ()V 7 1 0 java/lang/Object <init> ()V 6 11 0 org/springframework/boot/loader/zip/ZipContent$Entry getUncompressedSize ()I 7 4 0 org/springframework/boot/loader/zip/ZipCentralDirectoryFileHeaderRecord uncompressedSize ()I 6 19 0 org/springframework/boot/loader/zip/ZipContent$Entry openContent ()Lorg/springframework/boot/loader/zip/CloseableDataBlock; 7 1 0 org/springframework/boot/loader/zip/ZipContent$Entry getContent ()Lorg/springframework/boot/loader/zip/FileDataBlock; 8 13 0 org/springframework/boot/loader/zip/ZipCentralDirectoryFileHeaderRecord offsetToLocalHeader ()I 8 16 0 java/lang/Integer toUnsignedLong (I)J 8 22 0 org/springframework/boot/loader/zip/ZipContent$Entry checkNotZip64Extended (J)V 8 33 0 org/springframework/boot/loader/zip/ZipLocalFileHeaderRecord load (Lorg/springframework/boot/loader/zip/DataBlock;J)Lorg/springframework/boot/loader/zip/ZipLocalFileHeaderRecord; 9 6 0 java/lang/Long valueOf (J)Ljava/lang/Long; 9 9 0 org/springframework/boot/loader/log/DebugLogger$DisabledDebugLogger log (Ljava/lang/String;Ljava/lang/Object;)V 9 14 0 java/nio/ByteBuffer allocate (I)Ljava/nio/ByteBuffer; 10 16 0 java/nio/HeapByteBuffer <init> (IILjava/lang/foreign/MemorySegment;)V 11 10 0 java/nio/ByteBuffer <init> (IIII[BILjava/lang/foreign/MemorySegment;)V 12 8 0 java/nio/Buffer <init> (IIIILjava/lang/foreign/MemorySegment;)V 13 1 0 java/lang/Object <init> ()V 13 39 0 java/nio/ByteBuffer limit (I)Ljava/nio/Buffer; 14 2 0 java/nio/ByteBuffer limit (I)Ljava/nio/ByteBuffer; 15 2 0 java/nio/Buffer limit (I)Ljava/nio/Buffer; 13 45 0 java/nio/ByteBuffer position (I)Ljava/nio/Buffer; 14 2 0 java/nio/ByteBuffer position (I)Ljava/nio/ByteBuffer; 15 2 0 java/nio/Buffer position (I)Ljava/nio/Buffer; 12 17 0 java/nio/ByteOrder nativeOrder ()Ljava/nio/ByteOrder; 9 22 0 java/nio/ByteBuffer order (Ljava/nio/ByteOrder;)Ljava/nio/ByteBuffer; 10 21 0 java/nio/ByteOrder nativeOrder ()Ljava/nio/ByteOrder; 9 29 0 org/springframework/boot/loader/zip/DataBlock readFully (Ljava/nio/ByteBuffer;J)V 10 30 0 java/nio/Buffer hasRemaining ()Z 9 35 0 java/nio/ByteBuffer rewind ()Ljava/nio/ByteBuffer; 10 1 0 java/nio/Buffer rewind ()Ljava/nio/Buffer; 9 40 0 java/nio/HeapByteBuffer getInt ()I 10 4 0 java/nio/Buffer session ()Ljdk/internal/foreign/MemorySessionImpl; 10 14 0 java/nio/Buffer nextGetIndex (I)I 10 18 0 java/nio/HeapByteBuffer byteOffset (J)J 10 25 0 jdk/internal/misc/ScopedMemoryAccess getIntUnaligned (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)I 11 6 0 jdk/internal/misc/ScopedMemoryAccess getIntUnalignedInternal (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)I 12 15 0 jdk/internal/misc/Unsafe getIntUnaligned (Ljava/lang/Object;JZ)I 13 8 0 jdk/internal/misc/Unsafe convEndian (ZI)I 12 21 0 java/lang/ref/Reference reachabilityFence (Ljava/lang/Object;)V 9 67 0 java/nio/HeapByteBuffer getShort ()S 10 4 0 java/nio/Buffer session ()Ljdk/internal/foreign/MemorySessionImpl; 10 14 0 java/nio/Buffer nextGetIndex (I)I 10 18 0 java/nio/HeapByteBuffer byteOffset (J)J 10 25 0 jdk/internal/misc/ScopedMemoryAccess getShortUnaligned (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)S 11 6 0 jdk/internal/misc/ScopedMemoryAccess getShortUnalignedInternal (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)S 12 15 0 jdk/internal/misc/Unsafe getShortUnaligned (Ljava/lang/Object;JZ)S 13 8 0 jdk/internal/misc/Unsafe convEndian (ZS)S 12 21 0 java/lang/ref/Reference reachabilityFence (Ljava/lang/Object;)V 9 71 0 java/nio/HeapByteBuffer getShort ()S 10 4 0 java/nio/Buffer session ()Ljdk/internal/foreign/MemorySessionImpl; 10 14 0 java/nio/Buffer nextGetIndex (I)I 10 18 0 java/nio/HeapByteBuffer byteOffset (J)J 10 25 0 jdk/internal/misc/ScopedMemoryAccess getShortUnaligned (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)S 11 6 0 jdk/internal/misc/ScopedMemoryAccess getShortUnalignedInternal (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)S 12 15 0 jdk/internal/misc/Unsafe getShortUnaligned (Ljava/lang/Object;JZ)S 13 8 0 jdk/internal/misc/Unsafe convEndian (ZS)S 12 21 0 java/lang/ref/Reference reachabilityFence (Ljava/lang/Object;)V 9 75 0 java/nio/HeapByteBuffer getShort ()S 10 4 0 java/nio/Buffer session ()Ljdk/internal/foreign/MemorySessionImpl; 10 14 0 java/nio/Buffer nextGetIndex (I)I 10 18 0 java/nio/HeapByteBuffer byteOffset (J)J 10 25 0 jdk/internal/misc/ScopedMemoryAccess getShortUnaligned (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)S 11 6 0 jdk/internal/misc/ScopedMemoryAccess getShortUnalignedInternal (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)S 12 15 0 jdk/internal/misc/Unsafe getShortUnaligned (Ljava/lang/Object;JZ)S 13 8 0 jdk/internal/misc/Unsafe convEndian (ZS)S 12 21 0 java/lang/ref/Reference reachabilityFence (Ljava/lang/Object;)V 9 79 0 java/nio/HeapByteBuffer getShort ()S 10 4 0 java/nio/Buffer session ()Ljdk/internal/foreign/MemorySessionImpl; 10 14 0 java/nio/Buffer nextGetIndex (I)I 10 18 0 java/nio/HeapByteBuffer byteOffset (J)J 10 25 0 jdk/internal/misc/ScopedMemoryAccess getShortUnaligned (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)S 11 6 0 jdk/internal/misc/ScopedMemoryAccess getShortUnalignedInternal (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)S 12 15 0 jdk/internal/misc/Unsafe getShortUnaligned (Ljava/lang/Object;JZ)S 13 8 0 jdk/internal/misc/Unsafe convEndian (ZS)S 12 21 0 java/lang/ref/Reference reachabilityFence (Ljava/lang/Object;)V 9 83 0 java/nio/HeapByteBuffer getShort ()S 10 4 0 java/nio/Buffer session ()Ljdk/internal/foreign/MemorySessionImpl; 10 14 0 java/nio/Buffer nextGetIndex (I)I 10 18 0 java/nio/HeapByteBuffer byteOffset (J)J 10 25 0 jdk/internal/misc/ScopedMemoryAccess getShortUnaligned (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)S 11 6 0 jdk/internal/misc/ScopedMemoryAccess getShortUnalignedInternal (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)S 12 15 0 jdk/internal/misc/Unsafe getShortUnaligned (Ljava/lang/Object;JZ)S 13 8 0 jdk/internal/misc/Unsafe convEndian (ZS)S 12 21 0 java/lang/ref/Reference reachabilityFence (Ljava/lang/Object;)V 9 87 0 java/nio/HeapByteBuffer getInt ()I 10 4 0 java/nio/Buffer session ()Ljdk/internal/foreign/MemorySessionImpl; 10 14 0 java/nio/Buffer nextGetIndex (I)I 10 18 0 java/nio/HeapByteBuffer byteOffset (J)J 10 25 0 jdk/internal/misc/ScopedMemoryAccess getIntUnaligned (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)I 11 6 0 jdk/internal/misc/ScopedMemoryAccess getIntUnalignedInternal (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)I 12 15 0 jdk/internal/misc/Unsafe getIntUnaligned (Ljava/lang/Object;JZ)I 13 8 0 jdk/internal/misc/Unsafe convEndian (ZI)I 12 21 0 java/lang/ref/Reference reachabilityFence (Ljava/lang/Object;)V 9 91 0 java/nio/HeapByteBuffer getInt ()I 10 4 0 java/nio/Buffer session ()Ljdk/internal/foreign/MemorySessionImpl; 10 14 0 java/nio/Buffer nextGetIndex (I)I 10 18 0 java/nio/HeapByteBuffer byteOffset (J)J 10 25 0 jdk/internal/misc/ScopedMemoryAccess getIntUnaligned (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)I 11 6 0 jdk/internal/misc/ScopedMemoryAccess getIntUnalignedInternal (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)I 12 15 0 jdk/internal/misc/Unsafe getIntUnaligned (Ljava/lang/Object;JZ)I 13 8 0 jdk/internal/misc/Unsafe convEndian (ZI)I 12 21 0 java/lang/ref/Reference reachabilityFence (Ljava/lang/Object;)V 9 95 0 java/nio/HeapByteBuffer getInt ()I 10 4 0 java/nio/Buffer session ()Ljdk/internal/foreign/MemorySessionImpl; 10 14 0 java/nio/Buffer nextGetIndex (I)I 10 18 0 java/nio/HeapByteBuffer byteOffset (J)J 10 25 0 jdk/internal/misc/ScopedMemoryAccess getIntUnaligned (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)I 11 6 0 jdk/internal/misc/ScopedMemoryAccess getIntUnalignedInternal (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)I 12 15 0 jdk/internal/misc/Unsafe getIntUnaligned (Ljava/lang/Object;JZ)I 13 8 0 jdk/internal/misc/Unsafe convEndian (ZI)I 12 21 0 java/lang/ref/Reference reachabilityFence (Ljava/lang/Object;)V 9 99 0 java/nio/HeapByteBuffer getShort ()S 10 4 0 java/nio/Buffer session ()Ljdk/internal/foreign/MemorySessionImpl; 10 14 0 java/nio/Buffer nextGetIndex (I)I 10 18 0 java/nio/HeapByteBuffer byteOffset (J)J 10 25 0 jdk/internal/misc/ScopedMemoryAccess getShortUnaligned (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)S 11 6 0 jdk/internal/misc/ScopedMemoryAccess getShortUnalignedInternal (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)S 12 15 0 jdk/internal/misc/Unsafe getShortUnaligned (Ljava/lang/Object;JZ)S 13 8 0 jdk/internal/misc/Unsafe convEndian (ZS)S 12 21 0 java/lang/ref/Reference reachabilityFence (Ljava/lang/Object;)V 9 103 0 java/nio/HeapByteBuffer getShort ()S 10 4 0 java/nio/Buffer session ()Ljdk/internal/foreign/MemorySessionImpl; 10 14 0 java/nio/Buffer nextGetIndex (I)I 10 18 0 java/nio/HeapByteBuffer byteOffset (J)J 10 25 0 jdk/internal/misc/ScopedMemoryAccess getShortUnaligned (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)S 11 6 0 jdk/internal/misc/ScopedMemoryAccess getShortUnalignedInternal (Ljdk/internal/foreign/MemorySessionImpl;Ljava/lang/Object;JZ)S 12 15 0 jdk/internal/misc/Unsafe getShortUnaligned (Ljava/lang/Object;JZ)S 13 8 0 jdk/internal/misc/Unsafe convEndian (ZS)S 12 21 0 java/lang/ref/Reference reachabilityFence (Ljava/lang/Object;)V 9 106 0 org/springframework/boot/loader/zip/ZipLocalFileHeaderRecord <init> (SSSSSIIISS)V 10 1 0 java/lang/Record <init> ()V 11 1 0 java/lang/Object <init> ()V 8 42 0 org/springframework/boot/loader/zip/ZipCentralDirectoryFileHeaderRecord compressedSize ()I 8 45 0 java/lang/Integer toUnsignedLong (I)J 8 53 0 org/springframework/boot/loader/zip/ZipContent$Entry checkNotZip64Extended (J)V 8 66 0 org/springframework/boot/loader/zip/ZipLocalFileHeaderRecord size ()J 9 3 0 org/springframework/boot/loader/zip/ZipLocalFileHeaderRecord fileNameLength ()S 9 8 0 org/springframework/boot/loader/zip/ZipLocalFileHeaderRecord extraFieldLength ()S 8 72 0 org/springframework/boot/loader/zip/FileDataBlock slice (JJ)Lorg/springframework/boot/loader/zip/FileDataBlock; 9 70 0 java/lang/Long valueOf (J)Ljava/lang/Long; 9 74 0 java/lang/Long valueOf (J)Ljava/lang/Long; 9 77 0 org/springframework/boot/loader/log/DebugLogger$DisabledDebugLogger log (Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)V 9 95 0 org/springframework/boot/loader/zip/FileDataBlock <init> (Lorg/springframework/boot/loader/zip/FileDataBlock$FileAccess;JJ)V 10 1 0 java/lang/Object <init> ()V 7 6 0 org/springframework/boot/loader/zip/FileDataBlock open ()V 8 4 0 org/springframework/boot/loader/zip/FileDataBlock$FileAccess open ()V 9 92 0 java/lang/Integer valueOf (I)Ljava/lang/Integer; 9 95 0 org/springframework/boot/loader/log/DebugLogger$DisabledDebugLogger log (Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V 5 65 0 org/springframework/boot/loader/jar/NestedJarFile$JarEntryInflaterInputStream <init> (Lorg/springframework/boot/loader/jar/NestedJarFile;Lorg/springframework/boot/loader/jar/NestedJarFile$JarEntryInputStream;Lorg/springframework/boot/loader/jar/NestedJarFileResources;)V 6 5 0 org/springframework/boot/loader/jar/NestedJarFileResources getOrCreateInflater ()Ljava/util/zip/Inflater; 7 17 0 java/util/ArrayDeque poll ()Ljava/lang/Object; 8 1 0 java/util/ArrayDeque pollFirst ()Ljava/lang/Object; 9 12 0 java/util/ArrayDeque elementAt ([Ljava/lang/Object;I)Ljava/lang/Object; 9 28 0 java/util/ArrayDeque inc (II)I 7 51 0 java/util/zip/Inflater <init> (Z)V 8 1 0 java/lang/Object <init> ()V 8 21 0 java/util/zip/Inflater$InflaterZStreamRef <init> (Ljava/util/zip/Inflater;J)V 9 1 0 java/lang/Object <init> ()V 9 9 0 jdk/internal/ref/CleanerFactory cleaner ()Ljava/lang/ref/Cleaner; 6 8 0 org/springframework/boot/loader/jar/NestedJarFile$JarEntryInflaterInputStream <init> (Lorg/springframework/boot/loader/jar/NestedJarFile;Lorg/springframework/boot/loader/jar/NestedJarFile$JarEntryInputStream;Lorg/springframework/boot/loader/jar/NestedJarFileResources;Ljava/util/zip/Inflater;)V 7 10 0 org/springframework/boot/loader/jar/NestedJarFile$JarEntryInputStream getUncompressedSize ()I 7 13 0 org/springframework/boot/loader/jar/ZipInflaterInputStream <init> (Ljava/io/InputStream;Ljava/util/zip/Inflater;I)V 8 5 0 org/springframework/boot/loader/jar/ZipInflaterInputStream getInflaterBufferSize (J)I 8 8 0 java/util/zip/InflaterInputStream <init> (Ljava/io/InputStream;Ljava/util/zip/Inflater;I)V 9 2 0 java/io/FilterInputStream <init> (Ljava/io/InputStream;)V 10 1 0 java/io/InputStream <init> ()V 11 1 0 java/lang/Object <init> ()V 7 25 0 org/springframework/boot/loader/jar/NestedJarFileResources createInflatorCleanupAction (Ljava/util/zip/Inflater;)Ljava/lang/Runnable; 8 2 0 java/lang/invoke/Invokers$Holder linkToTargetMethod (Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 9 6 0 java/lang/invoke/DirectMethodHandle$Holder newInvokeSpecial (Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 10 1 0 java/lang/invoke/DirectMethodHandle allocateInstance (Ljava/lang/Object;)Ljava/lang/Object; 10 6 0 java/lang/invoke/DirectMethodHandle constructorMethod (Ljava/lang/Object;)Ljava/lang/Object; 10 19 0  @bci org/springframework/boot/loader/jar/NestedJarFileResources createInflatorCleanupAction (Ljava/util/zip/Inflater;)Ljava/lang/Runnable; 2 <appendix> member <vmtarget> ; <init> (Lorg/springframework/boot/loader/jar/NestedJarFileResources;Ljava/util/zip/Inflater;)V 11 1 0 java/lang/Object <init> ()V 7 28 0 org/springframework/boot/loader/ref/DefaultCleaner register (Ljava/lang/Object;Ljava/lang/Runnable;)Ljava/lang/ref/Cleaner$Cleanable; 8 10 0 java/lang/ref/Cleaner register (Ljava/lang/Object;Ljava/lang/Runnable;)Ljava/lang/ref/Cleaner$Cleanable; 9 3 0 java/util/Objects requireNonNull (Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object; 9 10 0 java/util/Objects requireNonNull (Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object; 9 21 0 jdk/internal/ref/CleanerImpl$PhantomCleanableRef <init> (Ljava/lang/Object;Ljava/lang/ref/Cleaner;Ljava/lang/Runnable;)V 10 3 0 jdk/internal/ref/PhantomCleanable <init> (Ljava/lang/Object;Ljava/lang/ref/Cleaner;)V 11 2 0 java/util/Objects requireNonNull (Ljava/lang/Object;)Ljava/lang/Object; 11 6 0 jdk/internal/ref/CleanerImpl getCleanerImpl (Ljava/lang/ref/Cleaner;)Ljdk/internal/ref/CleanerImpl; 12 4 0 java/lang/ref/Cleaner$1 apply (Ljava/lang/Object;)Ljava/lang/Object; 13 5 0 java/lang/ref/Cleaner$1 apply (Ljava/lang/ref/Cleaner;)Ljdk/internal/ref/CleanerImpl; 11 12 0 java/lang/ref/PhantomReference <init> (Ljava/lang/Object;Ljava/lang/ref/ReferenceQueue;)V 12 3 0 java/lang/ref/Reference <init> (Ljava/lang/Object;Ljava/lang/ref/ReferenceQueue;)V 13 1 0 java/lang/Object <init> ()V 11 27 0 jdk/internal/ref/CleanerImpl getCleanerImpl (Ljava/lang/ref/Cleaner;)Ljdk/internal/ref/CleanerImpl; 12 4 0 java/lang/ref/Cleaner$1 apply (Ljava/lang/Object;)Ljava/lang/Object; 13 5 0 java/lang/ref/Cleaner$1 apply (Ljava/lang/ref/Cleaner;)Ljdk/internal/ref/CleanerImpl; 11 37 0 jdk/internal/ref/PhantomCleanable insert ()V 11 41 0 java/lang/ref/Reference reachabilityFence (Ljava/lang/Object;)V 11 45 0 java/lang/ref/Reference reachabilityFence (Ljava/lang/Object;)V 5 76 0 org/springframework/boot/loader/jar/NestedJarFileResources addInputStream (Ljava/io/InputStream;)V 6 12 0 java/util/Collections$SetFromMap add (Ljava/lang/Object;)Z
